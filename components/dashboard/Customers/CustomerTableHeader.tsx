import React from 'react';
const columns = [
	{ id: 'name', label: 'Customer', sortable: true },
	// { id: 'customerId', label: 'ID', sortable: true },
	{ id: 'joinDate', label: 'Join Date', sortable: true },
	{ id: 'email', label: 'Email', sortable: false },
	{ id: 'role', label: 'ROLE', sortable: false },
	// { id: 'totalOrders', label: 'Orders', sortable: true },
	// { id: 'totalSpent', label: 'Total Spent', sortable: true },
	// { id: 'status', label: 'Status', sortable: false },
];

const CustomerTableHeader = () => {
	return (
		<thead className='bg-gray-50'>
			<tr>
				{/* <th
					scope='col'
					className='px-4 py-3 text-left'>
					<CheckBox
						checked={isAllSelected}
						onChange={(e: React.ChangeEvent<HTMLInputElement>) =>
							onSelectAll(e.target.checked)
						}
						className='h-4 w-4'
					/>
				</th> */}
				{columns.map((column) => (
					<th
						key={column.id}
						scope='col'
						className='px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider'>
						<div className='flex items-center'>
							{column.label}
							{/* {column.sortable && (
                <ArrowUpDown className="ml-1 h-4 w-4 text-gray-400" />
              )} */}
						</div>
					</th>
				))}
				<th
					scope='col'
					className='relative px-4 py-3'>
					<span className='sr-only'>Actions</span>
				</th>
			</tr>
		</thead>
	);
};

export default CustomerTableHeader;
