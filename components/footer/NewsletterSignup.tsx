import React from "react";
import Image from "next/image";
import { Icon } from "@/assets/images";
import { shortSiteName, siteName } from "@/utils/variables";

export const NewsletterSignup: React.FC = () => {
  return (
    <div>
      <div className="flex justify-center items-center">
        <Image
          src={Icon}
          alt={`${siteName} Logo`}
          width={80}
          height={80}
          className="mb-2"
        />
      </div>
      <p className="text-gray-700 mb-4">
        Get the latest updates about {shortSiteName} new features and product
        updates.
      </p>
      <div className="flex flex-col sm:flex-row">
        <input
          type="email"
          placeholder="Email address"
          className="border border-gray-300 rounded-md px-4 py-2 mb-2 sm:mb-0 sm:mr-2 focus:outline-none focus:ring-2 focus:ring-blue-500"
        />
        <button className="bg-[#403C6F] text-white rounded-md px-6 py-2 hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-indigo-500">
          Subscribe
        </button>
      </div>
    </div>
  );
};
