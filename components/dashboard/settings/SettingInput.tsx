import { postData } from "@/api";
import InputField from "@/components/general/InputField";
import Spinner from "@/components/general/Spinner";
import { constructErrorMessage } from "@/utils/functions";
import { Check, Pencil } from "lucide-react";
import { useCallback, useEffect, useRef, useState } from "react";
import { toast } from "react-toastify";

export interface ConfigDetails<T = unknown> {
  description?: string;
  value: string;
  key: string;
  metaData?: T;
}

export interface SettingsInputType<T = unknown> {
  settingKey: string;
  label: string;
  configList?: ConfigDetails[];
  apiUrl: string;
  refetch: () => void;
  onValueChange: (previousValue: string, newValue: string) => string;
  onValidateInput: (data: string) => T;
}

const SettingsInput: React.FC<SettingsInputType> = ({
  label,
  settingKey,
  configList = [],
  apiUrl = "",
  refetch = () => {},
  onValueChange = (previousValue) => previousValue,
  onValidateInput = () => undefined
}) => {
  const [value, setValue] = useState("");
  const [allowEdit, setAllowEdit] = useState(false);
  const [updatingSettings, setUpdatingSettings] = useState(false);
  const [updatingError, setUpdatingError] = useState<string | null>(null);
  const inputRef = useRef<HTMLInputElement>(null);
  const configDetails = configList.find((config) => config.key === settingKey);

  const updateConfig = useCallback(async () => {
    if (!apiUrl) {
      return toast("URL not found!");
    }
    const dataToSend = onValidateInput(value);

    if (!dataToSend) {
      return toast("No data to send");
    }
    setUpdatingSettings(true);
    try {
      await postData(apiUrl, dataToSend);
      refetch();
      setAllowEdit(false);
    } catch (error) {
      const errorList =
        (error as ApiErrorResponseType)?.response?.data?.errors || {};
      const errorText = Object.values(errorList)?.[0];
      setUpdatingError(errorText || null);
      toast(
        constructErrorMessage(
          error as ApiErrorResponseType,
          `Error encountered saving ${label}`
        )
      );
    } finally {
      setUpdatingSettings(false);
    }
  }, [value, label, onValidateInput, refetch, apiUrl]);

  const focusOnInput = useCallback(() => {
    const inputElement = inputRef?.current;

    if (inputElement) {
      inputElement?.focus();
    }
  }, []);

  useEffect(() => {
    setValue(configDetails?.value?.toString() || "");
  }, [configDetails]);

  useEffect(() => {
    if (allowEdit) {
      focusOnInput();
    }
  }, [allowEdit, focusOnInput]);

  return (
    <div className="">
      <InputField
        ref={inputRef}
        labelClassName="text-sm font-semibold"
        label={label}
        value={value}
        disabled={!allowEdit}
        onChange={(e) => {
          const newValue = (e?.target as HTMLInputElement)?.value;
          setValue(onValueChange(value, newValue));
        }}
        error={updatingError || undefined}
        rightIconAction={() => {
          if (!allowEdit) {
            return setAllowEdit(true);
          }

          if (value === configDetails?.value) {
            return setAllowEdit(false);
          }

          updateConfig();
        }}
        rightButtonClassName="border-l absolute -translate-y-1/2 top-1/2 h-full px-3 "
        rightIcon={
          <span className={`${allowEdit ? "text-green-600" : "opacity-60"}`}>
            {updatingSettings && <Spinner className="!border size-4" />}
            {!updatingSettings && (
              <>
                {!allowEdit && <Pencil size={14} />}
                {allowEdit && <Check />}
              </>
            )}
          </span>
        }
      />
    </div>
  );
};

export default SettingsInput;
