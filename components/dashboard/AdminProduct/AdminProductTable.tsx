import React, { useCallback, useEffect, useState } from "react";
import AdminProductTableHeader from "./AdminProductTableHeader";
import AdminProductTableRow from "./AdminProductTableRow";
import { ChevronLeft, ChevronRight, Loader2 } from "lucide-react";
import Button from "@/components/Button";
import ErrorContainer from "@/components/status/ErrorContainer";
import { getData } from "@/api";
import { useQuery } from "@tanstack/react-query";
import { ProductDetailsType } from "@/components/product/ProductCard";
import { constructErrorMessage } from "@/utils/functions";
import { useRouter } from "next/router";
import { usePathname } from "next/navigation";

const AdminProductTable = () => {
  const [productList, setProductList] = useState<ProductDetailsType[] | null>(
    null
  );
  const [totalPage, setTotalPage] = useState(0);
  const pathname = usePathname();
  const { query, push } = useRouter();
  const { page: queryPage, q } = query;
  const page = parseInt(queryPage?.toString() || "1");
  // Number of columns: Checkbox + Product + Price + Category + Stock + Actions = 6
  const numberOfColumns = 6;
  const queryFn = useCallback(
    async (page: number) => {
      setProductList(null);
      const urlSearchParams = new URLSearchParams();
      urlSearchParams.set("page", page?.toString());
      if (q) {
        urlSearchParams.set("q", q?.toString());
      }
      return await getData<ApiCallResponseType<ProductDetailsType[]>>(
        `/products?${urlSearchParams?.toString()}`
      );
    },
    [q]
  );

  const { data, error, refetch, isLoading } = useQuery({
    queryKey: ["admin-products", page, q],
    queryFn: () => queryFn(page)
  });

  const handlePageChange = useCallback(
    (page: number) => {
      push({ pathname, query: { ...query, page } });
    },
    [query, push, pathname]
  );

  useEffect(() => {
    if (data) {
      setProductList(data?.data?.data);
      setTotalPage(data?.data?.pagination?.totalPage);
    }
  }, [page, data, totalPage]);

  return (
    <div className="overflow-x-auto">
      <div className="align-middle inline-block min-w-full">
        <div className="shadowborder-b border-gray-200 sm:rounded-lg">
          <table className="min-w-full divide-y divide-gray-200">
            <AdminProductTableHeader />
            <tbody className="bg-white divide-y divide-gray-200">
              {!productList && (
                <tr>
                  <td
                    colSpan={numberOfColumns}
                    className="px-4 py-10 text-center text-sm text-gray-500"
                  >
                    <div className="flex justify-center items-center">
                      <Loader2 className="h-6 w-6 animate-spin mr-2" />
                      Loading products...
                    </div>
                  </td>
                </tr>
              )}
              {error && !productList && (
                <tr>
                  <td
                    colSpan={numberOfColumns}
                    className="px-4 py-10 text-center text-sm"
                  >
                    <ErrorContainer
                      error={constructErrorMessage(
                        error as ApiErrorResponseType,
                        "Error fetching product"
                      )}
                      retryFunction={refetch}
                    />
                  </td>
                </tr>
              )}
              {productList &&
                productList.length > 0 &&
                productList.map((product) => (
                  <AdminProductTableRow
                    key={product.id}
                    product={product}
                    refetch={refetch}
                  />
                ))}
              {productList && productList.length < 1 && (
                <tr>
                  <td
                    colSpan={numberOfColumns}
                    className="px-4 py-10 text-center text-sm text-gray-500"
                  >
                    No products found.
                  </td>
                </tr>
              )}
            </tbody>
          </table>
        </div>

        {/* Pagination */}
        {!error && (
          <div className="flex justify-center items-center mt-4 space-x-2">
            <Button
              variant="primary"
              size="small"
              loading={isLoading}
              onClick={() => {
                handlePageChange(page - 1);
              }}
              disabled={page <= 1}
            >
              <ChevronLeft className="h-4 w-4" />
            </Button>
            <span className="text-sm text-gray-700">
              Page {page} of {totalPage}
            </span>
            <Button
              variant="primary"
              size="small"
              loading={isLoading}
              onClick={() => {
                handlePageChange(page + 1);
              }}
              disabled={page >= totalPage}
            >
              <ChevronRight className="h-4 w-4" />
            </Button>
          </div>
        )}
      </div>
    </div>
  );
};

export default AdminProductTable;
