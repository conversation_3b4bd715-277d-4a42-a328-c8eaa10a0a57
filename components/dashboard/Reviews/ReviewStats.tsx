import { getData } from "@/api";
import ErrorContainer from "@/components/status/ErrorContainer";
import { constructErrorMessage } from "@/utils/functions";
import { useQuery } from "@tanstack/react-query";
import React, { useCallback, useEffect, useState } from "react";
import { FaStar } from "react-icons/fa";

export type ReviewStatDetails = {
  totalReviews: number;
  totalApprovedReviews: number;
  totalDisapprovedReviews: number;
  totalNewReviews: number;
  averageRating: number;
};

interface StatCardProps {
  title: string;
  value: number | string;
  icon?: React.ReactNode;
}

const StatCard: React.FC<StatCardProps> = ({ title, value, icon }) => (
  <div className="bg-white p-4 rounded-lg border border-gray-200">
    <p className="text-sm text-gray-500 mb-1">{title}</p>
    <div className="flex items-center">
      {icon && <span className="mr-2">{icon}</span>}
      <p className="text-2xl font-semibold text-gray-900">{value}</p>
    </div>
  </div>
);

const ReviewStats = () => {
  const [stats, setStats] = useState<ReviewStatDetails | null>(null);
  const queryFn = useCallback(async () => {
    return await getData<ApiCallResponseType<ReviewStatDetails>>(
      "/reviews/stat"
    );
  }, []);
  const { data, error, refetch } = useQuery({
    queryFn,
    queryKey: ["reviews-stat"]
  });
  useEffect(() => {
    if (data) {
      setStats(data?.data?.data);
    }
  }, [data]);
  return (
    <>
      {!stats && error && (
        <ErrorContainer
          error={constructErrorMessage(
            error as ApiErrorResponseType,
            "Unable to fetch stats! Please try again"
          )}
          retryFunction={refetch}
        />
      )}

      {((!error && !stats) || stats) && (
        <div className="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-5 gap-4 mb-6">
          <StatCard
            title="Total Reviews"
            value={stats ? stats?.totalReviews : "..."}
          />
          <StatCard
            title="Approved Reviews"
            value={stats ? stats?.totalApprovedReviews : "..."}
          />
          <StatCard
            title="New Reviews"
            value={stats ? stats?.totalNewReviews : "..."}
          />
          <StatCard
            title="Rejected Reviews"
            value={stats ? stats?.totalDisapprovedReviews : "..."}
          />
          <StatCard
            title="Average Rating"
            value={stats ? stats?.averageRating?.toFixed(1) : "..."}
            icon={<FaStar className="text-yellow-400" />}
          />
        </div>
      )}
    </>
  );
};

export default ReviewStats;
