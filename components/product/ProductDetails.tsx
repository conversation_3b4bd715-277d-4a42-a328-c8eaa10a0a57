import React, { useCallback, useEffect, useRef, useState } from "react";
import SectionContainer from "../layouts/SectionContainer";
import StarList from "./StarList";
import Button from "../Button";
import { Minus, Plus } from "lucide-react";
import InputField from "../general/InputField";
import { toast } from "react-toastify";
import { ProductDetailsType } from "./ProductCard";
import CustomImage from "../general/CustomImage";
import { siteName } from "@/utils/variables";
import { deleteData, postData } from "@/api";
import { constructErrorMessage } from "@/utils/functions";
import { Swiper, SwiperRef, SwiperSlide } from "swiper/react";
import useCart from "@/hooks/useCart";
import { CartDetailsType } from "@/store/useCartStore";
import { BsHeart, BsHeartFill } from "react-icons/bs";
import useUser from "@/hooks/useUser";

const cartQuantityClassName =
  "disabled:opacity-40 cursor-pointer disabled:cursor-not-allowed";

const ProductDetails: React.FC<ProductDetailsType> = ({
  quantity: productQuantity = 0,
  images = [],
  name,
  id,
  description,
  rating,
  price,
  discount,
  isFavorite
}) => {
  const { userToken } = useUser();
  const [isAddedToFavorite, setIsAddedToFavorite] = useState(false);
  const [isAddingToFavorite, setIsAddingToFavorite] = useState(false);
  const {
    setShouldOpenCart,
    updateCart,
    constructCartDetails,
    updateLocalStorageCart
  } = useCart();
  const [activeSlide, setActiveSlide] = useState(0);
  const [quantity, setQuantity] = useState(1);
  const [isAddingToCart, setIsAddingToCart] = useState(false);
  const ref = useRef<SwiperRef | null>(null);
  const slideToElement = useCallback((index: number) => {
    const element = ref?.current;
    if (!element) {
      return;
    }
    element?.swiper?.slideTo(index);
  }, []);
  const addToCart = useCallback(async () => {
    if (!quantity || !id) {
      return;
    }
    setIsAddingToCart(true);
    if (!userToken) {
      const cartDetails = constructCartDetails(
        { name, price, quantity: productQuantity, id, images },
        quantity
      );
      updateLocalStorageCart(cartDetails);
      toast(`${quantity} ${name} added to cart successfully`, {
        autoClose: 500
      });
      return setIsAddingToCart(false);
    }
    try {
      const { data } = await postData<
        { quantity: number },
        ApiCallResponseType<CartDetailsType>
      >(`/cart/${id}`, {
        quantity
      });
      updateCart(data?.data);
      setShouldOpenCart(true);
      toast(`${quantity} ${name} added to cart successfully`);
    } catch (error) {
      toast(
        constructErrorMessage(
          error as ApiErrorResponseType,
          "Unable to add to cart! Please try again later"
        )
      );
    } finally {
      setIsAddingToCart(false);
    }
  }, [
    quantity,
    id,
    setShouldOpenCart,
    updateCart,
    name,
    constructCartDetails,
    images,
    price,
    userToken,
    updateLocalStorageCart,
    productQuantity
  ]);

  const toggleFavorite = useCallback(async () => {
    setIsAddingToFavorite(true);
    try {
      if (isAddedToFavorite) {
        await deleteData(`/favorites/${id}`);
      }
      if (!isAddedToFavorite) {
        await postData(`/favorites/${id}`);
      }

      setIsAddedToFavorite((prevState) => !prevState);
    } catch (error) {
      toast(
        constructErrorMessage(
          error as ApiErrorResponseType,
          "Unable to process request!"
        )
      );
    } finally {
      setIsAddingToFavorite(false);
    }
  }, [isAddedToFavorite, id]);
  useEffect(() => {
    setIsAddedToFavorite(isFavorite);
  }, [isFavorite]);
  return (
    <SectionContainer
      contentContainerClassName="grid grid-cols-1 md:grid-cols-2 gap-10"
      className="pt-10"
    >
      <div className="flex flex-col gap-4">
        <div className="border min-h-[35rem] border-slate-200 bg-[#D0D0D0]">
          <Swiper
            ref={ref}
            spaceBetween={50}
            slidesPerView={1}
            onSlideChange={(swiper) => {
              setActiveSlide(swiper?.activeIndex);
            }}
            className="h-full w-full"
          >
            {images.map((url, index) => (
              <SwiperSlide key={index} className="relative overflow-hidden">
                <CustomImage
                  alt={`${name} - ${siteName}`}
                  src={url}
                  fill
                  className="object-contain object-center"
                />
              </SwiperSlide>
            ))}
          </Swiper>
        </div>
        <div className="flex items-center gap-4 flex-wrap">
          {images.map((url, index) => {
            const isActive = index === activeSlide;
            return (
              <button
                title={"View image"}
                onClick={() => {
                  slideToElement(index);
                }}
                aria-label={"View image"}
                className={`size-14 bg-[#D0D0D0] rounded-md overflow-hidden relative inline-block  ${
                  isActive
                    ? "border-2 border-primary"
                    : "border border-slate-200"
                }`}
                key={index}
              >
                <CustomImage
                  src={url}
                  alt={`${name} - ${siteName}`}
                  key={index}
                  fill
                  className="object-contain object-center"
                />
              </button>
            );
          })}
        </div>
      </div>
      <div className="flex flex-col gap-10">
        <div className="flex items-center gap-6 justify-between">
          <h1 className="font-bold text-[clamp(1.1rem,1.35vw,1.35rem)]">
            {name}
          </h1>
          {userToken && (
            <button
              disabled={isAddingToFavorite}
              onClick={toggleFavorite}
              title={`${isAddedToFavorite ? "Remove" : "Add"} to favorite`}
              aria-label={`${isAddedToFavorite ? "Remove" : "Add"} to favorite`}
              className={`disabled:opacity-40 ${
                isFavorite ? "text-red-600" : ""
              }`}
            >
              {isAddedToFavorite && <BsHeartFill />}
              {!isAddedToFavorite && <BsHeart />}
            </button>
          )}
        </div>

        <div className="flex flex-col gap-4">
          <p className="opacity-60">{description}</p>
          <StarList rating={rating} />
        </div>

        <div className="flex flex-col gap-2 border-b pb-5">
          <div>
            {discount && (
              <p className="font-bold">
                {discount?.formatted?.withCurrency || 0}
              </p>
            )}
            <h1
              className={` ${
                discount
                  ? "text-sm line-through opacity-50 font-medium"
                  : "font-bold"
              }`}
            >
              {price?.formatted?.withCurrency}
            </h1>
          </div>
          <p className="opacity-60">
            Suggested payments with 6 months special financing
          </p>
        </div>

        <div className="flex flex-col gap-4">
          <h1 className="font-semibold">Quantity</h1>
          <div className="flex items-center gap-6">
            <div className="p-2 px-4 bg-slate-100 rounded-full inline-flex items-center gap-4 text-sm">
              <button
                title="Subtract 1"
                aria-label="Subtract 1"
                disabled={quantity < 2 || isAddingToCart || !id}
                className={cartQuantityClassName}
                onClick={() => {
                  setQuantity((prevState) => {
                    const quantityToSave = prevState - 1;

                    if (quantityToSave > 0) {
                      return quantityToSave;
                    }
                    return prevState;
                  });
                }}
              >
                <Minus size={13} />
              </button>
              <InputField
                placeholder=" "
                disabled={isAddingToCart || !id}
                value={quantity?.toString()}
                onChange={(e) => {
                  const value = (e?.target as HTMLInputElement)?.value;
                  const valueAsANumber = Number(value || "0");

                  if (isNaN(valueAsANumber)) {
                    return;
                  }

                  if (valueAsANumber > productQuantity) {
                    toast("You cannot add more than the available quantity");
                    setQuantity(quantity);
                    return;
                  }

                  setQuantity(valueAsANumber);
                }}
                inputClassName="!w-[2rem] bg-transparent border-none !py-1 text-center !px-0 font-medium disabled:opacity-30"
              />
              <button
                disabled={
                  !(quantity < productQuantity) || isAddingToCart || !id
                }
                title="Add 1 more"
                arial-label={"Add 1 more"}
                className={cartQuantityClassName}
                onClick={() => {
                  setQuantity((prevState) => {
                    const quantityToSave = prevState + 1;

                    if (quantityToSave < productQuantity) {
                      return quantityToSave;
                    }
                    return quantity;
                  });
                }}
              >
                <Plus size={13} />
              </button>
            </div>
            <div className="text-sm">
              <p className="">
                <span className="opacity-60">Only </span>
                <span className="font-semibold text-secondary">
                  {productQuantity} Items
                </span>
                <span className="opacity-60">left</span>
              </p>
              <p className="opacity-60">Don&apos;t miss it</p>
            </div>
          </div>
          <div className="flex items-center gap-4">
            <Button
              className="border-secondary border text-secondary !rounded-full !px-8"
              variant="transparent"
            >
              Buy now
            </Button>
            <Button
              onClick={addToCart}
              loading={isAddingToCart}
              disabled={
                quantity < 1 ||
                isAddingToCart ||
                !id ||
                quantity === productQuantity
              }
              className="border-secondary border text-secondary !rounded-full !px-8"
              variant="transparent"
            >
              Add to cart
            </Button>
          </div>
        </div>
      </div>
    </SectionContainer>
  );
};

export default ProductDetails;
