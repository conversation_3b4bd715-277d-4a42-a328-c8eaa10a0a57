import React from "react";
import { useRouter } from "next/router";
import { Arrow<PERSON><PERSON><PERSON>, Check, X, Trash2, Loader2 } from "lucide-react";
import { toast } from "react-toastify";
import Button from "@/components/Button";
import ReviewStatusBadge from "./ReviewStatusBadge";
import CustomImage from "@/components/general/CustomImage";
import { useQuery, useMutation, useQueryClient } from "@tanstack/react-query";
import { getData, postData, deleteData } from "@/api";
import { constructErrorMessage, formatDate } from "@/utils/functions";
import ErrorContainer from "@/components/status/ErrorContainer";

import StarList from "@/components/product/StarList";
import { ReviewDetailsType } from "./ReviewTableRow";

interface ReviewDetailsProps {
  reviewId?: string;
}

const ReviewDetails: React.FC<ReviewDetailsProps> = ({ reviewId }) => {
  const router = useRouter();
  const queryClient = useQueryClient();

  // Fetch review details with Tanstack Query
  const {
    data: review,
    error,
    refetch
  } = useQuery({
    queryKey: ["review", reviewId],
    queryFn: async () => {
      if (!reviewId) return null;
      const { data } = await getData<ApiCallResponseType<ReviewDetailsType>>(
        `/review/${reviewId}`
      );
      return data.data;
    },
    enabled: !!reviewId
  });

  // Create mutations for review actions
  const approveMutation = useMutation({
    mutationFn: async (productId: string) => {
      await postData(`/review/${productId}/approve`, {});
      return productId;
    },
    onSuccess: () => {
      toast.success("Review approved successfully");
      queryClient.invalidateQueries({ queryKey: ["review", reviewId] });
    },
    onError: (error) => {
      const errorMessage = constructErrorMessage(
        error as ApiErrorResponseType,
        "Error approving review"
      );
      toast.error(errorMessage);
    }
  });

  const disapproveMutation = useMutation({
    mutationFn: async (productId: string) => {
      await postData(`/review/${productId}/disapprove`, {});
      return productId;
    },
    onSuccess: () => {
      toast.success("Review rejected successfully");
      queryClient.invalidateQueries({ queryKey: ["review", reviewId] });
    },
    onError: (error) => {
      const errorMessage = constructErrorMessage(
        error as ApiErrorResponseType,
        "Error disapproving review"
      );
      toast.error(errorMessage);
    }
  });

  const deleteMutation = useMutation({
    mutationFn: async (reviewId: string) => {
      await deleteData(`/review/${reviewId}`);
      router.push("/account/reviews");
      return;
    },
    onSuccess: () => {
      toast.success("Review deleted successfully");
      router.push("/account/reviews/all");
    },
    onError: (error) => {
      const errorMessage = constructErrorMessage(
        error as ApiErrorResponseType,
        "Error deleting review"
      );
      toast.error(errorMessage);
    }
  });

  const handleGoBack = () => {
    router.back();
  };

  const handleApproveReview = () => {
    if (review) {
      approveMutation.mutate(review?.id || "");
    }
  };

  const handleRejectReview = () => {
    if (review) {
      disapproveMutation.mutate(review?.id || "");
    }
  };

  const handleDeleteReview = () => {
    if (review) {
      deleteMutation.mutate(review.id);
    }
  };

  if (error) {
    return (
      <div className="p-4">
        <Button onClick={handleGoBack}>
          <ArrowLeft className="h-4 w-4 mr-2" />
          Back to Reviews
        </Button>
        <div className="mt-4">
          <ErrorContainer
            error={constructErrorMessage(
              error as ApiErrorResponseType,
              "Failed to load review details"
            )}
            retryFunction={refetch}
          />
        </div>
      </div>
    );
  }

  if (!review) {
    return (
      <div className="p-4 flex justify-center items-center h-64">
        <div className="flex items-center">
          <Loader2 className="h-6 w-6 animate-spin mr-2" />
          <p>Loading review details...</p>
        </div>
      </div>
    );
  }

  return (
    <div className="p-4">
      <div className="flex items-center justify-between mb-6">
        <Button onClick={handleGoBack} className="flex items-center">
          <ArrowLeft className="h-4 w-4 mr-2" />
          Back to Reviews
        </Button>
        <div className="flex space-x-3">
          {review?.status !== "APPROVED" && (
            <Button
              onClick={handleApproveReview}
              className="flex items-center bg-green-600 hover:bg-green-700"
              disabled={!review}
            >
              <Check className="h-4 w-4 mr-2" />
              Approve Review
            </Button>
          )}
          {review?.status !== "DISAPPROVED" && (
            <Button
              onClick={handleRejectReview}
              className="flex items-center bg-red-600 hover:bg-red-700"
              disabled={!review}
            >
              <X className="h-4 w-4 mr-2" />
              Reject Review
            </Button>
          )}
          <Button
            onClick={handleDeleteReview}
            className="flex items-center bg-gray-600 hover:bg-gray-700"
            disabled={!review}
          >
            <Trash2 className="h-4 w-4 mr-2" />
            Delete Review
          </Button>
        </div>
      </div>
      <div className="bg-white rounded-lg shadow overflow-hidden">
        <div className="px-6 py-4 border-b border-gray-200">
          <h2 className="text-xl font-semibold text-gray-900">
            Review Details
          </h2>
        </div>

        <div className="p-6">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            <div>
              <h3 className="text-lg font-medium text-gray-900 mb-4">
                Review Information
              </h3>
              <div className="space-y-4">
                <div>
                  <p className="text-sm font-medium text-gray-500">Review ID</p>
                  <p className="mt-1">{review?.id}</p>
                </div>
                <div>
                  <p className="text-sm font-medium text-gray-500">Date</p>
                  <p className="mt-1">
                    {formatDate(review?.createdAt || new Date())}
                  </p>
                </div>
                <div>
                  <p className="text-sm font-medium text-gray-500">Status</p>
                  <div className="mt-1">
                    <ReviewStatusBadge status={review?.status} />
                  </div>
                </div>
                <div>
                  <p className="text-sm font-medium text-gray-500">Rating</p>
                  <div className="mt-1">
                    <StarList rating={review?.rating} />
                  </div>
                </div>
              </div>
            </div>

            <div>
              <h3 className="text-lg font-medium text-gray-900 mb-4">
                Product Information
              </h3>
              <div className="flex items-start space-x-4">
                <div className="h-20 w-20 flex-shrink-0">
                  <CustomImage
                    src={review?.product?.images?.[0] || ""}
                    alt={review?.product?.name || ""}
                    className="h-20 w-20 rounded-md object-cover"
                  />
                </div>
                <div className="space-y-2">
                  <p className="font-medium text-gray-900">
                    {review?.product?.name || ""}
                  </p>
                  <p className="text-sm text-gray-500">{review?.product?.id}</p>
                  <Button
                    className="text-sm"
                    onClick={() => {
                      router.push(`/product/${review?.product?.id}`); // Navigate to product details page
                    }}
                  >
                    View Product
                  </Button>
                </div>
              </div>
            </div>
          </div>

          <div className="mt-8">
            <h3 className="text-lg font-medium text-gray-900 mb-4">
              Customer Information
            </h3>
            <div className="flex items-center space-x-4">
              {review?.user?.avatar && (
                <div className="h-12 w-12 flex-shrink-0">
                  <CustomImage
                    src={review?.user?.avatar}
                    alt={review?.user?.firstName}
                    className="h-12 w-12 rounded-full object-cover"
                  />
                </div>
              )}
              {!review?.user?.avatar && (
                <div className="h-12 w-12 flex-shrink-0 bg-gray-200 rounded-full flex items-center justify-center">
                  <span className="text-gray-600 text-lg">
                    {review?.user?.firstName || "Deleted user"?.charAt(0)}
                  </span>
                </div>
              )}
              <div>
                <p className="font-medium text-gray-900">{`${review?.user?.firstName} ${review?.user?.lastName}`}</p>
                <p className="text-sm text-gray-500">{review?.user?.email}</p>
              </div>
            </div>
          </div>

          <div className="mt-8">
            <h3 className="text-lg font-medium text-gray-900 mb-4">
              Review Comment
            </h3>
            <div className="bg-gray-50 p-4 rounded-md">
              <p className="text-gray-700">{review?.content}</p>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default ReviewDetails;
