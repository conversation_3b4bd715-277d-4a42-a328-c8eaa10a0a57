import { changeFirstLetterToCapitalLetter } from "@/utils/functions";
import { useParams } from "next/navigation";
import { useRouter } from "next/router";
import React, { useEffect, useState } from "react";

const tabs = [
  "all",
  "new",
  "pending",
  "delivered",
  "cancelled",
  "paid",
  "shipped"
];

const OrderFilterTabs = () => {
  const [activeTab, setActiveTab] = useState(tabs[0]);
  const { push } = useRouter();
  const params = useParams();
  const { status } = params || {};

  useEffect(() => {
    setActiveTab(status?.toString() || "");
  }, [status]);

  return (
    <div className="border-b border-gray-200 mb-4">
      <nav className="-mb-px flex space-x-6" aria-label="Tabs">
        {tabs.map((tab) => (
          <button
            key={tab}
            onClick={() => push(`/account/orders/${tab}`)}
            className={`whitespace-nowrap py-3 px-1 border-b-2 font-medium text-sm transition-colors duration-150 ease-in-out
                            ${
                              activeTab === tab
                                ? "border-secondary-600 text-primary-300"
                                : "border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300"
                            }
                        `}
            aria-current={activeTab === tab ? "page" : undefined}
          >
            {changeFirstLetterToCapitalLetter(tab)}
          </button>
        ))}
      </nav>
    </div>
  );
};

export default OrderFilterTabs;
