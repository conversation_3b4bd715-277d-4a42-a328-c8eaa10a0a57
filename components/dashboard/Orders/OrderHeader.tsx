import React from 'react';
import { Calendar, Download, MoreHorizontal, Plus } from 'lucide-react';
import Button from '@/components/Button';

const OrderHeader: React.FC = () => {
	return (
		<div className='flex flex-col md:flex-row md:items-center md:justify-between mb-6'>
			<h1 className='text-2xl font-semibold text-gray-900 mb-4 md:mb-0'>
				Order
			</h1>
			<div className='flex flex-wrap items-center gap-2'>
				<Button
					variant='primary'
					size='small'
					className='flex items-center gap-2'>
					<Calendar className='h-4 w-4' />
					<span>Today</span>
				</Button>
				<Button
					variant='primary'
					size='small'
					className='flex items-center gap-2'>
					<Download className='h-4 w-4' />
					<span>Export</span>
				</Button>
				<Button
					variant='primary'
					size='small'
					className='flex items-center gap-2'>
					<MoreHorizontal className='h-4 w-4' />
					<span>More Action</span>
				</Button>
				<Button
					variant='primary'
					size='small'
					className='flex items-center gap-2'>
					<Plus className='h-4 w-4' />
					<span>Create Order</span>
				</Button>
			</div>
		</div>
	);
};

export default OrderHeader;
