import React, { useCallback, useEffect, useState } from "react";
import StatCard from "@/components/dashboard/StatCard";
import { Ticket, Clock } from "lucide-react";
import { useQuery } from "@tanstack/react-query";
import { getData } from "@/api";
import ErrorContainer from "@/components/status/ErrorContainer";
import { constructErrorMessage } from "@/utils/functions";

export type CouponStat = {
  activeCoupons: number;
  expiredCoupons: number;
  totalCoupons: number;
};

const CouponStats = () => {
  const [stat, setStat] = useState<CouponStat | null>(null);
  const queryFn = useCallback(async () => {
    return await getData<ApiCallResponseType<CouponStat>>("/coupons/stat");
  }, []);
  const { data, error, refetch } = useQuery({
    queryKey: ["coupon-stat"],
    queryFn
  });

  useEffect(() => {
    if (data) {
      setStat(data?.data?.data);
    }
  }, [data]);
  return (
    <>
      {error && (
        <ErrorContainer
          error={constructErrorMessage(
            error as ApiErrorResponseType,
            "Unable to fetch stats! Please try again"
          )}
          retryFunction={refetch}
        />
      )}
      {((!error && !stat) || stat) && (
        <div className="grid grid-cols-1 md:grid-cols-3  gap-4 mb-6">
          <StatCard
            title="Total Coupons"
            value={stat?.totalCoupons?.toString()}
            icon={<Ticket className="h-5 w-5 text-blue-500" />}
            iconBgColor="bg-blue-50"
          />
          {/* <StatCard
        title='Active Coupons'
        value={isLoading ? '...' : stats?.activeCoupons || 0}
        icon={<CheckCircle className='h-5 w-5 text-green-500' />}
        iconBgColor='bg-green-50'
      /> */}
          <StatCard
            title="Active Coupons"
            value={stat?.activeCoupons?.toString()}
            icon={<Ticket className="h-5 w-5 text-green-500" />}
            iconBgColor="bg-green-50"
          />
          <StatCard
            title="Expired Coupons"
            value={stat?.expiredCoupons?.toString()}
            icon={<Clock className="h-5 w-5 text-orange-500" />}
            iconBgColor="bg-orange-50"
          />

          {/* <StatCard
				title='Used Coupons'
				value={isLoading ? '...' : stats?.usedCoupons || 0}
				icon={<XCircle className='h-5 w-5 text-purple-500' />}
				iconBgColor='bg-purple-50'
			/> */}
        </div>
      )}
    </>
  );
};

export default CouponStats;
