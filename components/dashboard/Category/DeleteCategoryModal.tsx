import React, { useCallback, useState } from "react";
import Modal from "@/components/Modal";
import Button from "@/components/Button";
import { CategoryDetailsType } from "@/store/useProductStore";
import { toast } from "react-toastify";
import { constructErrorMessage } from "@/utils/functions";
import { AlertTriangle, X } from "lucide-react";
import { deleteData } from "@/api";

interface DeleteCategoryModalProps {
  opened: boolean;
  onClose: () => void;
  category: CategoryDetailsType;
  onCategoryDeleted?: () => void;
}

const DeleteCategoryModal: React.FC<DeleteCategoryModalProps> = ({
  opened,
  onClose,
  category,
  onCategoryDeleted
}) => {
  const [isDeleting, setIsDeleting] = useState(false);
  const [error, setError] = useState<string | null>(null);

  const handleDelete = useCallback(async () => {
    setIsDeleting(true);
    setError(null);

    if (!category) {
      return toast("No category selected");
    }

    try {
      await deleteData(`/category/${category?.id}`);
      toast.success("Category deleted successfully!");

      if (onCategoryDeleted) {
        onCategoryDeleted();
      }
    } catch (err: unknown) {
      const errorMessage = constructErrorMessage(
        err as ApiErrorResponseType,
        "Failed to delete category. Please try again."
      );
      setError(errorMessage);
      toast.error(errorMessage);
      console.error("[Delete Category Error]:", err);
    } finally {
      setIsDeleting(false);
    }
  }, [onCategoryDeleted, category]);

  return (
    <Modal
      opened={opened}
      onClose={onClose}
      position="center"
      contentClassName="bg-white p-6 rounded-lg max-w-md mx-auto"
    >
      <div className="flex justify-between items-center mb-4">
        <h2 className="text-xl font-semibold">Delete Category</h2>
        <button
          title="close modal"
          onClick={onClose}
          className="text-gray-500 hover:text-gray-700"
        >
          <X className="h-5 w-5" />
        </button>
      </div>

      <div className="mb-6">
        <div className="flex items-center justify-center mb-4 text-amber-500">
          <AlertTriangle className="h-12 w-12" />
        </div>
        <p className="text-center text-gray-700 mb-2">
          Are you sure you want to delete the category
          <span className="font-semibold">&quot;{category.name}&quot;</span>?
        </p>
        {category?.totalProducts > 0 && (
          <p className="text-center text-red-500 text-sm">
            Warning: This category contains {category?.totalProducts || 0}
            products. Deleting it may affect these products.
          </p>
        )}
      </div>

      {error && <p className="text-red-500 text-sm mb-4">{error}</p>}

      <div className="flex justify-center space-x-4">
        <Button
          type="button"
          variant="white"
          onClick={onClose}
          disabled={isDeleting}
        >
          Cancel
        </Button>
        <Button
          type="button"
          variant="primary-dark"
          className="bg-red-600 hover:bg-red-700"
          onClick={handleDelete}
          disabled={isDeleting}
        >
          {isDeleting ? "Deleting..." : "Delete Category"}
        </Button>
      </div>
    </Modal>
  );
};

export default DeleteCategoryModal;
