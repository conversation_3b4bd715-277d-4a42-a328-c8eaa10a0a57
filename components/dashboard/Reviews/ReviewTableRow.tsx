import React, { useState, useRef, useEffect, useCallback } from "react";
import { MoreHorizontal, Eye, Check, X, Trash2 } from "lucide-react";
import CheckBox from "@/components/general/CheckBox";
import CustomImage from "@/components/general/CustomImage";
import ReviewStatusBadge, { ReviewStatus } from "./ReviewStatusBadge";
import StarList from "@/components/product/StarList";
import Button from "@/components/Button";
import { ShortUserResponse } from "@/store/useUserStore";
import { constructErrorMessage, formatDate } from "@/utils/functions";
import { deleteData, postData } from "@/api";
import { toast } from "react-toastify";

export type ReviewDetailsType = {
  id: string;
  rating: number;
  content: string;
  createdAt?: Date;
  updatedAt?: Date;
  user: ShortUserResponse;
  product?: {
    name: string;
    id: string;
    images?: string[];
  };
  status?: ReviewStatus;
  isNew?: boolean;
};

interface ReviewTableRowProps {
  review: ReviewDetailsType;
  isSelected: boolean;
  onSelect: (id: string, checked: boolean) => void;
  refetch: () => void;
}

const ReviewTableRow: React.FC<ReviewTableRowProps> = ({
  review,
  isSelected,
  onSelect,
  refetch = () => {}
}) => {
  const [isChangingReviewStatus, setIsChangingReviewStatus] = useState(false);
  const [showDropdown, setShowDropdown] = useState(false);
  const dropdownRef = useRef<HTMLDivElement>(null);

  const approveReview = useCallback(async () => {
    const id = review?.id;
    if (!id) {
      return;
    }
    setIsChangingReviewStatus(true);
    try {
      await postData(`/review/${id}/approve`);
      refetch();
    } catch (error) {
      toast(
        constructErrorMessage(
          error as ApiErrorResponseType,
          "Unable to approve review! Please try again later"
        )
      );
    } finally {
      setIsChangingReviewStatus(false);
    }
  }, [review, refetch]);
  const disapproveReview = useCallback(async () => {
    const id = review?.id;
    if (!id) {
      return;
    }
    setIsChangingReviewStatus(true);
    try {
      await postData(`/review/${id}/disapprove`);
      refetch();
    } catch (error) {
      toast(
        constructErrorMessage(
          error as ApiErrorResponseType,
          "Unable to disapprove review! Please try again later"
        )
      );
    } finally {
      setIsChangingReviewStatus(false);
    }
  }, [review, refetch]);
  const deleteReview = useCallback(async () => {
    const id = review?.id;
    if (!id) {
      return;
    }
    setIsChangingReviewStatus(true);
    try {
      await deleteData(`/review/${id}`);
      refetch();
    } catch (error) {
      toast(
        constructErrorMessage(
          error as ApiErrorResponseType,
          "Unable to delete review! Please try again later"
        )
      );
    } finally {
      setIsChangingReviewStatus(false);
    }
  }, [review, refetch]);

  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (
        dropdownRef.current &&
        !dropdownRef.current.contains(event.target as Node)
      ) {
        setShowDropdown(false);
      }
    };

    document.addEventListener("mousedown", handleClickOutside);
    return () => {
      document.removeEventListener("mousedown", handleClickOutside);
    };
  }, []);

  const truncateText = (text: string, maxLength: number) => {
    if (text.length <= maxLength) return text;
    return text.substring(0, maxLength) + "...";
  };

  return (
    <tr className="border-b border-gray-200 hover:bg-gray-50">
      <td className="px-4 py-4 w-[3rem]">
        <CheckBox
          checked={isSelected}
          onChange={(e: React.ChangeEvent<HTMLInputElement>) =>
            onSelect(review?.id, e.target.checked)
          }
        />
      </td>
      <td className="px-4 py-4">
        <div className="flex items-center gap-3">
          <div className="size-10 flex-shrink-0 relative">
            <CustomImage
              src={review?.product?.images?.[0] || ""}
              alt={review?.product?.name || "Deleted product!"}
              className="h-10 w-10 rounded-md object-cover"
            />
          </div>
          <div>
            <div className="font-medium text-gray-900">
              {review?.product?.name}
            </div>
            <div className="text-gray-500 text-sm">{review?.product?.id}</div>
          </div>
          {review?.isNew && (
            <span className="bg-amber-100 text-amber-700 px-2 py-1 text-[0.5rem] font-semibold rounded-full uppercase">
              New
            </span>
          )}
        </div>
      </td>
      <td className="px-4 py-4 text-sm text-gray-500">{review?.id}</td>
      <td className="px-4 py-4 text-sm text-gray-500">
        {formatDate(review?.createdAt || new Date())}
      </td>
      <td className="px-4 py-4">
        <div className="flex items-center">
          {review?.user?.avatar && (
            <div className="h-8 w-8 flex-shrink-0 mr-3">
              <CustomImage
                src={review?.user?.avatar}
                alt={`${review?.user?.firstName} ${review?.user?.lastName}`}
                className="h-8 w-8 rounded-full object-cover"
              />
            </div>
          )}
          {!review?.user?.avatar && (
            <div className="h-8 w-8 flex-shrink-0 mr-3 bg-gray-200 rounded-full flex items-center justify-center">
              <span className="text-gray-600 text-sm">
                {(review?.user?.firstName || "Deleted User")?.charAt(0)}
              </span>
            </div>
          )}
          <div>
            <div className="font-medium text-gray-900">
              {review?.user?.firstName || "Deleted"}
              {review?.user?.lastName || "user"}
            </div>
            <div className="text-gray-500 text-sm">{review?.user?.email}</div>
          </div>
        </div>
      </td>
      <td className="px-4 py-4">
        <StarList rating={review.rating} />
      </td>
      <td className="px-4 py-4 text-sm text-gray-500">
        {truncateText(review?.content, 50)}
      </td>
      <td className="px-4 py-4">
        <ReviewStatusBadge status={review?.status} />
      </td>
      <td className="px-4 py-4 text-right text-sm font-medium">
        <div className="relative" ref={dropdownRef}>
          <Button
            onClick={() => setShowDropdown(!showDropdown)}
            className="text-gray-400 hover:text-gray-500 bg-transparent"
          >
            <MoreHorizontal className="h-5 w-5" />
          </Button>
          {showDropdown && (
            <div className="absolute right-0 mt-2 w-48 bg-white rounded-md shadow-lg z-10">
              <div className="py-1">
                <Button
                  onClick={() => {
                    setShowDropdown(false);
                  }}
                  className="flex items-center px-4 py-2 text-sm text-gray-700 hover:bg-gray-100 w-full text-left"
                >
                  <Eye className="h-4 w-4 mr-2" />
                  View Details
                </Button>
                {review?.status && (
                  <>
                    {review?.status !== "APPROVED" && (
                      <Button
                        onClick={() => {
                          approveReview();
                          setShowDropdown(false);
                        }}
                        disabled={isChangingReviewStatus}
                        className="flex items-center px-4 py-2 text-sm text-gray-700 hover:bg-gray-100 w-full text-left"
                      >
                        <Check className="h-4 w-4 mr-2 text-green-500" />
                        Approve
                      </Button>
                    )}

                    {review?.status !== "DISAPPROVED" && (
                      <button
                        onClick={() => {
                          disapproveReview();
                          setShowDropdown(false);
                        }}
                        className="flex items-center px-4 py-2 text-sm text-gray-700 hover:bg-gray-100 w-full text-left"
                        disabled={isChangingReviewStatus}
                      >
                        <X className="h-4 w-4 mr-2 text-red-500" />
                        Reject
                      </button>
                    )}
                  </>
                )}
                <button
                  onClick={() => {
                    deleteReview();
                    setShowDropdown(false);
                  }}
                  className="flex items-center px-4 py-2 text-sm text-red-700 hover:bg-gray-100 w-full text-left"
                  disabled={isChangingReviewStatus}
                >
                  <Trash2 className="h-4 w-4 mr-2 text-red-500" />
                  Delete
                </button>
              </div>
            </div>
          )}
        </div>
      </td>
    </tr>
  );
};

export default ReviewTableRow;
