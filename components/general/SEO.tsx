import { SEODescription, siteName, websiteUrl } from "@/utils/variables";
import Head from "next/head";
import Script from "next/script";

type SEOProps = {
  title?: string;
  description?: string;
  image?: string;
  imageDescription?: string;
  locale?: string;
};

const SEO: React.FC<SEOProps> = ({
  title,
  description,
  image,
  // imageDescription,
  locale
}) => {
  return (
    <>
      <Head key="main-head">
        <title>{`${siteName}${title ? ` | ${title}` : ""} `}</title>
        <meta
          name="description"
          content={description ? description : SEODescription}
        />

        {/* Facebook meta */}
        <meta
          property="og:title"
          content={`${siteName}${title ? `| ${title}` : ""} `}
        />
        <meta
          property="og:description"
          content={description ? description : SEODescription}
        />
        <meta
          property="og:image"
          content={image ? image : `${websiteUrl}/meta_image.png`}
        />
        <meta
          property="og:image:alt"
          content={`${siteName}${title ? `| ${title}` : ""} `}
        />
        <meta property="og:locale" content={locale ? locale : "en_US"} />
        <meta property="og:type" content="website" />
        <meta property="og:url" content={websiteUrl} />
        <meta property="og:site_name" content={siteName} />
        <meta property="og:site" content={siteName} />
        <meta property="og:updated_time" content={new Date().toString()} />
        <meta property="og:see_also" content={websiteUrl} />
        <meta property="og:locale:alternate" content="en_US" />
        <meta property="og:locale:alternate" content="ar_EG" />

        {/* Twitter meta */}
        <meta name="twitter:card" content="summary_large_image" />
        <meta
          name="twitter:image"
          content={image ? image : `${websiteUrl}/meta_image.png`}
        />
        <meta
          name="twitter:image:alt"
          content={`${siteName}${title ? `| ${title}` : ""} `}
        />
        <meta name="twitter:site" content="@Banafaperfume" />
        <meta name="twitter:creator" content="@dasimems" />
        <meta
          name="twitter:title"
          content={`${siteName}${title ? `| ${title}` : ""} `}
        />
        <meta
          name="twitter:description"
          content={description ? description : SEODescription}
        />
        <meta name="twitter:label1" content="Developed by" />
        <meta name="twitter:data1" content="@dasimems" />
        <meta name="twitter:label2" content="Developed by" />
        <meta name="twitter:data2" content="@Agbeni_Daniel" />

        <link
          rel="apple-touch-icon"
          href={image ? image : `${websiteUrl}/meta_image.png`}
        />
      </Head>
      <Script
        src="https://www.google-analytics.com/analytics.js"
        async
      ></Script>
    </>
  );
};

export default SEO;
