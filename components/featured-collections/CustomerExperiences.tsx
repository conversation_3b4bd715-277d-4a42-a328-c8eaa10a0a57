import React from "react";
import SectionContainer from "../layouts/SectionContainer";
import SectionHeader from "../general/SectionHeader";
import { LuShoppingCart } from "react-icons/lu";
import { convertObjectToArray } from "@/utils/functions";
import { Box, Ship, SmilePlus } from "lucide-react";

const customerExperienceContent = {
  originalProducts: {
    title: "Original Products",
    description: "We ensure money-back guarantee if the product is counterfeit",
    icon: <LuShoppingCart />
  },
  satisfaction: {
    title: "Satisfaction Guarantee",
    description: "We ensure money-back guarantee if the product is counterfeit",
    icon: <SmilePlus />
  },
  newArrival: {
    title: "New Arrival Everyday",
    description: "We ensure money-back guarantee if the product is counterfeit",
    icon: <Box />
  },
  fastShipping: {
    title: "Fast & Free Shipping",
    description: "We ensure money-back guarantee if the product is counterfeit",
    icon: <Ship />
  }
};

const customerExperienceList = convertObjectToArray(customerExperienceContent);

const CustomerExperiencesCard: React.FC<{
  icon: React.ReactNode;
  title: string;
  description: string;
}> = ({ icon, title, description }) => {
  return (
    <div className="flex flex-col items-start gap-4">
      <span className="inline-flex size-12 rounded-full text-secondary text-xl bg-secondary-900 items-center justify-center">
        {icon}
      </span>
      <div className="flex flex-col gap-2 w-full items-start">
        <h1 className="font-medium text-lg md:text-xl">{title}</h1>
        <p className="opacity-60">{description}</p>
      </div>
    </div>
  );
};

const CustomerExperiences = () => {
  return (
    <SectionContainer contentContainerClassName="py-10">
      <div className="flex flex-col gap-10 md:px-5">
        <SectionHeader title="We provide the best customer experiences" />
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-5">
          {customerExperienceList.map((item) => (
            <CustomerExperiencesCard key={item.title} {...item} />
          ))}
        </div>
      </div>
    </SectionContainer>
  );
};

export default CustomerExperiences;
