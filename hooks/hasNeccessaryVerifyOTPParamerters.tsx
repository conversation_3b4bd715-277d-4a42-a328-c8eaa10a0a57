import React, { useCallback, useEffect } from "react";
import { useRouter } from "next/router";
import { verificationTypesArray } from "@/utils/variables";

const hasNeccessaryVerifyOTPParameters = <P extends object>(
  Component: React.ComponentType<P>
) => {
  const Page = (props: P) => {
    const { push, query } = useRouter();
    const { token, email, type } = query || {};

    const redirectToHome = useCallback(() => {
      push(`/`);
    }, [push]);

    useEffect(() => {
      const isTypeIncludedInArray = verificationTypesArray.includes(
        type?.toString() || ""
      );
      if (!token || !email || !type || !isTypeIncludedInArray) {
        redirectToHome();
      }
    }, [redirectToHome, token, email, type]);

    return <Component {...props} />;
  };
  return Page;
};

export default hasNeccessaryVerifyOTPParameters;
