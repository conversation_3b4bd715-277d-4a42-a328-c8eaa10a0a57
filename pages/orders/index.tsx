import React, { useEffect, useState } from "react";
import { useInfiniteQuery } from "@tanstack/react-query";
import { getData } from "@/api";
import { OrderDetailsType } from "@/store/useOrderStore";
import SectionContainer from "@/components/layouts/SectionContainer";
import ErrorContainer from "@/components/status/ErrorContainer";
import { ArrowLeft, Eye } from "lucide-react";
import { useRouter } from "next/router";
import Button from "@/components/Button";
import EmptyContainer from "@/components/status/EmptyContainer";
import { constructErrorMessage, formatDate } from "@/utils/functions";
import protectRoute from "@/hooks/protectRoute";
import Head from "next/head";
import { siteName } from "@/utils/variables";

const fetchOrders = async (page: number = 1) => {
  return await getData<ApiCallResponseType<OrderDetailsType[]>>(
    `/orders?page=${page}`
  );
};

const OrdersPage = () => {
  const router = useRouter();
  const [orders, setOrders] = useState<OrderDetailsType[] | null>(null);

  const { data, isError, error, refetch } = useInfiniteQuery({
    queryKey: ["customer-orders"],
    queryFn: ({ pageParam }) => fetchOrders(pageParam),
    initialPageParam: 1,
    getNextPageParam: (lastPage) => lastPage?.data?.pagination?.nextPage
  });

  const getStatusBadgeClass = (status: string) => {
    switch (status) {
      case "PAID":
        return "bg-green-100 text-green-800";
      case "PENDING":
        return "bg-yellow-100 text-yellow-800";
      case "DELIVERED":
        return "bg-blue-100 text-blue-800";
      default:
        return "bg-gray-100 text-gray-800";
    }
  };

  const calculateTotal = (order: OrderDetailsType) => {
    return (order.carts || [])
      .map((cart) => cart?.price?.amount || 0)
      .reduce((sum, nextPrice) => {
        return sum + nextPrice;
      }, 0)
      .toFixed(2);
  };

  const getItemsCount = (order: OrderDetailsType) => {
    if (order.totalCarts !== undefined) {
      return order.totalCarts;
    }
    if (order.carts && order.carts.length > 0) {
      return order.carts.length;
    }
    return 0;
  };

  useEffect(() => {
    if (data) {
      setOrders(data?.pages?.flatMap((value) => value?.data?.data));
    }
  }, [data]);

  return (
    <>
      <Head>
        <title>Orders | {siteName}</title>
      </Head>
      <SectionContainer contentContainerClassName="flex items-center gap-4">
        <div className="flex items-center gap-2 py-4">
          <button
            onClick={() => router.back()}
            title="go back"
            className="text-slate-500 text-sm inline-flex items-center gap-2"
          >
            <span className="inline-flex border border-slate-200 px-2 py-1">
              <ArrowLeft size={15} />
            </span>
            <span className="inline-block">Go back</span>
          </button>
        </div>
      </SectionContainer>

      <SectionContainer>
        <div className="py-8">
          <h1 className="text-2xl font-semibold mb-6">My Orders</h1>

          {!orders && (
            <div className="space-y-4">
              {[...Array(3)].map((_, index) => (
                <div
                  key={index}
                  className="bg-gray-100 animate-pulse h-24 rounded-lg"
                ></div>
              ))}
            </div>
          )}

          {isError && !orders && (
            <ErrorContainer
              error={constructErrorMessage(
                error as ApiErrorResponseType,
                "Failed to load orders."
              )}
              retryFunction={refetch}
            />
          )}

          {orders && orders?.length === 0 && (
            <EmptyContainer description="You haven't placed any orders yet" />
          )}

          {orders && orders.length > 0 && (
            <div className="space-y-4">
              {orders.map((order) => (
                <div
                  key={order.id}
                  className="border rounded-lg p-4 shadow-sm hover:shadow-md transition-shadow"
                >
                  <div className="flex flex-col md:flex-row justify-between gap-4">
                    <div>
                      <div className="flex items-center gap-2 mb-2">
                        <h3 className="font-medium">Order ID:</h3>
                        <span className="text-gray-700">
                          {order.orderCode || order.id}
                        </span>
                      </div>
                      <div className="flex items-center gap-2 mb-2">
                        <h3 className="font-medium">Date:</h3>
                        <span className="text-gray-700">
                          {formatDate(
                            order.paymentInitiatedAt ||
                              order.paidAt ||
                              order.createdAt ||
                              ""
                          )}
                        </span>
                      </div>
                      <div className="flex items-center gap-2">
                        <h3 className="font-medium">Status:</h3>
                        <span
                          className={`px-2 py-1 rounded-full text-xs ${getStatusBadgeClass(
                            order.status
                          )}`}
                        >
                          {order.status}
                        </span>
                      </div>
                    </div>
                    <div>
                      <div className="flex items-center gap-2 mb-2">
                        <h3 className="font-medium">Items:</h3>
                        <span className="text-gray-700">
                          {getItemsCount(order)}
                        </span>
                      </div>
                      <div className="flex items-center gap-2 mb-4">
                        <h3 className="font-medium">Total:</h3>
                        <span className="text-gray-700">
                          {calculateTotal(order)} AED
                        </span>
                      </div>
                      <Button
                        onClick={() => {
                          router.push(`/order/${order.id}`);
                        }}
                        variant="primary"
                        size="small"
                        className="flex items-center gap-2"
                      >
                        <Eye size={16} />
                        <span>View Details</span>
                      </Button>
                    </div>
                  </div>
                </div>
              ))}
            </div>
          )}
        </div>
      </SectionContainer>
    </>
  );
};

export default protectRoute(OrdersPage);
