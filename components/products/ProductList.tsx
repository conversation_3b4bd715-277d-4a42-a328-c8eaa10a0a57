import React, { useState, useEffect, useCallback, useRef } from "react";
import ProductCard, { ProductDetailsType } from "../product/ProductCard";
import { getData } from "@/api";
import EmptyContainer from "../status/EmptyContainer";
import { useInfiniteQuery } from "@tanstack/react-query";
import { useRouter } from "next/router";
import ErrorContainer from "../status/ErrorContainer";
import { constructErrorMessage } from "@/utils/functions";
import ProductCardLoader from "../product/ProductCardLoader";
import { useParams } from "next/navigation";
import useProduct from "@/hooks/useProduct";

const emptyPageClassName =
  "text-center py-8 text-gray-500 flex justify-center items-center w-full";

const ProductList = () => {
  const { categories } = useProduct();
  const { query } = useRouter();
  const { min, max, q, is_favorite, best_seller, is_special, latest } =
    query || {};

  const param = useParams();
  const { slug } = param || {};

  const stringifiedSlug = slug?.toString();
  const itemList = stringifiedSlug?.split(",") || [];
  const categoryName = itemList?.[itemList.length - 1];
  const selectedCategory = (categories || []).find(
    (category) => category?.name === categoryName
  );

  const [products, setProducts] = useState<ProductDetailsType[] | null>(null);
  const observerRef = useRef<HTMLDivElement>(null);

  const constructCacheName = useCallback(() => {
    let cacheName = "products";
    if (min) cacheName += `-${min}`;
    if (max) cacheName += `-${max}`;
    if (q) cacheName += `-${q}`;
    if (is_favorite) cacheName += "-favorite";
    if (best_seller) cacheName += "-best-seller";
    if (is_special) cacheName += "-special";
    if (latest) cacheName += "-latest";
    if (selectedCategory) cacheName += `-${selectedCategory.id}`;

    return cacheName;
  }, [
    min,
    max,
    q,
    is_favorite,
    best_seller,
    is_special,
    latest,
    selectedCategory
  ]);

  const queryFn = useCallback(
    async ({ pageParam }: { pageParam: number }) => {
      const url = new URLSearchParams();

      url.set("page", pageParam.toString());
      if (min) url.set("min", min.toString());
      if (max) url.set("max", max.toString());
      if (q) url.set("q", q.toString());
      if (is_favorite) url.set("is_favorite", is_favorite.toString());
      if (best_seller) url.set("best_seller", best_seller.toString());
      if (is_special) url.set("is_special", is_special.toString());
      if (latest) url.set("latest", latest.toString());
      if (selectedCategory) url.set("categoryId", selectedCategory.id);
      return await getData<ApiCallResponseType<ProductDetailsType[]>>(
        `/products?${url.toString()}`
      );
    },
    [
      min,
      max,
      q,
      is_favorite,
      best_seller,
      is_special,
      latest,
      selectedCategory
    ]
  );

  const {
    data,
    error,
    refetch,
    fetchNextPage,
    isLoading,
    isFetchingNextPage,
    hasNextPage
  } = useInfiniteQuery({
    queryKey: [constructCacheName()],
    queryFn,
    initialPageParam: 1,
    getNextPageParam: (lastPage) =>
      lastPage?.data?.meta?.nextLink
        ? lastPage?.data?.pagination?.nextPage
        : undefined
  });

  useEffect(() => {
    if (data) {
      const productList = data.pages.flatMap(
        (value) => value?.data?.data || []
      );
      setProducts(productList);
    }
  }, [data]);

  useEffect(() => {
    const observer = new IntersectionObserver(
      (entries) => {
        if (entries[0].isIntersecting && hasNextPage) {
          fetchNextPage();
        }
      },
      {
        rootMargin: "100px"
      }
    );

    if (observerRef.current) {
      observer.observe(observerRef.current);
    }

    return () => {
      if (observerRef.current) observer.unobserve(observerRef.current);
    };
  }, [fetchNextPage, hasNextPage]);

  if (error) {
    return (
      <div className={emptyPageClassName}>
        <ErrorContainer
          error={constructErrorMessage(
            error as ApiErrorResponseType,
            "Unable to fetch products! Please try again later"
          )}
          retryFunction={refetch}
        />
      </div>
    );
  }

  if (!products || isLoading) {
    return (
      <div className="grid grid-cols-1 md:grid-cols-1 lg:grid-cols-2 gap-4 w-full">
        {new Array(9).fill(0).map((_, index) => (
          <ProductCardLoader key={index} />
        ))}
      </div>
    );
  }

  if (products && products.length < 1) {
    return (
      <div className={emptyPageClassName}>
        <EmptyContainer description="No product available" />
      </div>
    );
  }

  return (
    <div className="flex-1">
      <div className="grid grid-cols-1 md:grid-cols-1 lg:grid-cols-2 gap-4 flex-1">
        {products.map((product) => (
          <ProductCard {...product} key={product?.id} />
        ))}
      </div>

      {/* Sentinel div */}
      <div
        ref={observerRef}
        className="h-20 w-full flex justify-center items-center"
      >
        {isFetchingNextPage && (
          <div className="text-gray-500 py-4">Loading more products...</div>
        )}
      </div>
    </div>
  );
};

export default ProductList;
