import React, { useCallback, useState } from "react";
import Modal from "@/components/Modal";
import InputField from "@/components/general/InputField";
import SelectBox from "@/components/general/SelectBox";
import Button from "@/components/Button";
import { Loader } from "lucide-react";
import { useForm } from "react-hook-form";
import { CouponDetailsType, CouponDiscountType } from "@/store/useOrderStore";
import { postData } from "@/api";
import { useRouter } from "next/router";
import { format } from "date-fns";
import { constructErrorMessage } from "@/utils/functions";

export type CouponFormData = {
  discountType: CouponDiscountType;
  discountAmount: number;
  minAmount: number;
  expiresAt: Date;
  usageCount?: number;
};

interface CreateCouponModalProps {
  isOpen: boolean;
  onClose: () => void;
}

const CreateCouponModal: React.FC<CreateCouponModalProps> = ({
  isOpen,
  onClose
}) => {
  const { push } = useRouter();
  const [error, setSubmittingError] = useState<string | null>(null);
  const [generatedCode, setGeneratedCode] = useState<string | null>(null);
  const today = format(new Date(), "yyyy-MM-dd");

  const {
    register,
    handleSubmit,
    reset,
    watch,
    setError,
    formState: { isSubmitting, errors }
  } = useForm<CouponFormData>({
    defaultValues: {
      discountType: "PERCENTAGE",
      discountAmount: 0,
      minAmount: 0,
      expiresAt: new Date()
    },
    mode: "onChange"
  });

  const discountType = watch("discountType");

  const onSubmit = useCallback(
    async (body: CouponFormData) => {
      setSubmittingError(null);
      const { usageCount, ...otherContent } = body;
      let contentToSend: CouponFormData = otherContent;
      if (usageCount && usageCount > 0) {
        contentToSend = { ...otherContent, usageCount };
      }
      try {
        const { data } = await postData<
          CouponFormData,
          ApiCallResponseType<CouponDetailsType>
        >("/coupon", contentToSend);
        setGeneratedCode(data?.data?.code);
        reset();
      } catch (err) {
        const apiError = err as ApiErrorResponseType;
        if (apiError?.response?.data?.errors) {
          const errorKeys = Object.keys(apiError.response.data.errors || {});
          errorKeys.forEach((key) => {
            setError(key as keyof CouponFormData, {
              message:
                apiError.response?.data?.errors?.[key] || "Error occurred"
            });
          });
        }
        setSubmittingError(
          constructErrorMessage(
            apiError,
            "Failed to create coupon. Please try again."
          )
        );
      }
    },
    [reset, setError]
  );

  return (
    <Modal
      opened={isOpen}
      onClose={() => {
        setGeneratedCode(null);
        onClose();
      }}
      title="Create Coupon"
      position="center"
      contentClassName="bg-white p-6 rounded-2xl w-[480px]"
    >
      <div className="relative w-screen max-w-[30rem]">
        {error && (
          <div className="mb-4 p-2 bg-red-50 text-red-500 rounded-md text-sm">
            {error}
          </div>
        )}
        {generatedCode && (
          <div className="text-center p-4 bg-green-50 border border-green-300 rounded-md text-green-700">
            <p className="text-sm mb-1">Coupon successfully created!</p>
            <p className="font-bold text-lg">{generatedCode}</p>
            <Button
              variant="secondary"
              className="mt-4 w-full"
              onClick={() => {
                push("/account/coupons/all");
                setGeneratedCode(null);
                onClose();
              }}
            >
              Close
            </Button>
          </div>
        )}
        {!generatedCode && (
          <form onSubmit={handleSubmit(onSubmit)}>
            <div className="grid grid-cols-1 gap-4">
              <div className="w-full grid grid-cols-2 gap-4">
                <SelectBox
                  label="Discount Type"
                  id="discountType"
                  options={[
                    { value: "FIXED", label: "Fixed Amount" },
                    { value: "PERCENTAGE", label: "Percentage" }
                  ]}
                  required
                  error={errors?.discountType?.message}
                  {...register("discountType", {
                    required: "Discount type is required"
                  })}
                />
                <InputField
                  label={
                    discountType === "FIXED"
                      ? "Discount Amount"
                      : "Discount Percentage"
                  }
                  id="discountAmount"
                  type="number"
                  placeholder={discountType === "FIXED" ? "0.00" : "0"}
                  min="0"
                  step={discountType === "FIXED" ? "0.01" : "1"}
                  required
                  error={errors?.discountAmount?.message}
                  {...register("discountAmount", {
                    required: "Discount amount is required",
                    min: {
                      value: 0,
                      message: "Discount amount must be positive"
                    },
                    max: {
                      value: discountType === "PERCENTAGE" ? 100 : 1000000,
                      message:
                        discountType === "PERCENTAGE"
                          ? "Max is 100%"
                          : "Amount too high"
                    },
                    valueAsNumber: true
                  })}
                />
              </div>

              {/* Second row */}
              <div className="w-full grid grid-cols-2 gap-4">
                <InputField
                  label="Minimum Purchase Amount"
                  id="minAmount"
                  type="number"
                  placeholder="0.00"
                  min="0"
                  step="0.01"
                  required
                  error={errors?.minAmount?.message}
                  {...register("minAmount", {
                    required: "Minimum amount is required",
                    min: {
                      value: 0,
                      message: "Must be positive"
                    },
                    valueAsNumber: true
                  })}
                />
                <InputField
                  label="Expiration Date"
                  id="expiresAt"
                  type="date"
                  min={today}
                  required
                  error={errors?.expiresAt?.message}
                  {...register("expiresAt", {
                    required: "Expiration date is required",
                    validate: (value) => {
                      const selectedDate = new Date(value);
                      const today = new Date();
                      return (
                        selectedDate >= today || "Date must be in the future"
                      );
                    }
                  })}
                />
              </div>
            </div>

            <div className="w-full">
              <InputField
                label="Usage count (No of time to be used. Optional)"
                id="usage-count"
                type="number"
                placeholder="0"
                min="0"
                step="1"
                error={errors?.usageCount?.message}
                {...register("usageCount", {
                  min: {
                    value: 0,
                    message: "Must be positive"
                  },
                  valueAsNumber: true
                })}
              />
            </div>

            <div className="mt-6 flex justify-end space-x-3">
              <Button
                variant="default"
                type="button"
                onClick={onClose}
                disabled={isSubmitting}
              >
                Cancel
              </Button>
              <Button
                variant="primary"
                type="submit"
                disabled={isSubmitting}
                className="flex items-center gap-2"
              >
                {isSubmitting ? (
                  <>
                    <Loader className="h-4 w-4 animate-spin" />
                    <span>Creating...</span>
                  </>
                ) : (
                  <span>Create</span>
                )}
              </Button>
            </div>
          </form>
        )}
      </div>
    </Modal>
  );
};

export default CreateCouponModal;
