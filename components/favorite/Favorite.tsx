import React, { useState, useEffect, useCallback } from "react";
import ProductCard, { ProductDetailsType } from "../product/ProductCard";
import { getData } from "@/api";
import EmptyContainer from "../status/EmptyContainer";
import { useInfiniteQuery } from "@tanstack/react-query";
import ErrorContainer from "../status/ErrorContainer";
import { constructErrorMessage } from "@/utils/functions";
import ProductCardLoader from "../product/ProductCardLoader";
import SectionContainer from "../layouts/SectionContainer";

export type FavoriteDetailsType = {
  product: ProductDetailsType;
  id: string;
};

const emptyPageClassName =
  "text-center py-8 text-gray-500 flex justify-center items-center w-full";

const Favorites = () => {
  const [favorites, setFavorites] = useState<FavoriteDetailsType[] | null>(
    null
  );

  const queryFn = useCallback(async ({ pageParam }: { pageParam: number }) => {
    const url = new URLSearchParams();
    url.set("page", pageParam?.toString());

    return await getData<ApiCallResponseType<FavoriteDetailsType[]>>(
      `/favorites?${url.toString()}`
    );
  }, []);

  const { data, error, refetch } = useInfiniteQuery({
    queryKey: ["favorites"],
    queryFn,
    initialPageParam: 1,
    getNextPageParam: (lastPage) =>
      lastPage?.data?.meta?.nextLink
        ? lastPage?.data?.pagination?.nextPage
        : undefined
  });

  useEffect(() => {
    if (data) {
      const favoritesList = data?.pages?.flatMap(
        (value) => value?.data?.data || []
      );

      setFavorites(favoritesList);
    }
  }, [data]);

  if (error) {
    return (
      <SectionContainer>
        <div className={emptyPageClassName}>
          <ErrorContainer
            error={constructErrorMessage(
              error as ApiErrorResponseType,
              "Unable to fetch favorite products! Please try again later"
            )}
            retryFunction={refetch}
          />
        </div>
      </SectionContainer>
    );
  }

  if (!favorites) {
    return (
      <SectionContainer>
        <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-4 w-full">
          {new Array(9).fill(0).map((_, index) => (
            <ProductCardLoader key={index} />
          ))}
        </div>
      </SectionContainer>
    );
  }

  if (favorites && favorites.length < 1) {
    return (
      <SectionContainer>
        <div className={emptyPageClassName}>
          <EmptyContainer description="No favorite products found" />
        </div>
      </SectionContainer>
    );
  }

  return (
    <SectionContainer contentContainerClassName="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-4 flex-1">
      {favorites.map((favorite) => (
        <ProductCard {...(favorite?.product || {})} key={favorite?.id} />
      ))}
    </SectionContainer>
  );
};

export default Favorites;
