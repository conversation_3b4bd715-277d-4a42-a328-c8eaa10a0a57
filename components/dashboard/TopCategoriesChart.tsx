import React, { useC<PERSON>back, useEffect, useState } from "react";
import {
  <PERSON>,
  <PERSON>hart,
  CartesianGrid,
  XAxis,
  ResponsiveContainer,
  Tooltip,
  TooltipProps
} from "recharts";
import {
  ValueType,
  NameType
} from "recharts/types/component/DefaultTooltipContent";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { ChartContainer } from "@/components/ui/chart";
import { getData } from "@/api";
import { useQuery } from "@tanstack/react-query";
import ErrorContainer from "../status/ErrorContainer";
import { constructErrorMessage } from "@/utils/functions";

export type CategoryGraphData = {
  categoryId: string;
  categoryName: string;
  totalSales: number;
  totalProducts: number;
};

interface TopCategoriesChartProps {
  className?: string;
}

const TopCategoriesChart: React.FC<TopCategoriesChartProps> = ({
  className = ""
}) => {
  const [categoryChartList, setCategoryChartList] = useState<
    CategoryGraphData[] | null
  >(null);

  const queryFn = useCallback(async () => {
    return await getData<ApiCallResponseType<CategoryGraphData[]>>(
      "/dashboard/graph/category"
    );
  }, []);

  const { data, error, refetch } = useQuery({
    queryKey: ["category-chart"],
    queryFn
  });
  // Transform data for chart based on data type
  const chartData = React.useMemo(() => {
    return (categoryChartList || []).map((category) => ({
      name: category.categoryName,
      sales: category.totalSales,
      products: category.totalProducts
    }));
  }, [categoryChartList]);

  const chartConfig = {
    sales: {
      label: "Sales",
      color: "hsl(var(--chart-1))"
    },
    products: {
      label: "Products",
      color: "hsl(var(--chart-4))"
    }
  };

  // Custom tooltip component
  const CustomTooltip = ({
    active,
    payload
  }: TooltipProps<ValueType, NameType>) => {
    if (active && payload && payload.length) {
      const item = payload[0].payload;
      return (
        <div className="bg-background border rounded-lg p-2 shadow-md text-center">
          <p className="text-foreground font-medium">{item.name}</p>
          <p className="text-primary font-semibold">
            Sales: AED{item.sales.toLocaleString()}
          </p>
          <p className="text-[hsl(var(--chart-4))] font-medium">
            Products: {item.products}
          </p>
        </div>
      );
    }
    return null;
  };

  useEffect(() => {
    if (data) {
      setCategoryChartList(data?.data?.data);
    }
  }, [data]);

  return (
    <Card className={className}>
      <CardHeader className="pb-2">
        <div className="flex items-center justify-between">
          <CardTitle>Top Categories</CardTitle>
        </div>
      </CardHeader>
      <CardContent>
        {error && !categoryChartList && (
          <ErrorContainer
            error={constructErrorMessage(
              error as ApiErrorResponseType,
              "Unable to fetch category list! Please retry"
            )}
            retryFunction={refetch}
          />
        )}
        {!categoryChartList && (
          <div className="h-64 flex items-center justify-center">
            <p className="text-gray-500">Loading chart data...</p>
          </div>
        )}
        {categoryChartList && (
          <ChartContainer config={chartConfig} className="h-64">
            <ResponsiveContainer width="100%" height="100%">
              <BarChart data={chartData} barGap={0} barCategoryGap="20%">
                <CartesianGrid vertical={false} />
                <XAxis
                  dataKey="name"
                  tickLine={false}
                  tickMargin={10}
                  axisLine={false}
                  fontSize={12}
                  tickFormatter={(value) =>
                    value.length > 6 ? `${value.slice(0, 6)}...` : value
                  }
                />
                <Tooltip content={<CustomTooltip />} />
                <Bar
                  dataKey="sales"
                  fill="hsl(var(--chart-1))"
                  radius={[4, 4, 0, 0]}
                  barSize={16}
                />
                <Bar
                  dataKey="products"
                  fill="hsl(var(--chart-4))"
                  radius={[4, 4, 0, 0]}
                  barSize={16}
                />
              </BarChart>
            </ResponsiveContainer>
          </ChartContainer>
        )}
      </CardContent>
    </Card>
  );
};

export default TopCategoriesChart;
