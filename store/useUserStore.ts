import { create } from "zustand";

export type Roles = "USER" | "ADMIN";
export interface ShortUserResponse {
  id: string;
  firstName: string;
  lastName: string;
  avatar?: string;
  email?: string;
}
export type UserDetailsType = {
  name: string;
  email: string;
  role: Roles;
  isEmailVerified: boolean;
  createdAt: Date;
  updatedAt: Date;
  isNew?: boolean;
} & ShortUserResponse;

export type UserAddressDetailsType = {
  id: string;
  address: string;
  longitude: number;
  latitude: number;
  createdAt: Date;
  updatedAt: Date;
  landMark?: string;
  city: string;
  state: string;
  country: string;
  firstName: string;
  lastName: string;
  zipCode: string;
  mobileNumber: string;
  email: string;
};

type UserStoreType = {
  userDetails: UserDetailsType | null;
  userToken: string | null;
  fetchingUserDetailsError: string | null;
  userAddresses: UserAddressDetailsType[] | null;
  fetchingAddressError: string | null;
  shouldOpenAddressModal: boolean;
  setShouldOpenAddressModal: (shouldOpenAddressModal: boolean) => void;
  updateAddress: (address: UserAddressDetailsType) => void;
  setAddresses: (userAddresses: UserAddressDetailsType[]) => void;
  setFetchingAddressError: (fetchingAddressError?: string | null) => void;
  setUserDetails: (details: UserDetailsType) => void;
  setUserToken: (token: string) => void;
  setFetchingUserDetailsError: (error: string) => void;
  clearStore: () => void;
};

const initialValue = {
  userDetails: null,
  userToken: null,
  fetchingUserDetailsError: null,
  shouldOpenAddressModal: false,
  userAddresses: null,
  fetchingAddressError: null
};

const useUserStore = create<UserStoreType>((set) => ({
  ...initialValue,
  setUserDetails: (userDetails) => {
    set({ userDetails, fetchingUserDetailsError: null });
  },
  setUserToken: (userToken) => {
    set({ userToken });
  },
  setFetchingUserDetailsError: (fetchingUserDetailsError) => {
    set({ fetchingUserDetailsError });
  },
  setAddresses: (userAddresses) => {
    set({
      userAddresses
    });
  },
  setFetchingAddressError: (fetchingAddressError) => {
    set({
      fetchingAddressError
    });
  },
  setShouldOpenAddressModal: (shouldOpenAddressModal) => {
    set({
      shouldOpenAddressModal
    });
  },
  updateAddress: (address) => {
    set((prevState) => {
      const doesAddressExist = !!(prevState?.userAddresses || [])?.find(
        (availableAddress) => availableAddress.id === address.id
      );
      return {
        ...prevState,
        userAddresses: doesAddressExist
          ? prevState?.userAddresses?.map((availableAddress) =>
              availableAddress?.id === address?.id ? address : availableAddress
            )
          : [address, ...(prevState?.userAddresses || [])]
      };
    });
  },
  clearStore: () => {
    set(initialValue);
  }
}));

export default useUserStore;
