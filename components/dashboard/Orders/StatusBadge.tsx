import { OrderStatusType } from "@/store/useOrderStore";
import React from "react";

interface StatusBadgeProps {
  status: OrderStatusType;
}

const getStatusStyles = (status: OrderStatusType): string => {
  switch (status) {
    case "PAID":
      return "bg-green-100 text-green-700";
    case "PENDING":
      return "bg-orange-100 text-orange-700";
    case "CANCELLED":
      return "bg-blue-100 text-blue-700";
    case "SHIPPED":
      return "bg-cyan-100 text-cyan-700";
    case "DELIVERED":
      return "bg-purple-100 text-purple-700";
    default:
      return "bg-gray-100 text-gray-700";
  }
};

const StatusBadge: React.FC<StatusBadgeProps> = ({ status }) => {
  const styles = getStatusStyles(status);

  return (
    <span
      className={`inline-block px-2 py-0.5 rounded-full text-xs font-medium ${styles}`}
    >
      {status}
    </span>
  );
};

export default StatusBadge;
