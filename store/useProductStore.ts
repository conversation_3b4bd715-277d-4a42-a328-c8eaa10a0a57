import { create } from 'zustand';

export type AmountType = {
	value: number;
	currency: string;
};

export type CategoryDetailsType = {
	id: string;
	name: string;
	totalProducts: number;
	createdAt?: Date;
};

export type AdminProducts = {
	id: string;
	name: string;
	description: string;
	images: string[];
	price: AmountType;
	rating: number;
	category: string;
	quantity: number;
	discount: AmountType | null;
	similarProducts?: AdminProducts[];
};

export type ProductStoreType = {
	categories: CategoryDetailsType[] | null;
	fetchingCategoriesError: string | null;
	setCategories: (categories: CategoryDetailsType[]) => void;
	setFetchingCategoriesError: (error?: string | null) => void;

	products: AdminProducts[] | null;
	fetchingProductsError: string | null;
	setProducts: (products: AdminProducts[]) => void;
	setFetchingProductsError: (error?: string | null) => void;
};

const initialValues = {
	categories: null,
	fetchingCategoriesError: null,
	products: null,
	fetchingProductsError: null,
};

const useProductStore = create<ProductStoreType>((set) => ({
	...initialValues,

	setCategories: (categories) => {
		set({ categories });
	},

	setFetchingCategoriesError: (error = null) => {
		set({ fetchingCategoriesError: error });
	},

	setProducts: (products) => {
		set({ products });
	},

	setFetchingProductsError: (error = null) => {
		set({ fetchingProductsError: error });
	},
}));

export default useProductStore;
