import React from "react";
import DashboardLayout from "@/components/dashboard/DashboardLayout";
import ReviewDetails from "@/components/dashboard/Reviews/ReviewDetails";
import { useRouter } from "next/router";
import isAdmin from "@/hooks/isAdmin";
import Head from "next/head";
import { siteName } from "@/utils/variables";

interface ReviewDetailsPageProps extends React.FC {
  hideFooter?: boolean;
  hideNav?: boolean;
}

const ReviewDetailsPage: ReviewDetailsPageProps = () => {
  const router = useRouter();
  const { reviewId } = router.query;

  return (
    <>
      <Head>
        <title>Review details | Admin | {siteName}</title>
      </Head>
      <DashboardLayout>
        <div className="p-8">
          <ReviewDetails reviewId={reviewId as string} />
        </div>
      </DashboardLayout>
    </>
  );
};

ReviewDetailsPage.hideFooter = true;
ReviewDetailsPage.hideNav = true;

export default isAdmin(ReviewDetailsPage);
