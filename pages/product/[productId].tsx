import { getData } from "@/api";
import SectionContainer from "@/components/layouts/SectionContainer";
import { ProductDetailsType } from "@/components/product/ProductCard";
import ProductDetails from "@/components/product/ProductDetails";
import ProductDetailsBreadCrumbs from "@/components/product/ProductDetailsBreadCrumbs";
import SimilarProducts from "@/components/product/SimilarProducts";
import ErrorContainer from "@/components/status/ErrorContainer";
import { constructErrorMessage } from "@/utils/functions";
import { siteName } from "@/utils/variables";
import { useQuery } from "@tanstack/react-query";
import { GetServerSideProps } from "next";
import Head from "next/head";
import { useParams } from "next/navigation";
import React, { useCallback, useEffect, useState } from "react";

const ProductDetailsPage: React.FC<{
  baseUrl: string;
  product: ProductDetailsType;
}> = ({ baseUrl, product }) => {
  let schemaAvailability = "https://schema.org/InStock";
  if (product?.quantity === 0) {
    schemaAvailability = "https://schema.org/OutOfStock";
  }

  if (product?.quantity < 5) {
    schemaAvailability = "https://schema.org/LimitedAvailability";
  }
  const description = `${product?.description} - ${product?.price?.formatted?.withCurrency} - ${product?.rating} stars`;
  const fullUrl = `${baseUrl}/products/${product?.id}`;
  const [productDetails, setProductDetails] =
    useState<ProductDetailsType | null>(null);
  const params = useParams();
  const { productId } = params || {};
  const queryFn = useCallback(async () => {
    return await getData<ApiCallResponseType<ProductDetailsType>>(
      `/product/${productId}`
    );
  }, [productId]);

  const { data, refetch, error } = useQuery({
    queryKey: [`product-${productId}`],
    queryFn,
    enabled: !!productId
  });

  useEffect(() => {
    if (data) {
      setProductDetails(data?.data?.data);
    }
  }, [data]);

  return (
    <>
      <Head>
        <title>
          {product.name} | {siteName}
        </title>
        <meta name="description" content={description} />
        <meta property="og:title" content={product?.name} />
        <meta property="og:description" content={description} />
        <meta name="twitter:description" content={description} />
        <meta property="og:image" content={product?.images?.[0]} />
        <meta name="twitter:image" content={product?.images?.[0]} />
        <link rel="apple-touch-icon" href={product?.images?.[0]} />

        <meta
          property="og:updated_time"
          content={product?.updatedAt?.toString() || new Date().toString()}
        />
        <meta
          property="og:image:alt"
          content={`${siteName} - ${product?.name}`}
        />
        <meta
          name="twitter:image:alt"
          content={`${siteName} - ${product?.name}`}
        />

        <meta property="og:url" content={fullUrl} />
        <link rel="canonical" href={fullUrl} />
        <script type="application/ld+json">
          {JSON.stringify({
            "@context": "https://schema.org",
            "@type": "Product",
            name: product?.name,
            image: product?.images || [],
            description: description,
            sku: product?.id,
            brand: { "@type": "Brand", name: siteName },
            offers: {
              "@type": "Offer",
              priceCurrency: "AED",
              price: product?.price?.amount,
              availability: schemaAvailability,
              url: fullUrl
            }
          })}
        </script>
      </Head>

      {error && !productDetails && (
        <ErrorContainer
          className="py-40"
          error={constructErrorMessage(
            error as ApiErrorResponseType,
            "Error fetching product details"
          )}
          retryFunction={refetch}
        />
      )}

      {!productDetails && (
        <SectionContainer
          contentContainerClassName="flex items-start gap-10 flex-col md:flex-row"
          className="py-20"
        >
          <div className="w-[30rem] h-[35rem] bg-slate-200 animate-pulse rounded-xl" />

          <div className="flex flex-col flex-1 py-10 gap-6">
            <div className="bg-slate-200 animate-pulse h-2 rounded-full w-full max-w-[20rem]" />
            <div className="gap-1 flex flex-col">
              <div className="bg-slate-200 animate-pulse h-2 rounded-full w-full" />
              <div className="bg-slate-200 animate-pulse h-2 rounded-full w-full" />
              <div className="bg-slate-200 animate-pulse h-2 rounded-full w-full" />
              <div className="bg-slate-200 animate-pulse h-2 rounded-full w-full" />
            </div>
            <div className="gap-1 flex flex-col">
              <div className="bg-slate-200 animate-pulse h-2 rounded-full w-full" />
              <div className="bg-slate-200 animate-pulse h-2 rounded-full w-full" />
              <div className="bg-slate-200 animate-pulse h-2 rounded-full w-full" />
              <div className="bg-slate-200 animate-pulse h-2 rounded-full w-full" />
            </div>
            <div className="gap-1 flex flex-col">
              <div className="bg-slate-200 animate-pulse h-2 rounded-full w-full" />
              <div className="bg-slate-200 animate-pulse h-2 rounded-full w-full" />
              <div className="bg-slate-200 animate-pulse h-2 rounded-full w-full" />
              <div className="bg-slate-200 animate-pulse h-2 rounded-full w-full" />
            </div>
            <div className="bg-slate-200 animate-pulse h-2 rounded-full w-full max-w-[20rem]" />
            <div className="flex items-center gap-4">
              <div className="h-8 w-20 rounded-full bg-slate-200 animate-pulse" />
              <div className="h-8 w-20 rounded-full bg-slate-200 animate-pulse" />
            </div>
          </div>
        </SectionContainer>
      )}

      {productDetails && (
        <>
          <ProductDetailsBreadCrumbs {...productDetails} />
          <ProductDetails {...productDetails} />
          <SimilarProducts {...productDetails} />
        </>
      )}
    </>
  );
};

export default ProductDetailsPage;

export const getServerSideProps: GetServerSideProps<{
  product: ProductDetailsType;
  baseUrl: string;
}> = async (context) => {
  const { params, req } = context || {};
  const { data } = await getData<ApiCallResponseType<ProductDetailsType>>(
    `/product/${params?.productId}`
  );
  const protocol = req.headers["x-forwarded-proto"] || "http";
  const baseUrl = `${protocol}://${req.headers.host}`;

  return {
    props: { product: data?.data, baseUrl }
  };
};
