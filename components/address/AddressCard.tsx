import useCart from "@/hooks/useCart";
import useUser from "@/hooks/useUser";
import { UserAddressDetailsType } from "@/store/useUserStore";
import { Check, EditIcon, Mail, Map, Phone } from "lucide-react";
import React from "react";

const AddressValue: React.FC<{ icon: React.ReactNode; value: string }> = ({
  icon,
  value
}) => {
  return (
    <div className="flex items-center gap-2">
      <span className="text-secondary-300">{icon}</span>
      <span className="opacity-60">{value}</span>
    </div>
  );
};

const AddressCard: React.FC<
  UserAddressDetailsType & { setIsAddressEdit?: (state: boolean) => void }
> = ({
  address,
  firstName,
  lastName,
  mobileNumber,
  email,
  setIsAddressEdit,
  id
}) => {
  const { userAddresses, setShouldOpenAddressModal } = useUser();
  const { setSelectedAddress, selectedAddress } = useCart();
  const addressDetails = userAddresses?.find((address) => address?.id === id);
  const isSelected = selectedAddress?.id === id;
  return (
    <div className="flex flex-col gap-4 p-5 bg-secondary/20 rounded-md">
      <div className="flex items-center gap-10 justify-between">
        <h1 className="font-semibold text-lg">Delivery information</h1>
        {setIsAddressEdit && (
          <button
            title="Edit address"
            aria-label="Edit address"
            onClick={() => {
              setIsAddressEdit(true);
            }}
            className="inline-flex items-center gap-1 text-secondary-300 text-sm mr-10"
          >
            <span>
              <EditIcon size={15} />
            </span>
            <span>Edit</span>
          </button>
        )}

        {!setIsAddressEdit && (
          <button
            title="Edit address"
            type="button"
            aria-label="Edit address"
            onClick={() => {
              if (!addressDetails) {
                return;
              }
              if (isSelected) {
                return;
              }
              setSelectedAddress(addressDetails);
              setShouldOpenAddressModal(false);
            }}
            className="inline-flex items-center gap-1 text-secondary-300 text-sm mr-10"
          >
            {/* <span>
              <EditIcon size={15} />
            </span> */}
            {!isSelected && <span>Select</span>}
            {isSelected && (
              <span>
                <Check />
              </span>
            )}
          </button>
        )}
      </div>
      <div className="flex flex-col gap-2">
        <h2 className="font-semibold">
          {firstName} {lastName}
        </h2>
        <AddressValue icon={<Map size={16} />} value={address} />
        <AddressValue icon={<Phone size={16} />} value={mobileNumber} />
        <AddressValue icon={<Mail size={16} />} value={email} />
      </div>
    </div>
  );
};

export default AddressCard;
