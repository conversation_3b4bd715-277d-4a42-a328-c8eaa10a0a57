import React, { useCallback } from "react";
import { useQuery } from "@tanstack/react-query";
import { getData } from "@/api";
import { OrderDetailsType } from "@/store/useOrderStore";
import ErrorContainer from "../status/ErrorContainer";
import { constructErrorMessage, formatDate } from "@/utils/functions";
import StatusBadge from "./Orders/StatusBadge";

interface RecentOrdersProps {
  className?: string;
}

const RecentOrders: React.FC<RecentOrdersProps> = ({ className = "" }) => {
  const queryFn = useCallback(async () => {
    const data = await getData<ApiCallResponseType<OrderDetailsType[]>>(
      "/orders/admin?limit=5"
    );
    return data?.data?.data;
  }, []);
  const { data, error, refetch } = useQuery({
    queryKey: ["recent-orders"],
    queryFn
  });
  return (
    <div className={`bg-white p-5 rounded-lg shadow-sm ${className}`}>
      <div className="flex items-center justify-between mb-4">
        <h3 className="text-lg font-medium">Recent Orders</h3>
        <button className="text-sm text-blue-600">See All</button>
      </div>

      {error && !data && (
        <ErrorContainer
          retryFunction={refetch}
          error={constructErrorMessage(
            error as ApiErrorResponseType,
            "Unable to fetch recent orders"
          )}
        />
      )}

      {!error && !data && (
        <div className="flex items-center justify-center">
          <p>Loading please wait...</p>
        </div>
      )}

      {data && (
        <div className="space-y-4">
          {data.length > 0 &&
            data.map((order) => (
              <div
                key={order?.id}
                className="flex items-center justify-between"
              >
                <div className="flex items-center space-x-3">
                  {/* <div
                  className={`p-2 rounded-full ${getIconBgColor(order?.type)}`}
                >
                  {getIcon(order?.type)}
                </div> */}
                  <div>
                    <p className="text-sm font-medium">{order?.orderCode}</p>
                    <p className="text-xs text-gray-500">
                      {order?.user?.email} • {formatDate(order?.createdAt)}
                    </p>
                  </div>
                </div>
                {/* {getActionButton(order?.type)} */}
                <StatusBadge status={order?.status} />
              </div>
            ))}
          {data?.length < 1 && (
            <div className="">
              <p>No orders made yet!</p>
            </div>
          )}
        </div>
      )}
    </div>
  );
};

export default RecentOrders;
