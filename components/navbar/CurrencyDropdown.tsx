// CurrencyDropdown.tsx
import React, { useState } from 'react';
import { ChevronDown } from 'lucide-react';
import Image from 'next/image';
import { uk } from '@/assets/svgs'; // Make sure the path is correct

interface Currency {
	code: string;
	symbol: string;
}

interface CurrencyDropdownProps {
	selectedCurrency: Currency;
	setSelectedCurrency: (currency: Currency) => void;
}

const CurrencyDropdown: React.FC<CurrencyDropdownProps> = ({
	selectedCurrency,
	setSelectedCurrency,
}) => {
	const [isDropdownOpen, setIsDropdownOpen] = useState(false);

	const currencies: Currency[] = [
		{ code: 'GB', symbol: '£' },
		{ code: 'US', symbol: '$' },
		{ code: 'NGN', symbol: '₦' },
		{ code: 'FR', symbol: '€' },
	];

	return (
		<div className='relative'>
			{/* Dropdown Toggle Button */}
			<button
				className='flex items-center gap-2 p-2 rounded-md'
				onClick={() => setIsDropdownOpen(!isDropdownOpen)}>
				<Image
					src={uk}
					alt='Uk'
					className='w-6 md:w-5 object-contain'
					width={20} // added width and height
					height={20}
				/>
				<span>
					{selectedCurrency.code} ({selectedCurrency.symbol})
				</span>
				<ChevronDown size={16} />
			</button>

			{/* Dropdown Menu */}
			{isDropdownOpen && (
				<div className='absolute left-0 mt-2 w-32 bg-white shadow-lg rounded-md z-10'>
					{currencies.map((currency) => (
						<button
							key={currency.code}
							className='w-full text-left px-4 py-2 hover:bg-gray-100 flex items-center gap-2'
							onClick={() => {
								setSelectedCurrency(currency);
								setIsDropdownOpen(false);
							}}>
							<Image
								src={uk}
								alt='Uk'
								className='w-20 md:w-5 object-contain'
								width={20} // added width and height
								height={20}
							/>
							{currency.code} ({currency.symbol})
						</button>
					))}
				</div>
			)}
		</div>
	);
};

export default CurrencyDropdown;
