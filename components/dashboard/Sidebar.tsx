import React from "react";
import { useRouter } from "next/router";
import {
  LuLayoutDashboard,
  LuShoppingCart,
  LuPackage,
  LuUsers,
  LuMessageSquare,
  LuSettings
} from "react-icons/lu";
import { RiCoupon3Line } from "react-icons/ri";
import SidebarItem from "./SidebarItem";
import Image from "next/image";
import Link from "next/link";
import { Icon } from "@/assets/images";
import { siteName } from "@/utils/variables";
import { Search } from "lucide-react";

const Sidebar: React.FC = () => {
  const router = useRouter();

  const menuItems = [
    {
      icon: LuLayoutDashboard,
      label: "Dashboard",
      href: "/account",
      isActive: router.pathname === "/account"
    },
    {
      icon: LuPackage,
      label: "Product",
      href: "/account/products",
      isActive: router.pathname === "/account/products"
    },
    {
      icon: LuShoppingCart,
      label: "Orders",
      submenuItems: [
        { label: "All", href: "/account/orders/all" },
        { label: "New", href: "/account/orders/new" },
        { label: "Pending", href: "/account/orders/pending" },
        { label: "Delivered", href: "/account/orders/delivered" },
        { label: "Canceled", href: "/account/orders/cancelled" },
        { label: "Paid", href: "/account/orders/paid" },
        { label: "Shipped", href: "/account/orders/shipped" }
      ]
    },
    {
      icon: RiCoupon3Line,
      label: "Coupons",
      href: "/account/coupons/all"
    },
    {
      icon: LuUsers,
      label: "Customers",
      href: "/account/customers",
      isActive: router.pathname === "/account/customers"
    },
    {
      icon: LuMessageSquare,
      label: "Reviews",
      submenuItems: [
        { label: "All", href: "/account/reviews/all" },
        { label: "New", href: "/account/reviews/new" },
        { label: "Approved", href: "/account/reviews/approved" },
        { label: "Rejected", href: "/account/reviews/disapproved" }
      ]
    },
    {
      icon: LuSettings,
      label: "Settings",
      href: "/account/settings",
      isActive: router.pathname === "/account/settings"
    }
  ];

  return (
    <aside className="fixed w-64 bg-secondary-200 border-r border-gray-100 h-screen p-4">
      <div className="flex-shrink-0 flex items-center justify-between px-5 py-1 w-full mb-5 border-b border-slate-500 pb-5">
        <Link href="/">
          <Image
            src={Icon}
            alt={`${siteName}`}
            className="w-10"
            width={80}
            height={80}
          />
        </Link>
        <p className="text-white text-4xl font-bold">Banafa</p>
      </div>
      <div className="flex items-center w-full md:w-auto">
        <div className="relative flex-1 md:w-[400px]">
          <input
            type="text"
            placeholder="Search here..."
            className="w-full py-2 px-4 pl-10 bg-gray-100 rounded-lg focus:outline-none"
          />
          <Search
            className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400"
            size={18}
          />
        </div>
      </div>
      <nav className="mt-4">
        <ul>
          {menuItems.map((item, index) => (
            <SidebarItem
              key={index}
              icon={item.icon}
              label={item.label}
              href={item.href}
              isActive={item.isActive}
              submenuItems={item.submenuItems || []}
            />
          ))}
        </ul>
      </nav>
    </aside>
  );
};

export default Sidebar;
