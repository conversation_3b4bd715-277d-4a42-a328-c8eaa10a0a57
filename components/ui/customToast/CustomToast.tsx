import React from "react";
import { ToastContainer, Slide } from "react-toastify";

const CustomToast: React.FC = () => {
  return (
    <ToastContainer
      position="top-center"
      autoClose={3000}
      newestOnTop
      limit={1}
      hideProgressBar
      closeOnClick
      pauseOnHover
      draggable
      icon={false}
      closeButton={false}
      transition={Slide}
      className={"z-[99999]"}
      toastClassName={"z-[99999]"}
    />
  );
};

export default CustomToast;
