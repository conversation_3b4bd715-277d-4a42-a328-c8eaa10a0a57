import { CartDetailsType } from "@/store/useCartStore";
import { localStorageKeys } from "@/utils/variables";

export const saveToken = (token: string) => {
  // const encryptedToken = encrypt(token, process.env.NEXT_PUBLIC_TOKEN_ENCRYPTION_KEY ?? "");
  return localStorage.setItem(localStorageKeys.tokenKey, token);
};

export const getSavedToken = () => {
  const savedToken = localStorage.getItem(localStorageKeys.tokenKey);
  // if (savedToken) {
  //   const decryptedToken = decrypt(savedToken, process.env.NEXT_PUBLIC_TOKEN_ENCRYPTION_KEY ?? "");
  //   if (decryptedToken) {
  //     token = decryptedToken?.toString();
  //   }
  // }
  return savedToken ?? null;
};

export const deleteSavedToken = () => {
  return localStorage.removeItem(localStorageKeys.tokenKey);
};

export const getCartsFromLocalStorage = () => {
  const cartList = localStorage.getItem(localStorageKeys.cart);

  return JSON.parse(cartList || "[]") as CartDetailsType[];
};

export const setCartsToLocalStorage = (carts: CartDetailsType[]) => {
  return localStorage.setItem(localStorageKeys.cart, JSON.stringify(carts));
};

export const deleteCartsFromLocalStorage = () => {
  return localStorage.removeItem(localStorageKeys.cart);
};
