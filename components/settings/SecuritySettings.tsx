import React, { useCallback } from "react";
import InputField from "../general/InputField";
import { useForm } from "react-hook-form";
import Button from "../Button";
import { patchData } from "@/api";
import { toast } from "react-toastify";
import { constructErrorMessage } from "@/utils/functions";

const defaultValues = {
  currentPassword: "",
  newPassword: "",
  confirmPassword: ""
};

const SecuritySettings = () => {
  const {
    register,
    handleSubmit,
    watch,
    reset,
    formState: { errors, isSubmitting }
  } = useForm({
    defaultValues,
    mode: "onChange"
  });

  const newPassword = watch("newPassword");
  const password = watch("currentPassword");

  const onSubmit = useCallback(
    async (body: typeof defaultValues) => {
      const { confirmPassword: _, ...dataToSend } = body;

      try {
        await patchData("/user/change-password", dataToSend);
        toast("Password changed successfully!");
        reset(defaultValues);
      } catch (error) {
        toast(
          constructErrorMessage(
            error as ApiErrorResponseType,
            "Unknown error encountered whilst changing password!"
          )
        );
      }
    },
    [reset]
  );

  return (
    <div className="bg-white rounded-lg shadow p-6">
      <h2 className="text-xl font-semibold mb-4">Change Password</h2>
      <form onSubmit={handleSubmit(onSubmit)} className="space-y-4">
        {/* Current Password */}
        <InputField
          label="Current Password"
          id="currentPassword"
          type="password"
          placeholder="Enter your current password"
          {...register("currentPassword", {
            required: "Current password is required"
          })}
          error={errors.currentPassword?.message}
        />

        {/* New Password */}
        <InputField
          label="New Password"
          id="newPassword"
          type="password"
          placeholder="Enter a new password"
          {...register("newPassword", {
            required: "New password is required",
            minLength: {
              value: 8,
              message: "Password must be at least 8 characters"
            },
            pattern: {
              value:
                /^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)(?=.*[@$!%*?&])[A-Za-z\d@$!%*?&]{8,}$/,
              message:
                "Password must include uppercase, lowercase, number and special character"
            },

            validate: (value) =>
              value === password
                ? "Current password and new password can't match"
                : undefined
          })}
          error={errors.newPassword?.message}
        />

        {/* Confirm Password */}
        <InputField
          label="Confirm Password"
          id="confirmPassword"
          type="password"
          placeholder="Confirm your new password"
          {...register("confirmPassword", {
            required: "Please confirm your password",
            validate: (value) =>
              value !== newPassword ? "Passwords do not match" : undefined
          })}
          error={errors.confirmPassword?.message}
        />

        <div className="pt-4">
          <Button
            type="submit"
            variant="primary"
            loading={isSubmitting}
            disabled={isSubmitting}
          >
            {isSubmitting ? "Updating Password..." : "Update Password"}
          </Button>
        </div>
      </form>
    </div>
  );
};

export default SecuritySettings;
