import { setHeaderAuthorization } from "@/api";
import { getSavedToken, saveToken } from "@/localservices";
import useUserStore from "@/store/useUserStore";
import { useCallback } from "react";
import useUser from "./useUser";
import useCart from "./useCart";
import useProduct from "./useProduct";

const useAuth = () => {
  const { fetchCategories } = useProduct();
  const { setUserToken } = useUserStore();
  const { getUserDetails, getUserAddresses } = useUser();
  const { getCart, getLocalStorageCart } = useCart();

  const performAuthOperations = useCallback(
    (token: string, shouldNotSaveToken?: boolean) => {
      if (!token) {
        return;
      }

      try {
        setHeaderAuthorization(token);
        setUserToken(token);
        if (!shouldNotSaveToken) {
          saveToken(token);
        }
        getUserDetails();
        getUserAddresses();
        getCart(token);
      } catch {
        // logoutUser();
        // perform any preaction operations
      }
    },
    [setUserToken, getUserDetails, getCart, getUserAddresses]
  );

  const loadApp = useCallback(async () => {
    try {
      const token = getSavedToken();
      if (token) {
        performAuthOperations(token, false);
      }
      if (!token) {
        getLocalStorageCart();
      }
      fetchCategories();
    } catch {
      // perform error operation
    }
  }, [performAuthOperations, fetchCategories, getLocalStorageCart]);

  return { performAuthOperations, loadApp };
};

export default useAuth;
