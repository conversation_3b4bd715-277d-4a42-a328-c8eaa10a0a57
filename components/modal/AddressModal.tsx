import React, { useEffect } from "react";
import Modal from "../Modal";
import { useRouter } from "next/router";
import useUser from "@/hooks/useUser";
import ErrorContainer from "../status/ErrorContainer";
import AddressCardLoader from "../address/AddressCardLoader";
import { X } from "lucide-react";
import EmptyContainer from "../status/EmptyContainer";
import AddressCard from "../address/AddressCard";

const AddressModal = () => {
  const {
    setShouldOpenAddressModal,
    shouldOpenAddressModal,
    fetchingAddressError,
    getUserAddresses,
    userAddresses,
    userToken
  } = useUser();
  const router = useRouter();

  useEffect(() => {
    if (!userAddresses && userToken && shouldOpenAddressModal) {
      getUserAddresses();
    }
  }, [userAddresses, shouldOpenAddressModal, userToken, getUserAddresses]);

  useEffect(() => {
    setShouldOpenAddressModal(false);
  }, [router, setShouldOpenAddressModal]);

  return (
    <Modal
      opened={shouldOpenAddressModal}
      onClose={() => setShouldOpenAddressModal(false)}
      position="top-right"
      dialogEnterFromAnimation="translate-x-1/2 opacity-0"
      dialogEnterToAnimation="translate-x-0 opacity-100"
      dialogLeaveToAnimation="translate-x-1/2 opacity-0"
    >
      <div className="w-screen h-screen bg-white max-w-[35rem] flex flex-col gap-10 p-5">
        <div className="flex items-center gap-10 justify-between">
          <h1 className="font-bold">All Address</h1>
          <button
            title="cancel"
            className=""
            onClick={() => {
              setShouldOpenAddressModal(false);
            }}
          >
            <X />
          </button>
        </div>
        {fetchingAddressError && (
          <ErrorContainer
            error={fetchingAddressError}
            retryFunction={getUserAddresses}
          />
        )}

        {!fetchingAddressError && !userAddresses && (
          <div className="flex flex-col gap-4">
            {new Array(6).fill(0).map((_, index) => (
              <AddressCardLoader key={index} />
            ))}
          </div>
        )}

        {!fetchingAddressError && userAddresses && (
          <>
            {userAddresses.length < 1 && (
              <EmptyContainer description="No address found" />
            )}

            {userAddresses.length > 0 &&
              userAddresses.map((addressDetails) => (
                <AddressCard {...addressDetails} key={addressDetails?.id} />
              ))}
          </>
        )}
      </div>
    </Modal>
  );
};

export default AddressModal;
