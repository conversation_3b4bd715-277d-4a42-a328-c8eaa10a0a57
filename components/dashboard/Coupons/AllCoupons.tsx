import React, { useState } from "react";
import CouponTable from "./CouponTable";
import CreateCouponModal from "./CreateCouponModal";
import CouponHeader from "./CouponHeader";
import CouponStats from "./CouponStats";
import CouponFilterTabs from "./CouponFilterTabs";

const AllCoupons: React.FC = () => {
  const [isCreateModalOpen, setIsCreateModalOpen] = useState(false);
  const handleOpenCreateModal = () => setIsCreateModalOpen(true);
  const handleCloseCreateModal = () => setIsCreateModalOpen(false);

  return (
    <div className="p-4 md:p-6 lg:p-8 bg-gray-50 min-h-screen">
      <CouponHeader onCreateCoupon={handleOpenCreateModal} />

      <CouponStats />

      <CouponFilterTabs />

      <CouponTable isCreateModalOpen={isCreateModalOpen} />

      <CreateCouponModal
        isOpen={isCreateModalOpen}
        onClose={handleCloseCreateModal}
      />
    </div>
  );
};

export default AllCoupons;
