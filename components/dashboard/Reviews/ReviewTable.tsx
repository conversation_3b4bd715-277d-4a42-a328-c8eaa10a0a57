import React, { useCallback, useEffect, useState } from "react";
import ReviewTableHeader from "./ReviewTableHeader";
import ReviewTableRow, { ReviewDetailsType } from "./ReviewTableRow";
import Button from "@/components/Button";
import { ChevronLeft, ChevronRight, Loader2 } from "lucide-react";
import { useQuery } from "@tanstack/react-query";
import { getData } from "@/api";
import { useRouter } from "next/router";
import { useParams } from "next/navigation";
import ErrorContainer from "@/components/status/ErrorContainer";
import { constructErrorMessage } from "@/utils/functions";

const ReviewTable = () => {
  const colSpan = 20;
  const { query, push, pathname } = useRouter();
  const params = useParams();
  const { status } = params || {};
  const { q, page: queryPage } = query || {};
  const page = parseInt(queryPage?.toString() || "1");
  const [reviews, setReviews] = useState<ReviewDetailsType[] | null>(null);
  const [totalPage, setTotalPage] = useState(0);
  const [selectedReviewsId, setSelectedReviewsId] = useState<string[]>([]);
  const isAllSelected =
    !!reviews &&
    reviews.length > 0 &&
    reviews.length === selectedReviewsId.length;
  const onSelectAllReviews = useCallback(
    (checked: boolean) => {
      if (checked) {
        setSelectedReviewsId((reviews || []).map((review) => review.id));
        return;
      }
      setSelectedReviewsId([]);
    },
    [reviews]
  );
  const queryFn = useCallback(
    async (page: number) => {
      const urlSearchParams = new URLSearchParams();
      urlSearchParams.set("page", page?.toString());
      if (q) {
        urlSearchParams.set("q", q?.toString());
      }
      const statusFromParams = status?.toString();
      if (statusFromParams && statusFromParams !== "all") {
        if (statusFromParams === "new") {
          urlSearchParams.set("new_review", "true");
        }

        if (statusFromParams !== "new") {
          urlSearchParams.set("status", statusFromParams?.toUpperCase());
        }
      }
      return await getData<ApiCallResponseType<ReviewDetailsType[]>>(
        `/reviews?${urlSearchParams?.toString()}`
      );
    },
    [q, status]
  );
  const { data, error, refetch, isLoading } = useQuery({
    queryKey: ["review-list", q, status],
    queryFn: () => queryFn(page || 1)
  });

  const handlePageChange = useCallback(
    (page: number) => {
      push({ pathname, query: { ...query, page } });
    },
    [query, push, pathname]
  );

  const onRowSelect = useCallback((id: string, checked: boolean) => {
    if (checked) {
      setSelectedReviewsId((prevState) => [...prevState, id]);
      return;
    }
    setSelectedReviewsId((prevState) =>
      prevState?.filter((savedId) => id !== savedId)
    );
  }, []);

  useEffect(() => {
    if (data) {
      setReviews(data?.data?.data);
      setTotalPage(data?.data?.pagination?.totalPage);
    }
  }, [data]);

  return (
    <div className="overflow-x-auto">
      <div className="align-middle inline-block min-w-full">
        <div className="shadow border-b border-gray-200 sm:rounded-lg">
          <table className="min-w-full divide-y divide-gray-200">
            <ReviewTableHeader
              isAllSelected={isAllSelected}
              onSelectAllReviews={onSelectAllReviews}
            />
            <tbody className="bg-white divide-y divide-gray-200">
              {error && !reviews && (
                <tr>
                  <td colSpan={colSpan}>
                    <ErrorContainer
                      error={constructErrorMessage(
                        error as ApiErrorResponseType,
                        "Error fetching reviews! Please try again"
                      )}
                      retryFunction={refetch}
                    />
                  </td>
                </tr>
              )}
              {!reviews && !error && (
                <tr>
                  <td colSpan={colSpan} className="py-10 text-center">
                    <div className="flex justify-center items-center">
                      <Loader2 className="h-6 w-6 animate-spin mr-2" />
                      Loading reviews...
                    </div>
                  </td>
                </tr>
              )}
              {reviews &&
                reviews.length > 0 &&
                reviews.map((review) => (
                  <ReviewTableRow
                    key={review.id}
                    review={review}
                    isSelected={selectedReviewsId.includes(review?.id)}
                    onSelect={onRowSelect}
                    refetch={refetch}
                  />
                ))}
              {reviews && reviews.length < 1 && (
                <tr>
                  <td
                    colSpan={colSpan}
                    className="py-10 text-center text-gray-500"
                  >
                    No reviews found.
                  </td>
                </tr>
              )}
            </tbody>
          </table>
        </div>
        {!error && (
          <div className="flex justify-center items-center mt-4 space-x-2">
            <Button
              variant="primary"
              size="small"
              loading={isLoading}
              onClick={() => {
                handlePageChange(page - 1);
              }}
              disabled={page <= 1}
            >
              <ChevronLeft className="h-4 w-4" />
            </Button>
            <span className="text-sm text-gray-700">
              Page {page} of {totalPage}
            </span>
            <Button
              variant="primary"
              size="small"
              loading={isLoading}
              onClick={() => {
                handlePageChange(page + 1);
              }}
              disabled={page >= totalPage}
            >
              <ChevronRight className="h-4 w-4" />
            </Button>
          </div>
        )}
      </div>
    </div>
  );
};

export default ReviewTable;
