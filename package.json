{"name": "banafa-web", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev --turbopack", "build": "next build", "start": "next start", "lint": "next lint"}, "dependencies": {"@headlessui/react": "^2.2.0", "@tanstack/react-query": "^5.71.5", "axios": "^1.7.9", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "date-fns": "^4.1.0", "iconsax-react": "^0.0.8", "lottie-react": "^2.4.1", "lucide-react": "^0.487.0", "next": "15.1.6", "nextjs-progressbar": "^0.0.16", "react": "^19.0.0", "react-dom": "^19.0.0", "react-google-autocomplete": "^2.7.5", "react-hook-form": "^7.54.2", "react-icons": "^5.4.0", "react-toastify": "^11.0.3", "recharts": "^2.15.2", "swiper": "^11.2.2", "tailwind-merge": "^3.2.0", "tailwindcss-animate": "^1.0.7", "use-debounce": "^10.0.4", "zustand": "^5.0.3"}, "devDependencies": {"@eslint/eslintrc": "^3", "@types/node": "^20", "@types/react": "^19", "@types/react-dom": "^19", "eslint": "^9", "eslint-config-next": "15.1.6", "postcss": "^8", "tailwindcss": "^3.4.1", "typescript": "^5"}}