import React from "react";
import Image from "next/image";
import { ArrowRightIcon } from "lucide-react";
import SectionContainer from "../layouts/SectionContainer";
import { CompetitionImage } from "@/assets/images";
import { useRouter } from "next/router";

const Competition = () => {
  const { push } = useRouter();
  return (
    <SectionContainer
      className=""
      contentContainerClassName="flex flex-col mdbetweenlg:flex-row mdbetweenlg:items-center justify-between gap-8"
    >
      <div className="relative w-full mdbetweenlg:flex-1 rounded-lg h-[clamp(28rem,36vw,36rem)] overflow-hidden">
        <Image
          src={CompetitionImage}
          alt="Perfume bottle"
          className="object-cover object-center"
          fill
        />

        <div className="relative w-full h-full z-10 flex flex-col justify-end p-10 mdbetweenlg:pb-24 text-white bg-black/30">
          <h3 className="text-3xl sm:text-4xl font-bold mb-2 leading-tight">
            Free Delivery on all Perfume ordered until November 30
          </h3>
          <p className="mb-4 text-base sm:text-xl opacity-80">
            All the sleekest dress for you to twin with your girlfriends.
          </p>

          <button
            title="Shop now"
            onClick={() => {
              push("/products");
            }}
            className="flex items-center gap-2 text-white"
          >
            Shop now <ArrowRightIcon className="w-5 h-5" />
          </button>
        </div>
      </div>
      <div className="flex flex-col  mdbetweenlg:w-[40%] mdbetweenlg:max-w-[35rem] gap-4">
        <h2 className="text-[2rem,2.5vw,2.5rem] font-bold tracking-tight">
          COMPETITION!
        </h2>
        <h3 className="text-[1.3rem,1.5vw,1.5rem]  font-normal leading-tight">
          WIN A WINTER WELL-BEING BUNDLE WITH GREEN PEOPLE AND FORTHGLADE PET
          FOODS
        </h3>
        <p className="text-gray-600 leading-relaxed">
          Winter is an essential time for self-care, and to help keep everyone
          healthy and happy, we&apos;ve teamed up with our pals at Forth glade
          to bring you a winter wellbeing competition you won&apos;t want to
          miss.
        </p>
        <button
          onClick={() => {
            push("/products");
          }}
          className="flex items-center gap-2 text-secondary-400 hover:text-secondary-300 transition-colors"
        >
          Shop now <ArrowRightIcon className="w-5 h-5" />
        </button>
      </div>
    </SectionContainer>
  );
};

export default Competition;
