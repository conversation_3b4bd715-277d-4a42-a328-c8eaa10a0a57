import React from "react";
import DashboardLayout from "@/components/dashboard/DashboardLayout";
import OrderDetails from "@/components/dashboard/Orders/OrderDetails";
import { useRouter } from "next/router";
import isAdmin from "@/hooks/isAdmin";
import Head from "next/head";
import { siteName } from "@/utils/variables";

interface OrderDetailsPageProps extends React.FC {
  hideFooter?: boolean;
  hideNav?: boolean;
}

const OrderDetailsPage: OrderDetailsPageProps = () => {
  const router = useRouter();
  const { orderId } = router.query;

  return (
    <>
      <Head>
        <title>Orders details | Admin | {siteName}</title>
      </Head>
      <DashboardLayout>
        <div className="p-8">
          <OrderDetails orderId={orderId as string} />
        </div>
      </DashboardLayout>
    </>
  );
};

OrderDetailsPage.hideFooter = true;
OrderDetailsPage.hideNav = true;

export default isAdmin(OrderDetailsPage);
