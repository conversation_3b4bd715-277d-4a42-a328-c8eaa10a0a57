import { siteName } from "@/utils/variables";
import React, { useCallback, useState } from "react";
import Button from "../Button";
import { Minus, Plus } from "lucide-react";
import StarList from "./StarList";
import CustomImage from "../general/CustomImage";
import { FaHeart } from "react-icons/fa";
import { toast } from "react-toastify";
import { postData } from "@/api";
import { constructErrorMessage } from "@/utils/functions";
import Link from "next/link";
import { CartDetailsType } from "@/store/useCartStore";
import useCart from "@/hooks/useCart";
import useUser from "@/hooks/useUser";

const MAX_PRODUCT_NAME_LENGTH = 30;

const trimName = (content: string = "") => {
  return `${content?.slice(0, MAX_PRODUCT_NAME_LENGTH)}${
    content.length > MAX_PRODUCT_NAME_LENGTH ? "..." : ""
  }`;
};

export type ProductDetailsType = {
  id: string;
  name: string;
  description: string;
  images: string[];
  isFavorite: boolean;
  sku?: string;
  price: AmountType;
  rating: number;
  gender: GenderType;
  category: string;
  quantity: number;
  discount: AmountType | null;
  updatedAt: Date;
  createdAt: Date;
  deletedAt: Date;
  similarProducts?: ProductDetailsType[];
};

export type ProductCardProp = {
  className?: string;
  favorite?: boolean;
} & ProductDetailsType;

const ProductCard: React.FC<ProductCardProp> = ({
  className,
  images = [],
  name,
  price,
  favorite,
  quantity: productQuantity,
  id,
  discount,
  rating
}) => {
  const { userToken } = useUser();
  const { updateCart, constructCartDetails, updateLocalStorageCart } =
    useCart();
  const [isAddingToCart, setIsAddingToCart] = useState(false);
  const [quantity, setQuantity] = useState(1);
  const addToCart = useCallback(async () => {
    if (!quantity) {
      return;
    }
    setIsAddingToCart(true);
    if (!userToken) {
      const cartDetails = constructCartDetails(
        { name, price, quantity: productQuantity, id, images },
        quantity
      );
      updateLocalStorageCart(cartDetails);
      toast(`${quantity} ${name} added to cart successfully`, {
        autoClose: 500
      });
      return setIsAddingToCart(false);
    }

    try {
      const { data } = await postData<
        { quantity: number },
        ApiCallResponseType<CartDetailsType>
      >(`/cart/${id}`, {
        quantity
      });
      updateCart(data?.data);
      toast(`${quantity} ${name} added to cart successfully`, {
        autoClose: 500
      });
    } catch (error) {
      toast(
        constructErrorMessage(
          error as ApiErrorResponseType,
          "Unable to add to cart! Please try again later"
        )
      );
    } finally {
      setIsAddingToCart(false);
    }
  }, [
    quantity,
    id,
    name,
    userToken,
    price,
    images,
    constructCartDetails,
    updateLocalStorageCart,
    productQuantity,
    updateCart
  ]);
  return (
    <div
      className={`${className} flex flex-col gap-3 h-[clamp(10rem,27rem,27rem)] group/product cursor-pointer overflow-hidden hover:border rounded-md pb-4 md:pb-0 md:hover:pb-4 border border-slate-100`}
    >
      <Link
        href={`/product/${id}`}
        className="bg-[#D0D0D0] flex-1 transition-all relative duration-300 overflow-hidden border-b border-b-100"
      >
        <CustomImage
          src={images[0] || ""}
          alt={`${siteName}-${name}`}
          fill
          className="object-center object-contain h-full w-full text-black"
        />
        {favorite && (
          <div className="absolute top-2 right-2">
            <FaHeart className="text-gray-600" size={20} />
          </div>
        )}
      </Link>
      <div className="flex flex-col gap-3 mb-0 md:-mb-11 relative group-hover/product:mb-0 duration-300 px-4">
        <div className="flex items-center gap-6 w-full justify-between">
          <div className="flex items-center gap-1 text-xs">
            <StarList rating={rating} />({rating})
          </div>
          <div className="relative">
            {discount && (
              <p className="font-semibold">
                {discount?.formatted?.withCurrency || 0}
              </p>
            )}
            <p
              className={`${
                discount
                  ? "text-xs line-through opacity-50 font-medium absolute"
                  : "font-semibold"
              }`}
            >
              {price?.formatted?.withCurrency || 0}
            </p>
          </div>
        </div>
        <Link
          href={`/product/${id}`}
          className="first-letter:uppercase font-semibold"
        >
          {trimName(name)}
        </Link>
        <div className="flex items-center gap-6 justify-between">
          <div className="flex items-stretch border rounded-sm h-full">
            <button
              aria-label={`Decrease 1 quantity from ${name}`}
              title={`decrease 1 quantity from ${name}`}
              onClick={() => {
                setQuantity((prevState) => {
                  const newQuantity = prevState - 1;
                  if (newQuantity < 1) {
                    return 1;
                  }
                  return newQuantity;
                });
              }}
              className="px-2"
            >
              <Minus size={14} />
            </button>
            <input
              value={quantity?.toString()}
              onChange={(e) => {
                const value = (e?.target as HTMLInputElement)?.value;
                const valueAsNumber = Number(value);

                if (isNaN(valueAsNumber)) {
                  return;
                }

                if (valueAsNumber < 0) {
                  return;
                }
                setQuantity(parseInt(valueAsNumber?.toString()));
              }}
              title={`${name} quantity`}
              className="w-10 bg-transparent text-center align-middle"
            />
            <button
              aria-label={`Increase 1 quantity from ${name}`}
              title={`decrease 1 quantity from ${name}`}
              className="px-2"
              onClick={() => {
                if (productQuantity === quantity) {
                  return toast("You cannot add any more product!");
                }
                setQuantity((prev) => prev + 1);
              }}
            >
              <Plus size={14} />
            </button>
          </div>
          <Button
            disabled={!quantity}
            onClick={addToCart}
            loading={isAddingToCart}
            className="text-xs border-secondary border rounded-sm bg-transparent text-secondary !px-3 h-11"
          >
            {isAddingToCart ? "Adding...." : "Add to cart"}
          </Button>
        </div>
      </div>
    </div>
  );
};

export default ProductCard;
