import { DefaultImage } from "@/assets/images";
import Image, { ImageProps } from "next/image";
import React, { forwardRef, useState } from "react";

const loadErrorClassName = "object-cover object-center h-full w-full";

const CustomImage = forwardRef<HTMLImageElement, ImageProps>(
  ({ src, fill, className, alt, width, height, ...props }, ref) => {
    const [isLoadError, setIsLoadError] = useState(false);
    return (
      <Image
        {...props}
        ref={ref}
        width={isLoadError || !src ? undefined : width}
        height={isLoadError || !src ? undefined : height}
        fill={isLoadError || !src || fill}
        src={isLoadError || !src ? DefaultImage : src}
        alt={alt}
        className={isLoadError || !src ? loadErrorClassName : className}
        onError={() => {
          setIsLoadError(true);
        }}
      />
    );
  }
);

CustomImage.displayName = "Custom image";

export default CustomImage;
