import Button from '@/components/Button';
import { Plus, ListFilter } from 'lucide-react';
import Link from 'next/link';
import React from 'react';

const AdminProductHeader = () => {
	return (
		<div className='flex flex-col md:flex-row md:items-center md:justify-between mb-6'>
			<h1 className='text-2xl font-semibold text-gray-900 mb-4 md:mb-0'>
				Product
			</h1>
			<div className='flex flex-wrap items-center gap-2'>
				<Link href='/account/categories'>
					<Button
						variant='secondary'
						size='small'
						className='flex items-center gap-2 mr-2'>
						<ListFilter className='h-4 w-4' />
						<span>Categories</span>
					</Button>
				</Link>
				<Link href='/account/products/add'>
					<Button
						variant='primary'
						size='small'
						className='flex items-center gap-2'>
						<Plus className='h-4 w-4' />
						<span>Add Product</span>
					</Button>
				</Link>
			</div>
		</div>
	);
};

export default AdminProductHeader;
