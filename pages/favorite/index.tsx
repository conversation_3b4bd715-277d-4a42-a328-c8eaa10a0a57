import BreadCrumbs from "@/components/favorite/BreadCrumbs";
import Favorite from "@/components/favorite/Favorite";
import protectRoute from "@/hooks/protectRoute";
import { siteName } from "@/utils/variables";
import Head from "next/head";
import React from "react";

const favoritePage = () => {
  return (
    <>
      <Head>
        <title>Favorites | {siteName}</title>
      </Head>
      <div>
        <BreadCrumbs />
        <Favorite />
      </div>
    </>
  );
};

export default protectRoute(favoritePage);
