import React, { useEffect, useState } from "react";
import { Search } from "lucide-react";
import { useRouter } from "next/router";
import { useDebouncedCallback } from "use-debounce";
const ReviewSearchBar = () => {
  const [searchText, setSearchText] = useState("");
  const { query, push, pathname } = useRouter();
  const { q } = query || {};

  const processSearch = useDebouncedCallback((search = "") => {
    const { q: _, ...otherQueries } = query || {};
    if (search) {
      push({ pathname, query: { ...otherQueries, q: search, page: "1" } });
      return;
    }
    push({ pathname, query: { ...otherQueries, page: "1" } });
  }, 1000);

  useEffect(() => {
    setSearchText(q?.toString() || "");
  }, [q]);
  return (
    <div className="mb-6 flex flex-col sm:flex-row justify-between items-start sm:items-center">
      <div className="w-full sm:w-auto mb-4 sm:mb-0">
        <div className="relative">
          <input
            type="text"
            placeholder="Search reviews..."
            className="pl-10 pr-4 py-2 border border-gray-300 rounded-md w-full sm:w-80"
            value={searchText}
            onChange={(e) => {
              const value = e?.target?.value;
              setSearchText(value);
              processSearch(value);
            }}
          />
          <Search
            className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400"
            size={18}
          />
        </div>
      </div>
      {/* <div className='flex space-x-2'>
        <select className='border border-gray-300 rounded-md px-3 py-2 text-sm'>
          <option>Filter by Product</option>
          <option>Product 1</option>
          <option>Product 2</option>
          <option>Product 3</option>
        </select>
        <select className='border border-gray-300 rounded-md px-3 py-2 text-sm'>
          <option>Filter by Rating</option>
          <option>5 Stars</option>
          <option>4 Stars</option>
          <option>3 Stars</option>
          <option>2 Stars</option>
          <option>1 Star</option>
        </select>
        <select className='border border-gray-300 rounded-md px-3 py-2 text-sm'>
          <option>Sort by</option>
          <option>Newest First</option>
          <option>Oldest First</option>
          <option>Highest Rating</option>
          <option>Lowest Rating</option>
        </select>
      </div> */}
    </div>
  );
};

export default ReviewSearchBar;
