import React, { useCallback, useEffect } from "react";
import Input<PERSON>ield from "../general/InputField";
import { useForm } from "react-hook-form";
import useUser from "@/hooks/useUser";
import Button from "../Button";
import { patchData } from "@/api";
import { UserDetailsType } from "@/store/useUserStore";
import { toast } from "react-toastify";
import { constructErrorMessage } from "@/utils/functions";

const defaultValues = {
  firstName: "",
  lastName: ""
};

const AccountSettings = () => {
  const { userDetails, setUserDetails } = useUser();
  const {
    register,
    handleSubmit,
    setValue,
    formState: { errors, isSubmitting }
  } = useForm({
    defaultValues
  });

  const onSubmit = useCallback(
    async (body: typeof defaultValues) => {
      try {
        const { data } = await patchData<
          typeof defaultValues,
          ApiCallResponseType<UserDetailsType>
        >("/user", body);
        toast("Account details updated successfully!");
        setUserDetails(data?.data);
      } catch (error) {
        toast(
          constructErrorMessage(
            error as ApiErrorResponseType,
            "Unable to update user details! Please try again later"
          )
        );
      }
    },
    [setUserDetails]
  );

  useEffect(() => {
    if (userDetails) {
      setValue("firstName", userDetails.firstName || "");
      setValue("lastName", userDetails.lastName || "");
    }
  }, [userDetails, setValue]);

  return (
    <div className="bg-white rounded-lg shadow p-6">
      <h2 className="text-xl font-semibold mb-4">Profile Information</h2>
      <form onSubmit={handleSubmit(onSubmit)} className="space-y-4">
        <InputField
          label="First Name"
          id="firstName"
          placeholder="First Name"
          {...register("firstName", {
            required: "First name is required"
          })}
          error={errors.firstName?.message}
        />

        {/* Last Name (editable) */}
        <InputField
          label="Last Name"
          id="lastName"
          placeholder="Last Name"
          // value={userDetails?.lastName || ''}
          {...register("lastName", {
            required: "Last name is required"
          })}
          error={errors.lastName?.message}
        />

        <div className="pt-4">
          <Button
            type="submit"
            variant="primary"
            loading={isSubmitting}
            disabled={isSubmitting}
          >
            {isSubmitting ? "Saving..." : "Save Changes"}
          </Button>
        </div>
      </form>
    </div>
  );
};

export default AccountSettings;
