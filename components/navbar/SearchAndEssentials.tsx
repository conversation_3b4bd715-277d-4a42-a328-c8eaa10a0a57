import React, { useEffect, useState } from "react";
import SectionContainer from "../layouts/SectionContainer";
import Link from "next/link";
import Image from "next/image";
import { siteName } from "@/utils/variables";
import { Search, User, Heart, ShoppingBag, LogOutIcon } from "lucide-react";
// import CurrencyDropdown from "./CurrencyDropdown";
import { Icon } from "@/assets/images";
import { AiOutlineHeart } from "react-icons/ai";
import { IoMenu } from "react-icons/io5";
import MobileNavbar from "./MobileNavbar";
import useUser from "@/hooks/useUser";
import useCart from "@/hooks/useCart";
import { Transition, TransitionChild } from "@headlessui/react";
import { useRouter } from "next/router";
import { useDebouncedCallback } from "use-debounce";

const loggedInMenuButtonClassName =
  "py-2 px-4 text-sm hover:bg-secondary-300 inline-block w-full hover:text-white text-left";
const loggedInMenuButtonContainerClassName =
  "w-full data-[closed]:translate-x-[2rem] duration-500";
const SearchAndEssentials = () => {
  const [searchText, setSearchText] = useState("");
  const { userDetails, userToken, logoutUser } = useUser();
  const router = useRouter();
  const { q } = router?.query || {};
  const [shouldShowLoginMenu, setShouldShowLoginMenu] = useState(false);
  const { carts, setShouldOpenCart, shouldOpenCart } = useCart();
  // State for selected currency
  // const [selectedCurrency, setSelectedCurrency] = useState<{
  //   code: string;
  //   symbol: string;
  // }>({
  //   code: "GB",
  //   symbol: "£"
  // });
  const [isMenuOpen, setIsMenuOpen] = useState<boolean>(false); // Default to false

  const processSearch = useDebouncedCallback((q: string) => {
    router.push({
      pathname: `/products`,
      query: { q }
    });
  }, 1000);

  useEffect(() => {
    if (router) {
      setIsMenuOpen(false);
    }
  }, [router]);

  useEffect(() => {
    const queryFromUrl = q?.toString() || "";
    if (searchText && queryFromUrl !== searchText) {
      processSearch(searchText?.trim());
    }
  }, [searchText, processSearch, q]);

  return (
    <>
      <div>
        {/* Desktop Navbar */}
        <SectionContainer contentContainerClassName="md:grid md:grid-cols-3 hidden gap-6 items-center px-6 py-3">
          {/* Logo */}
          <div className="flex-shrink-0">
            <Link href="/">
              <Image
                src={Icon}
                alt={`${siteName}`}
                className="w-20 md:w-12 object-contain"
                width={80}
                height={80}
              />
            </Link>
          </div>

          {/* Search Input */}
          <div className="flex items-center w-full md:w-auto md:flex-1 justify-center">
            <div className="relative md:w-[400px]">
              <input
                type="text"
                placeholder="Search here..."
                className="w-full py-2 px-4 pl-10 bg-gray-100 rounded-lg focus:outline-none border border-slate-300 focus:border focus:border-primary"
                onChange={(e) => {
                  const value = (e?.target as HTMLInputElement)?.value;
                  setSearchText(value);
                }}
              />
              <Search
                className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400"
                size={18}
              />
            </div>
          </div>

          {/* Currency Dropdown and Icons */}
          <ul className="flex items-center justify-end gap-8">
            {/* <li>
              <CurrencyDropdown
                selectedCurrency={selectedCurrency}
                setSelectedCurrency={setSelectedCurrency}
              />
            </li> */}
            {!userToken && (
              <li>
                <Link href="/auth" className="inline-flex items-center gap-2">
                  <User size={24} />
                  <span>Login / Register</span>
                </Link>
              </li>
            )}
            {userToken && (
              <li>
                <button
                  disabled={!userDetails}
                  onClick={() => {
                    if (!userDetails) {
                      return;
                    }
                    setShouldShowLoginMenu((prevState) => !prevState);
                  }}
                  title="Open menu"
                  className="relative disabled:cursor-not-allowed disabled:opacity-40 inline-flex items-center gap-2"
                >
                  <User size={24} />
                  <span>{userDetails?.name}</span>
                  {shouldShowLoginMenu && (
                    <Transition show={shouldShowLoginMenu} appear>
                      <ul className="inline-flex flex-col w-[10rem] bg-white shadow-md absolute top-[150%] right-0 z-[999] border border-slate-100 data-[closed]:opacity-0 duration-300">
                        {userDetails?.role === "ADMIN" && (
                          <TransitionChild>
                            <li
                              className={loggedInMenuButtonContainerClassName}
                            >
                              <Link
                                className={loggedInMenuButtonClassName}
                                href="/account"
                              >
                                Dashboard
                              </Link>
                            </li>
                          </TransitionChild>
                        )}
                        <TransitionChild>
                          <li className={loggedInMenuButtonContainerClassName}>
                            <Link
                              className={loggedInMenuButtonClassName}
                              href="/orders"
                            >
                              Orders
                            </Link>
                          </li>
                        </TransitionChild>
                        <TransitionChild>
                          <li className={loggedInMenuButtonContainerClassName}>
                            <Link
                              className={loggedInMenuButtonClassName}
                              href="/settings"
                            >
                              Settings
                            </Link>
                          </li>
                        </TransitionChild>
                        <TransitionChild>
                          <li className={loggedInMenuButtonContainerClassName}>
                            <button
                              onClick={() => logoutUser()}
                              className={
                                "inline-flex text-left items-center justify-start w-full py-2 px-4 text-sm gap-2 text-red-600 hover:bg-red-100"
                              }
                              title="Logout"
                            >
                              <span>
                                <LogOutIcon size={13} />
                              </span>
                              <span>Logout</span>
                            </button>
                          </li>
                        </TransitionChild>
                      </ul>
                    </Transition>
                  )}
                </button>
              </li>
            )}
            {userToken && (
              <li>
                <Link
                  href="/favorite"
                  className="inline-flex items-center gap-2"
                >
                  <Heart size={24} />
                  <span>Favorites</span>
                </Link>
              </li>
            )}

            {/* Shopping Bag with Count */}
            <li>
              <button
                title="Open cart"
                aria-label="Open cart"
                className="relative inline-flex items-center gap-2"
                onClick={() => setShouldOpenCart(!shouldOpenCart)}
              >
                <span className="relative">
                  <ShoppingBag size={24} />
                  <span className="absolute -top-2 -right-2 bg-purple-600 text-white text-xs rounded-full size-5 flex items-center justify-center">
                    {carts?.length || 0}
                  </span>
                </span>
                <span>Cart</span>
              </button>
            </li>
          </ul>
        </SectionContainer>

        {/* Mobile Navbar Toggle */}
        <SectionContainer contentContainerClassName="flex md:hidden justify-between items-center px-6 py-2">
          <div className="flex items-center justify-center gap-4">
            <IoMenu
              className="text-3xl cursor-pointer"
              onClick={() => setIsMenuOpen(true)} // Open mobile navbar
            />
            {/* <FiSearch className="text-gray-500 text-3xl cursor-pointer" /> */}
          </div>

          {/* Logo */}
          <div className="flex-shrink-0">
            <Link href="/">
              <Image src={Icon} alt={`${siteName}`} width={40} height={40} />
            </Link>
          </div>

          {/* Heart Icon with Notification Badge */}
          <ul className="flex items-center justify-center gap-6">
            {userToken && (
              <li>
                <Link href="/favorite">
                  <AiOutlineHeart className="text-gray-800 text-2xl cursor-pointer" />
                </Link>
              </li>
            )}
            <li>
              <button
                onClick={() => {
                  setShouldOpenCart(true);
                }}
                className="relative"
              >
                <ShoppingBag className="text-gray-500 text-3xl cursor-pointer" />
                <span className="absolute -top-2 -right-2 bg-secondary-400 text-white text-xs rounded-full w-5 h-5 flex items-center justify-center">
                  {(carts || [])?.length || 0}
                </span>
              </button>
            </li>
          </ul>
        </SectionContainer>
      </div>

      <MobileNavbar isMenuOpened={isMenuOpen} setIsMenuOpen={setIsMenuOpen} />
    </>
  );
};

export default SearchAndEssentials;
