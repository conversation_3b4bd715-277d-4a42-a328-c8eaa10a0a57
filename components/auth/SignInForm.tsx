import { useCallback } from "react";
import Link from "next/link";
import { useRouter } from "next/router";
import InputField from "../general/InputField";
import Button from "../Button";
import { toast } from "react-toastify";
import { postData } from "@/api";
import useAuth from "@/hooks/useAuth";
import { UserDetailsType } from "@/store/useUserStore";
import { useForm } from "react-hook-form";
import { emailRegExp } from "@/utils/regex";
import { OTPType, ResendOTPResponseType } from "@/pages/auth/verify-otp";
import { verificationTypes } from "@/utils/variables";
import { constructErrorMessage } from "@/utils/functions";

type LoginResponse = {
  userDetails: UserDetailsType;
  accessToken: string;
};

const defaultValues = {
  email: "",
  password: ""
};

const SignInForm: React.FC = () => {
  const router = useRouter();
  const { query } = router || {};
  const { redirect } = query || {};
  const { performAuthOperations } = useAuth();

  const {
    register,
    handleSubmit,
    formState: { isSubmitting, errors, isValid }
  } = useForm({
    defaultValues,
    mode: "onChange"
  });

  const resendOTP = useCallback(
    async (email: string, retryCount = 1) => {
      if (retryCount > 5) {
        return toast("Email not verified");
      }
      try {
        const response = await postData<
          { email: string; otpType: OTPType },
          ApiCallResponseType<ResendOTPResponseType>
        >("/auth/otp/resend", {
          email,
          otpType: verificationTypes.emailVerification as OTPType
        });

        const { data } = response?.data;
        if (data?.token) {
          router.push(
            `/auth/verify-otp?token=${data?.token}&email=${email}&type=${verificationTypes?.emailVerification}`
          );
          toast("Please verify your email to continue");
        }
      } catch {
        return await resendOTP(email, retryCount + 1);
      }
    },
    [router]
  );

  const loginUser = useCallback(
    async (body: typeof defaultValues) => {
      try {
        const response = await postData<
          { email: string; password: string },
          ApiCallResponseType<LoginResponse>
        >("/auth/login", body);

        const { data } = response?.data;

        if (data) {
          performAuthOperations(data?.accessToken);
          toast.success("Login successful!");
          if (redirect) {
            return router.push(redirect?.toString());
          }
          router.push("/");
        }
      } catch (error) {
        const apiError = error as ApiErrorResponseType;
        if (apiError?.response?.status === 403) {
          return await resendOTP(body?.email);
        }
        toast.error(
          constructErrorMessage(
            apiError as ApiErrorResponseType,
            "Unable to login! Please try again later"
          )
        );
      }
    },
    [performAuthOperations, router, redirect, resendOTP]
  );

  return (
    <form className="mt-8 space-y-3" onSubmit={handleSubmit(loginUser)}>
      <div className="space-y-1">
        <InputField
          id="email"
          type="email"
          label="Email*"
          className="w-full py-3 text-gray-500 rounded-lg"
          placeholder="Enter your email"
          error={errors?.email?.message}
          {...register("email", {
            required: "Please provide your email",
            pattern: {
              value: emailRegExp,
              message: "Please provide a valid email"
            }
          })}
        />
        <InputField
          id="password"
          type="password"
          label="Password*"
          className="w-full py-3 text-gray-500 rounded-lg"
          placeholder="Enter your password"
          error={errors?.password?.message}
          {...register("password", {
            required: "Please provide your password"
          })}
        />
        <div className="flex items-center justify-end">
          <Link href="/auth/forgot-password" className="text-primary underline">
            Forgot password?
          </Link>
        </div>
      </div>

      <Button
        variant="secondary"
        className="w-full"
        type="submit"
        loading={isSubmitting}
        disabled={isSubmitting || !isValid}
      >
        {isSubmitting ? "Signing In..." : "Sign In"}
      </Button>
    </form>
  );
};

export default SignInForm;
