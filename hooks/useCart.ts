import { getData, postData, putData } from "@/api";
import useCartStore, { CartDetailsType } from "@/store/useCartStore";
import {
  constructErrorMessage,
  constructPriceResponse
} from "@/utils/functions";
import { useCallback } from "react";
import {
  deleteCartsFromLocalStorage,
  getCartsFromLocalStorage,
  setCartsToLocalStorage
} from "@/localservices";
import useUserStore from "@/store/useUserStore";

const useCart = () => {
  const { setCarts, setCartFetchingError, updateCart, ...details } =
    useCartStore();
  const { userToken } = useUserStore();

  const compareCartList = useCallback(
    async (
      cartFromServer: CartDetailsType[],
      token?: string
    ): Promise<CartDetailsType[] | void> => {
      if (!userToken && !token) {
        return;
      }
      const localStorageCartList = getCartsFromLocalStorage();

      const cartToAddToServer: CartDetailsType[] = [];
      const cartToUpdateOnServer: CartDetailsType[] = [];

      localStorageCartList.forEach((cart) => {
        const existingCartOnServer = cartFromServer.find(
          (serverCart) => serverCart?.product?.id == cart?.product?.id
        );

        if (existingCartOnServer) {
          const existingCartOnServerTime = new Date(
            existingCartOnServer?.createdAt
          )?.getTime();
          const presentCartTime = new Date(cart?.createdAt)?.getTime();

          if (presentCartTime < existingCartOnServerTime) {
            return;
          }
          cartToUpdateOnServer.push({
            ...existingCartOnServer,
            quantity: cart?.quantity
          });
          return;
        }
        cartToAddToServer.push(cart);
      });
      const addToCartPromise = cartToAddToServer.map((cart) =>
        postData(`/cart/${cart?.product?.id}`, {
          quantity: cart?.quantity
        })
      );
      const updateCartPromise = cartToUpdateOnServer.map((cart) =>
        putData(`/cart/${cart?.id}`, { quantity: cart?.quantity })
      );
      await Promise.allSettled([...addToCartPromise, ...updateCartPromise]);
      deleteCartsFromLocalStorage();
      const { data } = await getData<ApiCallResponseType<CartDetailsType[]>>(
        "/cart"
      );
      const { data: content } = data;
      return content;
    },
    [userToken]
  );

  const constructCartDetails = (
    product: {
      name: string;
      id: string;
      price: AmountType;
      images: string[];
      quantity: number;
    },
    quantity: number
  ): CartDetailsType => {
    return {
      product: {
        name: product?.name,
        id: product?.id,
        price: product?.price,
        images: product?.images,
        quantity: product?.quantity
      },
      createdAt: new Date(),
      id: `local-cart-${product?.id}`,
      quantity,
      price: constructPriceResponse(product?.price?.amount * quantity),
      updatedAt: new Date(),
      productPrice: product?.price
    };
  };

  const getCart = useCallback(
    async (token?: string) => {
      if (!token && !userToken) {
        return;
      }
      setCartFetchingError();
      setCarts(null);

      try {
        const { data } = await getData<ApiCallResponseType<CartDetailsType[]>>(
          "/cart"
        );
        const { data: content } = data;
        const comparedCarts = await compareCartList(content, token);
        setCarts(comparedCarts || content || []);
      } catch (error) {
        setCartFetchingError(
          constructErrorMessage(
            error as ApiErrorResponseType,
            "Error encountered whilst fetching cart list"
          )
        );
      }
    },
    [setCarts, setCartFetchingError, userToken, compareCartList]
  );

  const getLocalStorageCart = useCallback(() => {
    if (userToken) {
      return;
    }
    const cartList = getCartsFromLocalStorage();
    setCarts(cartList);
  }, [userToken, setCarts]);

  const updateLocalStorageCart = useCallback(
    (cart: CartDetailsType) => {
      if (userToken) {
        return;
      }
      const cartList = getCartsFromLocalStorage();
      const existingCartIndex = cartList.findIndex(
        (item) => item?.product?.id === cart?.product?.id
      );

      if (existingCartIndex !== -1) {
        const newCartList = cartList.map((cartItem) =>
          cart?.product?.id === cartItem?.product?.id
            ? {
                ...cartItem,
                ...cart,
                quantity: (cartItem?.quantity || 0) + (cart?.quantity || 0)
              }
            : cartItem
        );
        setCarts(newCartList);
        setCartsToLocalStorage(newCartList);
        return;
      }

      const newCartList = [...cartList, cart];

      setCartsToLocalStorage(newCartList);
      setCarts(newCartList);
    },
    [userToken, setCarts]
  );

  const updateCartQuantityInLocalStorage = useCallback(
    (productId: string, quantity: number) => {
      if (userToken) {
        return;
      }
      const cartList = getCartsFromLocalStorage();
      const existingCartIndex = cartList.findIndex(
        (item) => item?.product?.id === productId
      );

      if (existingCartIndex === -1) {
        return;
      }
      setCartsToLocalStorage(
        cartList.map((cartItem) => {
          const isCartDetails = productId === cartItem?.product?.id;
          if (!isCartDetails) {
            return cartItem;
          }
          const newCartDetails = {
            ...cartItem,
            quantity,
            updatedAt: new Date(),
            price: constructPriceResponse(
              (cartItem?.product?.price?.amount || 0) * quantity
            )
          };
          updateCart(newCartDetails);
          return newCartDetails;
        })
      );
    },
    [userToken, updateCart]
  );

  const removeCartStoredInLocalStorage = useCallback(
    (productId: string) => {
      if (userToken) {
        return;
      }
      const cartList = getCartsFromLocalStorage();
      const newCartList = cartList?.filter(
        (cart) => cart?.product?.id !== productId
      );
      setCartsToLocalStorage(newCartList);
      setCarts(newCartList);
    },
    [userToken, setCarts]
  );

  return {
    getCart,
    updateCart,
    getLocalStorageCart,
    updateCartQuantityInLocalStorage,
    updateLocalStorageCart,
    constructCartDetails,
    removeCartStoredInLocalStorage,
    ...details
  };
};

export default useCart;
