import React from 'react';
import { ArrowUpDown } from 'lucide-react';

const columns = [
	{ id: 'code', label: 'Coupon Code', sortable: true },
	{ id: 'discountType', label: 'Discount Type', sortable: true },
	{ id: 'discountAmount', label: 'Discount Amount', sortable: true },
	// { id: 'minAmount', label: 'Min Amount', sortable: true },
	{ id: 'expiresAt', label: 'Expires At', sortable: true },
	{ id: 'status', label: 'Status', sortable: true },
	{ id: 'actions', label: 'Actions', sortable: false },
];

interface CouponTableHeaderProps {
	onSort?: (columnId: string) => void;
	sortColumn?: string;
	sortDirection?: 'asc' | 'desc';
}

const CouponTableHeader: React.FC<CouponTableHeaderProps> = ({
	onSort,
	sortColumn,
}) => {
	return (
		<thead className='bg-gray-50'>
			<tr>
				{columns.map((column) => (
					<th
						key={column.id}
						scope='col'
						className='px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider'>
						<div className='flex items-center space-x-1'>
							<span>{column.label}</span>
							{column.sortable && onSort && (
								<button
									onClick={() => onSort(column.id)}
									className='ml-1 focus:outline-none'>
									<ArrowUpDown
										className={`h-4 w-4 ${
											sortColumn === column.id
												? 'text-gray-700'
												: 'text-gray-400'
										}`}
									/>
								</button>
							)}
						</div>
					</th>
				))}
			</tr>
		</thead>
	);
};

export default CouponTableHeader;
