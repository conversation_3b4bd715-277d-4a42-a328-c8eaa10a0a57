import SEO from "@/components/general/SEO";
import "react-toastify/dist/ReactToastify.css";
import ApplicationWrapper from "@/components/layouts/ApplicationWrapper";
import "@/styles/ttchocolates.css";
import "@/styles/globals.css";
import "swiper/css";
import type { AppProps } from "next/app";
import NextNProgress from "nextjs-progressbar";
import CustomToast from "@/components/ui/customToast/CustomToast";
import { useRouter } from "next/router";
import Nav from "@/components/general/Nav";
import Footer from "@/components/general/Footer";
import { QueryClient, QueryClientProvider } from "@tanstack/react-query";
import CartModal from "@/components/modal/CartModal";
import AddressModal from "@/components/modal/AddressModal";

export type AppEngineProps = AppProps & {
  Component: {
    title?: string;
    description?: string;
    image?: string;
    imageDescription?: string;
    locale?: string;
    hideFooter?: boolean;
    hideNav?: boolean;
  };
};

const queryClient = new QueryClient();

export default function App({ Component, pageProps }: AppEngineProps) {
  const { pathname } = useRouter();
  const isAuthRoute = pathname.includes("/auth/");
  const isAccountRoute = pathname.includes("/account");
  return (
    <>
      <ApplicationWrapper>
        <NextNProgress color="#0A434F" height={3.5} />
        <SEO
          title={Component?.title}
          description={Component?.description}
          image={Component?.image}
          imageDescription={Component?.imageDescription}
          locale={Component?.locale}
        />
        <QueryClientProvider client={queryClient}>
          {!isAccountRoute && (
            <>
              {!Component.hideNav && <Nav />}
              <Component {...pageProps} />
              {!Component.hideFooter && !isAuthRoute && <Footer />}
            </>
          )}
          {isAccountRoute && <Component {...pageProps} />}
        </QueryClientProvider>
        <CustomToast />
        <CartModal />
        <AddressModal />
      </ApplicationWrapper>
    </>
  );
}
