import { getData } from "@/api";
import ErrorContainer from "@/components/status/ErrorContainer";
import { constructErrorMessage } from "@/utils/functions";
import { useQuery } from "@tanstack/react-query";
import React, { useCallback, useEffect, useState } from "react";
export type OrderStatType = {
  totalOrderedItems: number;
  totalOrders: number;
  totalDeliveredOrders: number;
  totalCanceledOrders: number;
  totalFulfilledOrders: number;
};
interface StatCardProps {
  title: string;
  value: string;
}

const StatCard: React.FC<StatCardProps> = ({ title, value }) => (
  <div className="bg-white p-4 rounded-lg border border-gray-200">
    <p className="text-sm text-gray-500 mb-1">{title}</p>
    <p className="text-2xl font-semibold text-gray-900">{value}</p>
  </div>
);

const OrderStats = () => {
  const queryFn = useCallback(async () => {
    return await getData<ApiCallResponseType<OrderStatType>>("/orders/stat");
  }, []);
  const [stat, setStat] = useState<OrderStatType | null>(null);
  const { data, error, refetch } = useQuery({
    queryFn,
    queryKey: ["order-stats"]
  });

  useEffect(() => {
    if (data) {
      setStat(data?.data?.data);
    }
  }, [data]);
  return (
    <>
      {error && (
        <ErrorContainer
          retryFunction={refetch}
          error={constructErrorMessage(
            error as ApiErrorResponseType,
            "Error fetching order stats! Please try again"
          )}
        />
      )}
      {((!error && !stat) || stat) && (
        <div className="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-5 gap-4 mb-6">
          <StatCard
            title="Total Orders"
            value={stat?.totalOrders?.toString() || "..."}
          />
          <StatCard
            title="Ordered items over time"
            value={stat?.totalOrderedItems?.toString() || "..."}
          />
          <StatCard
            title="Cancelled"
            value={stat?.totalCanceledOrders?.toString() || "..."}
          />
          <StatCard
            title="Fulfilled orders over time"
            value={stat?.totalFulfilledOrders?.toString() || "..."}
          />
          <StatCard
            title="Delivered orders overtime"
            value={stat?.totalDeliveredOrders?.toString() || "..."}
          />
        </div>
      )}
    </>
  );
};

export default OrderStats;
