import { create } from "zustand";
import { UserAddressDetailsType } from "./useUserStore";

export type CartDetailsType = {
  product?: {
    name: string;
    id: string;
    price: AmountType;
    images: string[];
    quantity: number;
  };
  createdAt: Date;
  id: string;
  quantity: number;
  price: AmountType;
  updatedAt: Date;
  productPrice: AmountType;
};

const removeDuplicateCarts = (
  cartArray: CartDetailsType[]
): CartDetailsType[] => {
  // Create a map to track the highest quantity cart for each product ID
  const cartMap: { [id: string]: CartDetailsType } = {};

  // Loop through each cart item
  for (const cart of cartArray) {
    // Check if the product ID is already in the map
    if (cartMap[cart.id]) {
      // If it's already there, compare the quantities
      if (cart.quantity > cartMap[cart.id].quantity) {
        // Keep the one with the higher quantity
        cartMap[cart.id] = cart;
      }
    } else {
      // If it's not in the map, add the item
      cartMap[cart.id] = cart;
    }
  }

  // Return the values of the cartMap, which now contains only unique cart items with the highest quantity
  return Object.values(cartMap);
};
export type CartStoreType = {
  carts: CartDetailsType[] | null;
  cartFetchingError: string | null;
  isNextCartLoading: boolean;
  shouldOpenCart: boolean;
  selectedAddress: UserAddressDetailsType | null;

  setCarts: (carts: CartDetailsType[] | null) => void;
  removeCart: (id: string) => void;
  setCartFetchingError: (error?: string | null) => void;
  isFetchingNextCart: () => void;
  setShouldOpenCart: (shouldOpenCart: boolean) => void;
  setSelectedAddress: (selectedAddress: UserAddressDetailsType) => void;

  updateCart: (cart: CartDetailsType) => void;
  clearCart: () => void;
};

const initialValue = {
  carts: null,
  cartFetchingError: null,
  isNextCartLoading: false,
  shouldOpenCart: false,
  selectedAddress: null
};

const useCartStore = create<CartStoreType>((set) => ({
  ...initialValue,
  setCarts: (carts = null) => {
    set({
      carts: removeDuplicateCarts(carts || []),
      isNextCartLoading: false,
      cartFetchingError: null
    });
  },
  setCartFetchingError: (cartFetchingError = null) => {
    set({ cartFetchingError, isNextCartLoading: false });
  },
  isFetchingNextCart: () => {
    set({ isNextCartLoading: true, cartFetchingError: null });
  },

  setShouldOpenCart: (shouldOpenCart) => {
    set({ shouldOpenCart });
  },
  setSelectedAddress: (selectedAddress) => {
    set({
      selectedAddress
    });
  },
  updateCart: (cart) => {
    set((prevState) => {
      const previousCarts = prevState.carts;
      let carts: CartDetailsType[] = [];
      if (previousCarts) {
        const cartExist = !!previousCarts.find(
          (savedCart) => savedCart?.id === cart?.id
        );

        if (cartExist) {
          carts = previousCarts?.map((savedCart) =>
            savedCart?.id === cart?.id ? cart : savedCart
          );
        }
        if (!cartExist) {
          carts = [...previousCarts, cart];
        }
      }

      if (!previousCarts) {
        carts = [cart];
      }
      return {
        ...prevState,
        carts: removeDuplicateCarts(carts)
      };
    });
  },

  removeCart: (id: string) => {
    set((prevState) => {
      const previousCarts = prevState.carts;
      if (previousCarts) {
        const carts = previousCarts?.filter((cart) => cart?.id !== id);
        return {
          ...prevState,
          carts: removeDuplicateCarts(carts)
        };
      }
      return prevState;
    });
  },

  clearCart: () => {
    set({
      carts: null,
      cartFetchingError: null,
      isNextCartLoading: false
    });
  }
}));

export default useCartStore;
