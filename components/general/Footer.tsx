import React from "react";
import SectionContainer from "../layouts/SectionContainer";
import { NewsletterSignup } from "../footer/NewsletterSignup";
import { FooterSection } from "../footer/FooterSection";
import { BottomFooter } from "../footer/BottomFooter";
import useProduct from "@/hooks/useProduct";

const Footer: React.FC = () => {
  const { categories } = useProduct();
  const productCategories = (categories || []).map((category) => ({
    label: category?.name,
    href: `/products/${category?.name}`
  }));

  const companyInfo = [
    { label: "Our story", href: "/our-story" },
    { label: "Contact us", href: "/contact" },
    { label: "Careers", href: "/careers" }
  ];

  const customerServices = [
    { label: "Order Status", href: "#" },
    { label: "Shipping & Delivery", href: "#" },
    { label: "Returns", href: "#" },
    { label: "Order Cancellation", href: "#" },
    { label: "Payment Options", href: "#" },
    { label: "Contact Us", href: "#" }
  ];

  return (
    <SectionContainer
      className=""
      contentContainerClassName="mx-auto px-4 py-12 "
    >
      <div className="grid grid-cols-1 md:grid-cols-3 lg:grid-cols-4 gap-8">
        <FooterSection title="Products Categories" links={productCategories} />
        <FooterSection title="Company Information" links={companyInfo} />
        <FooterSection title="Customer Services" links={customerServices} />
        <NewsletterSignup />
      </div>
      <BottomFooter />
    </SectionContainer>
  );
};

export default Footer;
