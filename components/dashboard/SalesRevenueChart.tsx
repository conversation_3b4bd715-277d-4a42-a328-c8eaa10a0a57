import React, { useCallback, useEffect, useState } from "react";
import {
  <PERSON>,
  <PERSON><PERSON>hart,
  CartesianGrid,
  XAxis,
  ResponsiveContainer
} from "recharts";
import {
  <PERSON>,
  CardContent,
  <PERSON><PERSON><PERSON>er,
  <PERSON>Header,
  CardTitle
} from "@/components/ui/card";
import {
  ChartConfig,
  ChartContainer,
  ChartTooltip,
  ChartTooltipContent
} from "@/components/ui/chart";
import { useQuery } from "@tanstack/react-query";
import { getData } from "@/api";
import ErrorContainer from "../status/ErrorContainer";
import { constructErrorMessage } from "@/utils/functions";

export type SalesChartData = {
  month: string;
  totalSales: number;
};
interface SalesRevenueChartProps {
  className?: string;
}

const apiChartConfig = {
  totalSales: {
    label: "Total Sales",
    color: "hsl(var(--chart-1))"
  }
} satisfies ChartConfig;

const SalesRevenueChart: React.FC<SalesRevenueChartProps> = ({
  className = ""
}) => {
  const [salesList, setSalesList] = useState<SalesChartData[] | null>(null);
  const queryFn = useCallback(async () => {
    return await getData<ApiCallResponseType<SalesChartData[]>>(
      "/dashboard/graph/sales"
    );
  }, []);

  const { data, error, refetch } = useQuery({
    queryKey: ["sales-revenue-chart"],
    queryFn
  });

  const chartData = React.useMemo(() => {
    return (salesList || []).map((item) => ({
      month: item.month,
      totalSales: item.totalSales
    }));
  }, [salesList]);

  const totalRevenue = React.useMemo(() => {
    return (salesList || []).reduce((acc, item) => acc + item.totalSales, 0);
  }, [salesList]);

  useEffect(() => {
    if (data) {
      setSalesList(data?.data?.data);
    }
  }, [data]);

  return (
    <Card className={className}>
      <CardHeader>
        <CardTitle>Sales Revenue</CardTitle>
      </CardHeader>
      <CardContent>
        {!salesList && error && (
          <ErrorContainer
            error={constructErrorMessage(
              error as ApiErrorResponseType,
              "Unable to load chart! Please try again"
            )}
            retryFunction={refetch}
          />
        )}
        {!salesList && (
          <div className="h-64 flex items-center justify-center">
            <p className="text-gray-500">Loading chart data...</p>
          </div>
        )}
        {salesList && (
          <ChartContainer config={apiChartConfig} className="h-64">
            <ResponsiveContainer width="100%" height="100%">
              <BarChart data={chartData}>
                <CartesianGrid vertical={false} />
                <XAxis
                  dataKey="month"
                  tickLine={false}
                  tickMargin={10}
                  axisLine={false}
                  tickFormatter={(value) => value.slice(0, 3)}
                />
                <ChartTooltip
                  cursor={false}
                  content={<ChartTooltipContent indicator="dashed" />}
                />
                <Bar
                  dataKey="totalSales"
                  fill="var(--color-totalSales)"
                  radius={[4, 4, 0, 0]}
                  barSize={12}
                />
              </BarChart>
            </ResponsiveContainer>
          </ChartContainer>
        )}
      </CardContent>
      <CardFooter className="flex-col items-start gap-2 text-sm">
        <div className="leading-none text-muted-foreground">
          Total revenue: AED{totalRevenue.toLocaleString()}
        </div>
      </CardFooter>
    </Card>
  );
};

export default SalesRevenueChart;
