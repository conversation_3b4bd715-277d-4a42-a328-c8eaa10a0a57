import { getData, setHeaderAuthorization } from "@/api";
import useUserStore, {
  UserAddressDetailsType,
  UserDetailsType
} from "@/store/useUserStore";
import { constructErrorMessage } from "@/utils/functions";
import { useCallback } from "react";
import { deleteSavedToken } from "@/localservices";
import { toast } from "react-toastify";
import { useRouter } from "next/router";
import { toastIds } from "@/utils/variables";
import useCart from "./useCart";

const useUser = () => {
  const {
    setFetchingUserDetailsError,
    setUserDetails,
    setAddresses,
    setFetchingAddressError,
    clearStore,
    ...details
  } = useUserStore();
  const { setSelectedAddress, clearCart } = useCart();
  const { push } = useRouter();

  const logoutUser = useCallback(
    (isUnauthorized = false) => {
      try {
        push("/");
        setTimeout(() => {
          setHeaderAuthorization();
          clearStore();
          clearCart();
          deleteSavedToken();
          if (isUnauthorized) {
            return toast.error("Please login again!", {
              toastId: toastIds.login
            });
          }
          toast.success("Logged out successfully!", {
            toastId: toastIds.login
          });
        }, 100);
      } catch (error) {
        // perform any preaction operations
      }
    },
    [clearStore, push, clearCart]
  );

  const validateError = useCallback(
    (error: ApiErrorResponseType) => {
      if (error?.status === 401) {
        logoutUser(true);
      }
    },
    [logoutUser]
  );

  const getUserDetails = useCallback(async () => {
    try {
      const { data } = await getData<ApiCallResponseType<UserDetailsType>>(
        "/user"
      );
      const { data: userDetails } = data;
      setUserDetails(userDetails);
    } catch (error) {
      validateError(error as ApiErrorResponseType);
      setFetchingUserDetailsError(
        constructErrorMessage(
          error as ApiErrorResponseType,
          "Unknown error occurred whilst fetching user details"
        )
      );
    }
  }, [setFetchingUserDetailsError, setUserDetails, validateError]);

  const getUserAddresses = useCallback(async () => {
    setFetchingAddressError();
    try {
      const { data } = await getData<
        ApiCallResponseType<UserAddressDetailsType[]>
      >("/addresses");
      const addresses = data?.data;
      const firstAddress = addresses?.[0];
      setAddresses(addresses);
      if (firstAddress) {
        setSelectedAddress(firstAddress);
      }
    } catch (error) {
      setFetchingAddressError(
        constructErrorMessage(
          error as ApiErrorResponseType,
          "Error encountered whilst loading addresses"
        )
      );
    }
  }, [setFetchingAddressError, setAddresses, setSelectedAddress]);

  return {
    getUserDetails,
    logoutUser,
    setUserDetails,
    getUserAddresses,
    ...details
  };
};

export default useUser;
