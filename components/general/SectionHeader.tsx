import React from "react";

type SectionHeaderProp = {
  title: string;
  rightContent?: React.ReactNode;
  className?: string;
};

const SectionHeader: React.FC<SectionHeaderProp> = ({
  title,
  rightContent,
  className
}) => {
  return (
    <div className="flex items-center justify-between gap-10">
      <h1 className={`${className} font-bold text-lg md:text-xl max-w-[15rem]`}>
        {title}
      </h1>
      {rightContent}
    </div>
  );
};

export default SectionHeader;
