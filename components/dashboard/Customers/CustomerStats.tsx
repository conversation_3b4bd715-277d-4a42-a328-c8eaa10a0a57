import React, { useCallback, useEffect, useMemo, useState } from "react";
import { Users, UserPlus, Banknote } from "lucide-react";
import { useQuery } from "@tanstack/react-query";
import { getData } from "@/api";
import ErrorContainer from "@/components/status/ErrorContainer";
import { constructErrorMessage } from "@/utils/functions";

export type UsersStat = {
  totalUsers: number | null;
  newCustomers: number | null;
  averageOrder: number | null;
};

const CustomerStats = () => {
  const [stats, setStats] = useState<UsersStat | null>(null);
  const queryFn = useCallback(async () => {
    return await getData<ApiCallResponseType<UsersStat>>("/users/stat");
  }, []);
  const { data, error, refetch } = useQuery({
    queryKey: ["user-stats"],
    queryFn
  });
  const statItems = useMemo(
    () => [
      {
        label: "Total Customers",
        value: stats ? stats?.totalUsers : "...",
        icon: <Users className="h-5 w-5 text-blue-500" />,
        bgColor: "bg-blue-50",
        textColor: "text-blue-700"
      },

      {
        label: "New This Week",
        value: stats ? stats?.newCustomers : "...",
        icon: <UserPlus className="h-5 w-5 text-purple-500" />,
        bgColor: "bg-purple-50",
        textColor: "text-purple-700"
      },
      {
        label: "Avg. Order Value",
        value: stats ? `AED${stats?.averageOrder || 0}` : "...",
        icon: <Banknote className="h-5 w-5 text-yellow-500" />,
        bgColor: "bg-yellow-50",
        textColor: "text-yellow-700"
      }
    ],
    [stats]
  );

  useEffect(() => {
    if (data) {
      setStats(data?.data?.data);
    }
  }, [data]);

  return (
    <div className="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 gap-4 mb-6">
      {!stats && error && (
        <ErrorContainer
          error={constructErrorMessage(
            error as ApiErrorResponseType,
            "Error encountered whilst fetching stats!"
          )}
          retryFunction={refetch}
        />
      )}
      {((!stats && !error) || stats) &&
        statItems.map((item) => (
          <div
            key={item.label}
            className="bg-white p-4 rounded-lg shadow-sm border border-gray-100"
          >
            <div className="flex items-center">
              <div className={`p-2 rounded-full ${item.bgColor} mr-3`}>
                {item.icon}
              </div>
              <div>
                <p className="text-sm text-gray-500">{item.label}</p>
                <p className={`text-xl font-semibold ${item.textColor}`}>
                  {item.value}
                </p>
              </div>
            </div>
          </div>
        ))}
    </div>
  );
};

export default CustomerStats;
