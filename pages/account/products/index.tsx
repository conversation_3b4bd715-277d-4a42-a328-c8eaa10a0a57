// pages/account/dashboard.tsx
import React from "react";
import DashboardLayout from "@/components/dashboard/DashboardLayout";
import AdminProduct from "@/components/dashboard/AdminProduct/AdminProduct";
import isAdmin from "@/hooks/isAdmin";
import Head from "next/head";
import { siteName } from "@/utils/variables";

interface ProductPageProps extends React.FC {
  hideFooter?: boolean;
  hideNav?: boolean;
}

const ProductPage: ProductPageProps = () => {
  return (
    <>
      <Head>
        <title>Products | Admin | {siteName}</title>
      </Head>
      <DashboardLayout>
        <div className="p-8">
          <AdminProduct />
        </div>
      </DashboardLayout>
    </>
  );
};
ProductPage.hideFooter = true;
ProductPage.hideNav = true;
export default isAdmin(ProductPage);
