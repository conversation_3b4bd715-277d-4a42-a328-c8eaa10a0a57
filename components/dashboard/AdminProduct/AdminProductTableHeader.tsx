// src/components/admin/product/AdminProductTableHeader.tsx

import React from 'react';
import { ArrowUpDown } from 'lucide-react';

// Updated columns based on requirements
const columns = [
	{ id: 'product', label: 'Product', sortable: true }, // Displays Name/Image/ID
	{ id: 'price', label: 'Price', sortable: true },
	{ id: 'category', label: 'Category', sortable: true },
	{ id: 'stock', label: 'Stock', sortable: true },
	{ id: 'status', label: 'Status', sortable: false },
	// Description might be too wide for a standard column, often viewed in details/modal
];

const AdminProductTableHeader = () => {
	return (
		<thead className='bg-gray-50'>
			<tr>
				{/* Select All Checkbox
				<th
					scope='col'
					className='pl-4 pr-2 py-3 text-left w-12'>
					<CheckBox
						checked={isAllSelected}
						onChange={(e) =>
							onSelectAll((e.target as HTMLInputElement).checked)
						}
						aria-label='Select all products' // Updated label
					/>
				</th> */}

				{/* Data Columns */}
				{columns.map((col) => (
					<th
						key={col.id}
						scope='col'
						// Adjust width distribution as needed
						className={`px-3 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider ${
							col.id === 'product' ? 'w-2/5' : '' // Example width allocation
						} ${col.id === 'price' || col.id === 'stock' ? 'w-1/6' : ''}
                           ${col.id === 'category' ? 'w-1/5' : ''}`}>
						<button className='group inline-flex items-center'>
							{col.label}
							{col.sortable && (
								<ArrowUpDown className='ml-1 h-3 w-3 text-gray-400 opacity-0 group-hover:opacity-100 transition-opacity' />
							)}
						</button>
					</th>
				))}

				{/* Actions Column Header (Empty) */}
				<th
					scope='col'
					className='relative pl-3 pr-4 py-3 w-12'>
					<span className='sr-only'>Actions</span>
				</th>
			</tr>
		</thead>
	);
};

export default AdminProductTableHeader;
