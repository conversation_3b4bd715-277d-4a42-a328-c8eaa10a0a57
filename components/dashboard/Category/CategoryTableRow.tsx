import React, { useCallback, useState } from "react";
import { Edit, Trash2 } from "lucide-react";
import CheckBox from "@/components/general/CheckBox";
import { CategoryDetailsType } from "@/store/useProductStore";
import DeleteCategoryModal from "./DeleteCategoryModal";
import AddCategoryModal from "./AddCategoryModal";
import { format } from "date-fns";
import useProduct from "@/hooks/useProduct";

interface CategoryTableRowProps {
  category: CategoryDetailsType;
  isSelected: boolean;
  onSelect: (id: string, checked: boolean) => void;
}

const CategoryTableRow: React.FC<CategoryTableRowProps> = ({
  category,
  isSelected,
  onSelect
}) => {
  const { fetchCategories } = useProduct();
  const [isDeleteModalOpen, setIsDeleteModalOpen] = useState(false);
  const [isEditModalOpen, setIsEditModalOpen] = useState(false);

  const handleOpenDeleteModal = useCallback(() => {
    setIsDeleteModalOpen(true);
  }, []);

  const handleCloseDeleteModal = useCallback(() => {
    setIsDeleteModalOpen(false);
  }, []);

  const handleOpenEditModal = useCallback(() => {
    setIsEditModalOpen(true);
  }, []);

  const handleCloseEditModal = useCallback(() => {
    setIsEditModalOpen(false);
  }, []);

  const handleCategoryUpdated = useCallback(() => {
    handleCloseEditModal();
    fetchCategories();
  }, [fetchCategories, handleCloseEditModal]);

  const handleCategoryDeleted = useCallback(() => {
    handleCloseDeleteModal();
    fetchCategories();
  }, [fetchCategories, handleCloseDeleteModal]);

  return (
    <>
      <tr className="hover:bg-gray-50">
        <td className="px-4 py-4 whitespace-nowrap">
          <CheckBox
            checked={isSelected}
            onChange={(e: React.ChangeEvent<HTMLInputElement>) =>
              onSelect(category.id, e.target.checked)
            }
            className="h-4 w-4"
          />
        </td>
        <td className="px-4 py-4 whitespace-nowrap">
          <div className="text-sm font-medium text-gray-900">
            {category.name}
          </div>
        </td>
        <td className="px-4 py-4 whitespace-nowrap">
          <div className="text-sm text-gray-500">
            {format(category?.createdAt || new Date(), "EEE dd, MMMM yyyy")}
          </div>
        </td>
        <td className="px-4 py-4 whitespace-nowrap">
          <div className="text-sm text-gray-500">{category?.totalProducts}</div>
        </td>
        <td className="px-4 py-4 whitespace-nowrap text-right text-sm font-medium">
          <div className="flex justify-end space-x-2">
            <button
              onClick={handleOpenEditModal}
              className="text-indigo-600 hover:text-indigo-900"
            >
              <Edit className="h-4 w-4" />
              <span className="sr-only">Edit</span>
            </button>
            <button
              onClick={handleOpenDeleteModal}
              className="text-red-600 hover:text-red-900"
            >
              <Trash2 className="h-4 w-4" />
              <span className="sr-only">Delete</span>
            </button>
          </div>
        </td>
      </tr>

      {/* Modal Container with fixed positioning for centering */}
      {(isDeleteModalOpen || isEditModalOpen) && (
        <div className="fixed inset-0 z-50 flex items-center justify-center bg-black bg-opacity-50">
          {isDeleteModalOpen && (
            <div className="bg-white rounded-lg shadow-xl overflow-hidden max-w-md w-full">
              <DeleteCategoryModal
                opened={isDeleteModalOpen}
                onClose={handleCloseDeleteModal}
                category={category}
                onCategoryDeleted={handleCategoryDeleted}
              />
            </div>
          )}

          {isEditModalOpen && (
            <div className="bg-white rounded-lg shadow-xl overflow-hidden max-w-md w-full">
              <AddCategoryModal
                opened={isEditModalOpen}
                onClose={handleCloseEditModal}
                category={category}
                onCategoryAdded={handleCategoryUpdated}
                isEditing={true}
              />
            </div>
          )}
        </div>
      )}
    </>
  );
};

export default CategoryTableRow;
