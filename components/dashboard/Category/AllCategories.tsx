import React, { useState, useEffect, useCallback } from "react";
import CategoryHeader from "./CategoryHeader";
import CategoryTable from "./CategoryTable";
import useProduct from "@/hooks/useProduct";

const AllCategories: React.FC = () => {
  const [selectedRows, setSelectedRows] = useState<Set<string>>(new Set());
  const { fetchCategories, categories, fetchingCategoriesError } = useProduct();

  // Fetch categories with product counts from API
  useEffect(() => {
    if (!categories) {
      fetchCategories();
    }
  }, [fetchCategories, categories]);

  const handleRowSelect = useCallback((id: string, checked: boolean) => {
    setSelectedRows((prev) => {
      const newSelected = new Set(prev);
      if (checked) {
        newSelected.add(id);
      } else {
        newSelected.delete(id);
      }
      return newSelected;
    });
  }, []);

  const handleSelectAll = useCallback(
    (checked: boolean) => {
      if (checked) {
        const allIds = (categories || []).map((category) => category.id);
        setSelectedRows(new Set(allIds));
      } else {
        setSelectedRows(new Set());
      }
    },
    [categories]
  );

  return (
    <div className="p-4 md:p-6 lg:p-8 bg-gray-50 min-h-screen">
      <CategoryHeader onCategoryAdded={fetchCategories} />

      {fetchingCategoriesError && (
        <div className="bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded mb-4">
          <p>{fetchingCategoriesError}</p>
        </div>
      )}

      {!categories && !fetchingCategoriesError && (
        <div className="bg-blue-100 border border-blue-400 text-blue-700 px-4 py-3 rounded mb-4">
          <p>Loading categories...</p>
        </div>
      )}

      {categories && (
        <CategoryTable
          categories={categories}
          selectedRows={selectedRows}
          onRowSelect={handleRowSelect}
          onSelectAll={handleSelectAll}
        />
      )}
    </div>
  );
};

export default AllCategories;
