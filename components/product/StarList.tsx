import React from "react";
import { FaStar } from "react-icons/fa";
import { FaStarHalfStroke } from "react-icons/fa6";

type StarListProp = {
  rating?: number;
};

const StarList: React.FC<StarListProp> = ({ rating = 5 }) => {
  const maxRating = 5;

  return (
    <div className="flex items-center gap-1 text-base">
      {new Array(maxRating).fill(0).map((_, index) => {
        const remainingCount = rating - index;
        const isEmpty = remainingCount < 0;
        const isHalf = remainingCount > 0 && remainingCount < 1;
        const isFull = !isEmpty && !isHalf;
        let colorClassName = "text-slate-300";

        if (!isEmpty) {
          colorClassName = "text-tetiary";
        }
        return (
          <span key={index} className={`${colorClassName}`}>
            {(isFull || isEmpty) && <FaStar />}
            {isHalf && <FaStarHalfStroke />}
          </span>
        );
      })}
    </div>
  );
};

export default StarList;
