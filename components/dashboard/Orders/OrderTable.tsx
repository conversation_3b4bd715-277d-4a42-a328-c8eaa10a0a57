// src/components/orders/OrderTable.tsx
import React, { useCallback, useEffect, useState } from "react";
import OrderTableHeader from "./OrderTableHeader";
import OrderTableRow from "./OrderTableRow";
import { OrderDetailsType } from "@/store/useOrderStore";
import { useQuery } from "@tanstack/react-query";
import { getData } from "@/api";
import { useParams } from "next/navigation";
import { useRouter } from "next/router";
import Button from "@/components/Button";
import { ChevronLeft, ChevronRight, Loader2 } from "lucide-react";
import ErrorContainer from "@/components/status/ErrorContainer";
import { constructErrorMessage } from "@/utils/functions";

const OrderTable = () => {
  const colSpan = 20;
  const [orders, setOrders] = useState<OrderDetailsType[] | null>(null);
  const [selectedOrderIds, setSelectedOrderIds] = useState<string[]>([]);
  const [totalPage, setTotalPage] = useState(0);
  const params = useParams();
  const { status } = params || {};
  const { query, push, pathname } = useRouter();
  const { page: queryPage, q } = query || {};
  const page = parseInt(queryPage?.toString() || "1");
  const isAllSelected =
    !!orders &&
    orders?.length > 0 &&
    selectedOrderIds?.length === orders?.length;

  const fetchAllOrders = useCallback(
    async (page: number) => {
      const urlSearchParams = new URLSearchParams();
      urlSearchParams.set("page", page?.toString());
      setOrders(null);
      if (q) {
        urlSearchParams.set("q", q?.toString());
      }

      if (status) {
        if (status === "new") {
          urlSearchParams.set("new_user", "true");
        }

        if (status !== "new" && status !== "all") {
          urlSearchParams.set("status", status?.toString());
        }
      }
      return await getData<ApiCallResponseType<OrderDetailsType[]>>(
        `/orders/admin?${urlSearchParams?.toString()}`
      );
    },
    [q, status]
  );

  const { data, error, refetch, isLoading } = useQuery({
    queryKey: ["orders-list"],
    queryFn: () => fetchAllOrders(page)
  });

  const onSelectAllOrder = useCallback(
    (checked: boolean) => {
      if (!orders) {
        return;
      }
      if (checked) {
        setSelectedOrderIds(orders?.map((order) => order?.id));
        return;
      }
      setSelectedOrderIds([]);
    },
    [orders]
  );

  const onRowSelect = useCallback((id: string, checked: boolean) => {
    if (checked) {
      setSelectedOrderIds((prevState) => [...prevState, id]);
      return;
    }

    setSelectedOrderIds((prevState) =>
      prevState?.filter((prevStateId) => prevStateId !== id)
    );
  }, []);

  const handlePageChange = useCallback(
    (page: number) => {
      push({ pathname, query: { ...query, page } });
    },
    [query, push, pathname]
  );

  useEffect(() => {
    if (data) {
      setOrders(data?.data?.data);
      setTotalPage(data?.data?.pagination?.totalPage);
    }
  }, [data]);

  return (
    <div className="overflow-x-auto">
      <div className="align-middle inline-block min-w-full">
        <div className="shadowborder-b border-gray-200 sm:rounded-lg">
          <table className="min-w-full divide-y divide-gray-200">
            <OrderTableHeader
              isAllSelected={isAllSelected}
              onSelectAllOrder={onSelectAllOrder}
            />
            <tbody className="bg-white divide-y divide-gray-200">
              {!orders && !error && (
                <tr>
                  <td
                    colSpan={colSpan}
                    className="px-4 py-10 text-center text-sm text-gray-500"
                  >
                    <div className="flex justify-center items-center">
                      <Loader2 className="h-6 w-6 animate-spin mr-2" />
                      Loading orders...
                    </div>
                  </td>
                </tr>
              )}
              {orders &&
                orders.length > 0 &&
                orders.map((order) => (
                  <OrderTableRow
                    key={order.id} // Use a truly unique ID here if orderId can repeat
                    order={order}
                    isSelected={selectedOrderIds.includes(order?.id)}
                    onSelect={onRowSelect}
                    refetch={refetch}
                  />
                ))}
              {error && !orders && (
                <tr>
                  <td
                    colSpan={colSpan}
                    className="px-4 py-10 text-center text-sm"
                  >
                    <ErrorContainer
                      error={constructErrorMessage(
                        error as ApiErrorResponseType,
                        "Error loading coupons! Please try again"
                      )}
                      retryFunction={refetch}
                    />
                  </td>
                </tr>
              )}
              {orders && orders.length < 1 && (
                <tr>
                  <td
                    colSpan={colSpan}
                    className="px-4 py-10 text-center text-sm text-gray-500"
                  >
                    No orders found.
                  </td>
                </tr>
              )}
            </tbody>
          </table>
        </div>
        {!error && (
          <div className="flex justify-center items-center mt-4 space-x-2">
            <Button
              variant="primary"
              size="small"
              loading={isLoading}
              onClick={() => {
                handlePageChange(page - 1);
              }}
              disabled={page <= 1}
            >
              <ChevronLeft className="h-4 w-4" />
            </Button>
            <span className="text-sm text-gray-700">
              Page {page} of {totalPage}
            </span>
            <Button
              variant="primary"
              size="small"
              loading={isLoading}
              onClick={() => {
                handlePageChange(page + 1);
              }}
              disabled={page >= totalPage}
            >
              <ChevronRight className="h-4 w-4" />
            </Button>
          </div>
        )}
      </div>
    </div>
  );
};

export default OrderTable;
