import { postData } from "@/api";
import AuthHeader from "@/components/auth/AuthHeader";
import Button from "@/components/Button";
import InputField from "@/components/general/InputField";
import { constructErrorMessage } from "@/utils/functions";
import { useRouter } from "next/router";
import React, { useCallback, useEffect, useRef, useState } from "react";
import { toast } from "react-toastify";

const OTP_LENGTH = 6;
const RESEND_TIMEOUT = 60; // 60 seconds = 1 minute

export type OTPType =
  | "EMAIL_VERIFICATION"
  | "FORGOT_PASSWORD"
  | "CHANGE_EMAIL_VERIFICATION";
// Updated to match API expectations
export type VerifyOTPBodyType = {
  token: string;
  otp: string;
  otpType: OTPType;
};

type VerifyOTPResponseType = {
  message: string;
  token?: string;
  redirectUrl?: string;
};

export type ResendOTPResponseType = {
  message: string;
  token: string;
};

const VerifyOTP = () => {
  const { query, push } = useRouter();
  const { token: initialToken, email, type, redirect } = query || {};
  const [currentToken, setCurrentToken] = useState<string | null>(null);
  const [otp, setOtp] = useState(new Array(OTP_LENGTH).fill(""));
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [resendCountdown, setResendCountdown] = useState(0);
  const inputFieldsRef = useRef<HTMLInputElement[]>([]);
  const timerRef = useRef<NodeJS.Timeout | null>(null);

  // Initialize currentToken with the URL token when available
  useEffect(() => {
    if (initialToken && typeof initialToken === "string") {
      setCurrentToken(initialToken);
    }
  }, [initialToken]);

  // Convert type parameter to otpType as expected by API

  useEffect(() => {
    // Focus first input field when component mounts
    if (inputFieldsRef.current[0]) {
      inputFieldsRef.current[0].focus();
    }

    // Clean up timer when component unmounts
    return () => {
      if (timerRef.current) {
        clearInterval(timerRef.current);
      }
    };
  }, []);

  // Countdown timer effect
  useEffect(() => {
    if (resendCountdown > 0) {
      timerRef.current = setInterval(() => {
        setResendCountdown((prev) => {
          if (prev <= 1) {
            if (timerRef.current) {
              clearInterval(timerRef.current);
            }
            return 0;
          }
          return prev - 1;
        });
      }, 1000);
    }

    return () => {
      if (timerRef.current) {
        clearInterval(timerRef.current);
      }
    };
  }, [resendCountdown]);

  const inputOTPValue = useCallback((value: string, index: number) => {
    const valueToSave = value.slice(0, 1);
    setOtp((prev) => {
      const newArray = prev.map((item, i) => {
        if (i === index) {
          return valueToSave;
        }
        return item;
      });

      return newArray;
    });

    const previousElement = inputFieldsRef.current[index - 1];
    const nextElement = inputFieldsRef.current[index + 1];

    if (value.length > 0 && nextElement) {
      nextElement.focus();
    }

    if (value.length < 1 && previousElement) {
      previousElement.focus();
    }
  }, []);

  const verifyOTP = useCallback(async () => {
    if (!currentToken) {
      toast.error("Missing required token. Please try again.");
      return;
    }

    const otpValue = otp.join("");

    if (otpValue.length !== OTP_LENGTH) {
      toast.error(`Please enter a ${OTP_LENGTH}-digit OTP code.`);
      return;
    }

    setIsSubmitting(true);

    try {
      // Updated request body to match API expectations
      const response = await postData<
        VerifyOTPBodyType,
        ApiCallResponseType<VerifyOTPResponseType>
      >("/auth/otp/verify", {
        token: currentToken,
        otp: otpValue,
        otpType: (type?.toString() || "") as OTPType
      });

      const { data } = response?.data;
      toast.success(data?.message || "OTP verified successfully!");

      if (type === "EMAIL_VERIFICATION") {
        const urlSearchParams = new URLSearchParams();

        if (redirect) {
          urlSearchParams.set("redirect", redirect?.toString());
        }

        push(`/auth?${urlSearchParams?.toString()}`);
        return;
      }
      if (type === "FORGOT_PASSWORD") {
        push(`/auth/reset-password?token=${data?.token}`);
        return;
      }
      push(redirect?.toString() || "/");
    } catch (error) {
      toast.error(
        constructErrorMessage(
          error as ApiErrorResponseType,
          "OTP verification failed. Please try again."
        )
      );
    } finally {
      setIsSubmitting(false);
    }
  }, [otp, currentToken, type, push, redirect]);

  const resendOTP = useCallback(async () => {
    if (!email || !type) {
      toast.error("Missing required information. Please try again.");
      return;
    }

    // Set resend countdown to prevent spam
    setResendCountdown(RESEND_TIMEOUT);

    try {
      const response = await postData<
        { email: string; otpType: OTPType },
        ApiCallResponseType<ResendOTPResponseType>
      >("/auth/otp/resend", {
        email: email as string,
        otpType: (type?.toString() || "") as OTPType
      });

      const { data } = response?.data;

      // Update the token with the new one received from the server
      if (data?.token) {
        setCurrentToken(data.token);
      }

      toast.success(data?.message || "A new OTP has been sent to your email.");

      // Clear the current OTP fields when a new OTP is sent
      setOtp(new Array(OTP_LENGTH).fill(""));

      // Focus the first input field after clearing
      if (inputFieldsRef.current[0]) {
        inputFieldsRef.current[0].focus();
      }
    } catch (error) {
      toast.error(
        constructErrorMessage(
          error as ApiErrorResponseType,
          "Failed to resend OTP. Please try again."
        )
      );
      // Reset countdown on error to allow retrying
      setResendCountdown(0);
    }
  }, [email, type]);

  // Handle paste functionality
  const handlePaste = useCallback((e: React.ClipboardEvent) => {
    e.preventDefault();
    const pastedData = e.clipboardData.getData("text/plain").trim();

    // Check if pasted content is a valid OTP
    if (pastedData.length === OTP_LENGTH && /^\d+$/.test(pastedData)) {
      setOtp(pastedData.split(""));

      // Focus the last input after paste
      if (inputFieldsRef.current[OTP_LENGTH - 1]) {
        inputFieldsRef.current[OTP_LENGTH - 1].focus();
      }
    }
  }, []);

  // Format countdown time as MM:SS
  const formatTime = (seconds: number) => {
    const mins = Math.floor(seconds / 60);
    const secs = seconds % 60;
    return `${mins}:${secs.toString().padStart(2, "0")}`;
  };

  if (!currentToken || !email || !type) {
    return (
      <div className="flex flex-col items-center pt-28 min-h-screen p-4">
        <div className="w-full max-w-md space-y-8 text-center">
          <AuthHeader title="Invalid Request" />
          <p className="text-center text-gray-600 text-lg">
            Missing required information. Please try the process again.
          </p>
          <Button variant="secondary" onClick={() => push("/auth")}>
            Back to Login
          </Button>
        </div>
      </div>
    );
  }

  return (
    <div className="flex flex-col items-center pt-28 min-h-screen p-4">
      <div className="w-full max-w-md space-y-8">
        <AuthHeader title="Verify OTP" />
        <p className="text-center text-gray-600 text-lg">
          Verify the OTP sent to your email
          <span className="font-bold">{email}</span>
        </p>
        <form
          className="flex flex-col gap-10"
          onSubmit={(e) => {
            e.preventDefault();
            verifyOTP();
          }}
        >
          <div
            className="flex items-stretch gap-4 justify-center"
            onPaste={handlePaste}
          >
            {otp.map((value, index) => (
              <InputField
                placeholder=" "
                key={index}
                value={value}
                inputClassName="text-center !py-0 !px-0 !size-[3rem] justify-center"
                onChange={(e) => {
                  const inputtedValue = (e?.target as HTMLInputElement)?.value;
                  if (
                    inputtedValue.length === OTP_LENGTH &&
                    !isNaN(Number(inputtedValue))
                  ) {
                    setOtp(inputtedValue.split(""));
                    return;
                  }
                  if (isNaN(Number(inputtedValue))) {
                    return;
                  }
                  inputOTPValue(inputtedValue, index);
                }}
                ref={(data) => {
                  if (!data) {
                    return;
                  }
                  inputFieldsRef.current[index] = data;
                }}
                onKeyDown={(e) => {
                  // Allow backspace to clear current field and move to previous
                  if (e.key === "Backspace" && !value) {
                    const previousElement = inputFieldsRef.current[index - 1];
                    if (previousElement) {
                      previousElement.focus();
                    }
                  }
                }}
              />
            ))}
          </div>
          <div className="flex flex-col gap-3">
            <Button
              variant="secondary"
              type="submit"
              loading={isSubmitting}
              disabled={isSubmitting || otp.join("").length !== OTP_LENGTH}
            >
              Verify
            </Button>
            <p className="text-center text-gray-600">
              Didn&apos;t receive the code?
              {resendCountdown > 0 ? (
                <span className="text-secondary-400">
                  Resend in {formatTime(resendCountdown)}
                </span>
              ) : (
                <button
                  type="button"
                  onClick={resendOTP}
                  className="text-secondary-400 hover:underline"
                >
                  Resend OTP
                </button>
              )}
            </p>
          </div>
        </form>
      </div>
    </div>
  );
};

export default VerifyOTP;
