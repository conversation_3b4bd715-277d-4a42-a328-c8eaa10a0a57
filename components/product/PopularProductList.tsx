import React, { useCallback, useEffect, useState } from "react";
import SectionContainer from "../layouts/SectionContainer";
import SectionHeader from "../general/SectionHeader";
import ProductCard, { ProductDetailsType } from "./ProductCard";
import { getData } from "@/api";
import { useQuery } from "@tanstack/react-query";
import ProductCardLoader from "./ProductCardLoader";
import ErrorContainer from "../status/ErrorContainer";
import { constructErrorMessage } from "@/utils/functions";
import EmptyContainer from "../status/EmptyContainer";
import Link from "next/link";

const PopularProductList = () => {
  const [fetchedProductList, setFetchedProductList] = useState<
    ProductDetailsType[] | null
  >(null);
  const queryFn = useCallback(async () => {
    return await getData<ApiCallResponseType<ProductDetailsType[]>>(
      "/products?best_seller=true&limit=8"
    );
  }, []);
  const { isPending, error, data, refetch } = useQuery({
    queryKey: ["popular-products"],
    queryFn
  });

  useEffect(() => {
    if (data) {
      setFetchedProductList(data?.data?.data?.filter((item) => !!item));
    }
  }, [data]);
  return (
    <SectionContainer contentContainerClassName="flex flex-col gap-4">
      <SectionHeader
        title="Popular"
        rightContent={
          <Link className="underline" href="/products">
            See all
          </Link>
        }
      />
      {isPending && !fetchedProductList && !error && (
        <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
          {new Array(8).fill(0).map((_, index) => (
            <ProductCardLoader key={index} />
          ))}
        </div>
      )}
      {!isPending && fetchedProductList && !error && (
        <>
          {fetchedProductList?.length > 0 && (
            <div className="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-4 gap-x-4 gap-y-6">
              {fetchedProductList.map((item, index) => (
                <ProductCard {...item} className="col-span-1" key={index} />
              ))}
            </div>
          )}
          {fetchedProductList.length < 1 && (
            <EmptyContainer description="No popular product available for now" />
          )}
        </>
      )}

      {error && (
        <ErrorContainer
          error={constructErrorMessage(
            error as ApiErrorResponseType,
            "Error occurred whilst fetching latest product"
          )}
          retryFunction={refetch}
        />
      )}
    </SectionContainer>
  );
};

export default PopularProductList;
