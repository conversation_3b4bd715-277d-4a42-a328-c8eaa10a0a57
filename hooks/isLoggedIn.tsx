import React, { useCallback, useEffect, useState } from "react";
import useUser from "./useUser";
import Spinner from "@/components/general/Spinner";
import { useRouter } from "next/router";
import { toast } from "react-toastify";
import { getSavedToken } from "@/localservices";
import { toastIds } from "@/utils/variables";
import useAuth from "./useAuth";

const isLoggedIn = <P extends object>(Component: React.ComponentType<P>) => {
  const Page = (props: P) => {
    const [hasChecked, setHasChecked] = useState(false);
    const { performAuthOperations } = useAuth();
    const { userToken } = useUser();
    const { push, query } = useRouter();
    const { redirect } = query || {};

    const redirectToHome = useCallback(() => {
      push(redirect?.toString() || `/`);
      toast("You are already logged in", {
        toastId: toastIds.loginRedirect
      });
    }, [push, redirect]);

    useEffect(() => {
      if (window && !hasChecked) {
        const savedToken = getSavedToken();
        if (savedToken && !userToken) {
          performAuthOperations(savedToken);
        }
        if (userToken || savedToken) {
          redirectToHome();
        }
        setHasChecked(true);
      }
    }, [redirectToHome, userToken, performAuthOperations, hasChecked]);

    if (userToken) {
      return (
        <div className="w-screen h-screen items-center justify-center flex">
          <Spinner />
        </div>
      );
    }

    return <Component {...props} />;
  };
  return Page;
};

export default isLoggedIn;
