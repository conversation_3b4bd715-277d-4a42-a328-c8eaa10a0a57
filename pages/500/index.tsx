import dynamic from 'next/dynamic';

import SectionContainer from '@/components/layouts/SectionContainer';
import { ServerErrorAnimation } from '@/assets/lotties';

// Dynamically import Lottie without SSR
const Lottie = dynamic(() => import('lottie-react'), { ssr: false });

const ServerError = () => {
	return (
		<SectionContainer className='min-h-screen flex items-center justify-center'>
			<div className='flex flex-col items-center justify-center text-center'>
				<div className='h-64 w-64 flex items-center justify-center'>
					<Lottie
						animationData={ServerErrorAnimation}
						className='w-full h-full'
					/>
				</div>
				<h1 className='text-8xl font-bold text-gray-800 mt-6 mb-4'>500</h1>
				<p className='text-xl text-gray-600 max-w-md'>
					The server encountered an error and could not complete your request.
				</p>
			</div>
		</SectionContainer>
	);
};

// Hide both navbar and footer
ServerError.hideFooter = true;
ServerError.hideNav = true;

export default ServerError;
