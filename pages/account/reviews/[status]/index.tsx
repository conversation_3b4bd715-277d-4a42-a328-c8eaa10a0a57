import DashboardLayout from "@/components/dashboard/DashboardLayout";
import AllReviews from "@/components/dashboard/Reviews/AllReviews";
import isAdmin from "@/hooks/isAdmin";
import { siteName } from "@/utils/variables";
import Head from "next/head";
import React from "react";

interface ReviewsProps extends React.FC {
  hideFooter?: boolean;
  hideNav?: boolean;
}

const Reviews: ReviewsProps = () => {
  return (
    <>
      <Head>
        <title>Reviews | Admin | {siteName}</title>
      </Head>
      <DashboardLayout>
        <div className="p-8">
          <AllReviews />
        </div>
      </DashboardLayout>
    </>
  );
};

Reviews.hideFooter = true;
Reviews.hideNav = true;
export default isAdmin(Reviews);
