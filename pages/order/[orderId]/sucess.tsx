import React from "react";
import Link from "next/link";
import { useRouter } from "next/router";
import { useQuery } from "@tanstack/react-query";
import { getData } from "@/api";

import SectionContainer from "@/components/layouts/SectionContainer";
import dynamic from "next/dynamic";
import { SuccessAnimation } from "@/assets/lotties";
import CustomImage from "@/components/general/CustomImage";
import ErrorContainer from "@/components/status/ErrorContainer";
import { constructErrorMessage } from "@/utils/functions";
import Spinner from "@/components/general/Spinner";
import protectRoute from "@/hooks/protectRoute";
import Head from "next/head";
import { siteName } from "@/utils/variables";
import { OrderDetailsType } from "@/store/useOrderStore";

// Dynamically import Lottie without SSR
const Lottie = dynamic(() => import("lottie-react"), { ssr: false });

// Function to fetch payment/order data
const fetchPaymentDetails = async (
  orderId: string
): Promise<OrderDetailsType> => {
  const { data } = await getData<ApiCallResponseType<OrderDetailsType>>(
    `/payment/${orderId}`
  );
  return data.data;
};

const OrderConfirmation = () => {
  const router = useRouter();
  const { orderId } = router.query;

  const {
    data: order,
    isLoading,
    isError,
    error
  } = useQuery({
    queryKey: ["paymentDetails", orderId],
    queryFn: () => fetchPaymentDetails(orderId as string),
    enabled: !!orderId
  });

  // Calculate total from order data or fallback to empty array if order not loaded yet
  const total =
    order?.total?.formatted?.withCurrency ||
    (order?.carts && order.carts.length > 0
      ? order.carts
          .reduce(
            (sum, item) =>
              sum + (item.price?.amount || 0) * (item.quantity || 1),
            0
          )
          .toFixed(2) +
        (order.carts[0]?.price?.formatted?.withCurrency?.replace(
          /[0-9.]/g,
          ""
        ) || "AED")
      : "N/A");

  // If loading, just show spinner centered in the page
  if (isLoading) {
    return (
      <SectionContainer className="min-h-screen bg-white">
        <div className="flex items-center justify-center w-full h-screen">
          <Spinner className="w-12 h-12" />
        </div>
      </SectionContainer>
    );
  }

  // Error state - show only error component when there's an error
  if (isError) {
    return (
      <>
        <Head>
          <title>Payment confirmation | {siteName}</title>
        </Head>
        <SectionContainer
          className="min-h-screen bg-white"
          contentContainerClassName="flex items-center justify-center py-8 px-4"
        >
          <div className="w-full max-w-md mx-auto">
            <ErrorContainer
              error={constructErrorMessage(
                error as ApiErrorResponseType,
                "Failed to load order details."
              )}
            />
            <div className="mt-6">
              <Link
                href="/"
                className="block w-full py-4 text-center bg-secondary text-white rounded-md hover:bg-secondary-300 transition-colors font-medium"
              >
                Return back home
              </Link>
            </div>
          </div>
        </SectionContainer>
      </>
    );
  }

  // Success state - show full confirmation UI once data is loaded
  return (
    <SectionContainer
      className="min-h-screen bg-white"
      contentContainerClassName="flex items-center justify-center py-8 px-4"
    >
      <div className="w-full max-w-md mx-auto">
        <div className="flex justify-center">
          <Lottie animationData={SuccessAnimation} className="w-40 h-40" />
        </div>

        <h1 className="text-3xl font-bold text-center text-gray-800 mb-4">
          Thank you for your purchase
        </h1>

        <p className="text-center text-gray-700 mb-8 text-lg">
          We&apos;ve received your order and will ship
          <br />
          in 3-5 business days.
        </p>

        {order && (
          <div className="border border-gray-200 rounded-lg p-6 mb-6">
            <h2 className="text-2xl font-bold text-gray-800 mb-2">
              Order Summary
            </h2>
            <p className="text-gray-600 mb-6">Order #{order.orderCode}</p>

            {order.carts?.map((item) => (
              <div
                key={item.id}
                className="flex items-center py-4 border-b border-gray-200 last:border-b-0"
              >
                <div className="bg-gray-100 rounded-md p-2 mr-4">
                  <div className="w-14 h-14 relative">
                    <CustomImage
                      src={item.product?.images?.[0] || ""}
                      alt={item.product?.name || ""}
                      priority
                    />
                  </div>
                </div>
                <div className="flex-grow">
                  <h3 className="font-bold text-gray-800">
                    {item.product?.name}
                  </h3>
                  {/* {item.product?.size && (
                    <p className="text-gray-600">Size: {item.product.size}</p>
                  )} */}
                  {item.quantity > 1 && (
                    <p className="text-gray-600">Qty: {item.quantity}</p>
                  )}
                </div>
                <div className="text-right">
                  <p className="font-bold text-lg text-gray-800 relative">
                    {item.price?.formatted?.withCurrency ? (
                      <>
                        <span className="text-xs font-normal absolute -top-0.5 -left-6 inline-block">
                          {item.price.formatted.withCurrency.replace(
                            /[0-9.]/g,
                            ""
                          )}
                        </span>
                        {item.price.formatted.withoutCurrency}
                      </>
                    ) : (
                      "N/A"
                    )}
                  </p>
                </div>
              </div>
            ))}

            <div className="space-y-2 pt-4">
              <div className="flex justify-between text-gray-600">
                <span>Subtotal</span>
                <span>{order.subTotal?.formatted?.withCurrency || "N/A"}</span>
              </div>

              {order.shippingFee && order.shippingFee.amount > 0 && (
                <div className="flex justify-between text-gray-600">
                  <span>Shipping</span>
                  <span>
                    {order.shippingFee?.formatted?.withCurrency || "Free"}
                  </span>
                </div>
              )}

              {order.taxFee && order.taxFee.amount > 0 && (
                <div className="flex justify-between text-gray-600">
                  <span>Tax</span>
                  <span>{order.taxFee?.formatted?.withCurrency || "N/A"}</span>
                </div>
              )}

              {order.discount && order.discount.amount > 0 && (
                <div className="flex justify-between text-gray-600">
                  <span>Discount</span>
                  <span className="text-green-600">
                    -{order.discount?.formatted?.withCurrency || "N/A"}
                  </span>
                </div>
              )}
            </div>

            <div className="flex justify-between items-center pt-6 mt-2 border-t border-gray-200">
              <h3 className="text-2xl font-bold text-gray-800">Total</h3>
              <p className="text-2xl font-bold text-gray-800 relative inline-block">
                {order.total?.formatted?.withCurrency ? (
                  <>
                    <span className="text-sm font-normal absolute -top-1 -left-6">
                      {order.total.formatted.withCurrency.replace(
                        /[0-9.]/g,
                        ""
                      )}
                    </span>
                    {order.total.formatted.withoutCurrency}
                  </>
                ) : (
                  total
                )}
              </p>
            </div>
          </div>
        )}

        <Link
          href="/"
          className="block w-full py-4 text-center bg-secondary text-white rounded-md hover:bg-secondary-300 transition-colors font-medium"
        >
          Return back home
        </Link>
      </div>
    </SectionContainer>
  );
};

OrderConfirmation.hideFooter = true;

export default protectRoute(OrderConfirmation);
