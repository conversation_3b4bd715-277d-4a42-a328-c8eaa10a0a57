import { getData } from "@/api";
import DashboardLayout from "@/components/dashboard/DashboardLayout";
import SettingsInput, {
  ConfigDetails,
  SettingsInputType
} from "@/components/dashboard/settings/SettingInput";
import Spinner from "@/components/general/Spinner";
import ErrorContainer from "@/components/status/ErrorContainer";
import isAdmin from "@/hooks/isAdmin";
import { constructErrorMessage } from "@/utils/functions";
import { siteName } from "@/utils/variables";
import { useQuery } from "@tanstack/react-query";
import Head from "next/head";
import React, { useCallback, useEffect, useMemo, useState } from "react";

const Settings = () => {
  const [configList, setConfigList] = useState<ConfigDetails[] | null>(null);
  const queryFn = useCallback(async () => {
    return await getData<ApiCallResponseType<ConfigDetails[]>>(
      "/settings/config"
    );
  }, []);
  const { data, error, refetch } = useQuery({
    queryKey: ["platform-config"],
    queryFn
  });
  const settingsList = useMemo<SettingsInputType<{ value: string }>[]>(
    () =>
      [
        {
          settingKey: "tax_fee",
          label: "Tax fee (%)",
          onValueChange: (previousValue: string, newValue: string) => {
            if (isNaN(Number(newValue))) {
              return previousValue;
            }
            return parseInt(newValue || "0", 10)?.toString();
          },
          onValidateInput: (data: string) => ({ value: data }),
          apiUrl: "/settings/tax"
        },
        {
          settingKey: "shipping_fee",
          label: "Shipping fee (AED)",
          onValueChange: (previousValue: string, newValue: string) => {
            if (isNaN(Number(newValue))) {
              return previousValue;
            }
            return parseInt(newValue || "0", 10)?.toString();
          },
          onValidateInput: (data: string) => ({ value: data }),
          apiUrl: "/settings/shipping"
        }
      ].map((data) => ({ ...data, refetch, configList: configList || [] })),
    [refetch, configList]
  );
  useEffect(() => {
    if (data) {
      setConfigList(data?.data?.data);
    }
  }, [data]);

  if (error) {
    return (
      <ErrorContainer
        error={constructErrorMessage(
          error as ApiErrorResponseType,
          "Error fetching config list!"
        )}
      />
    );
  }
  return (
    <>
      <Head>
        <title>Settings | Admin | {siteName}</title>
      </Head>
      <DashboardLayout>
        <div className="p-8 space-y-10">
          <div className="flex items-center py-4 border-b">
            <h1 className="font-semibold text-2xl">Settings</h1>
          </div>
          {!configList && (
            <div className="py-20 flex items-center justify-center">
              <Spinner />
            </div>
          )}
          {configList && (
            <div className="grid grid-cols-4 gap-6">
              {settingsList.map((settingContent) => (
                <SettingsInput
                  {...settingContent}
                  configList={configList || []}
                  key={settingContent.settingKey}
                />
              ))}
            </div>
          )}
        </div>
      </DashboardLayout>
      ;
    </>
  );
};

export default isAdmin(Settings);
