import React from "react";
import { useRouter } from "next/router";
import { ArrowLeft, Printer, Download } from "lucide-react";
import Button from "@/components/Button";
import StatusBadge from "./StatusBadge";
import CustomImage from "@/components/general/CustomImage";
import { useQuery } from "@tanstack/react-query";
import { fetchOrderDetails } from "@/pages/order/[orderId]";
import ErrorContainer from "@/components/status/ErrorContainer";
import { constructErrorMessage, formatDate } from "@/utils/functions";

interface OrderDetailsProps {
  orderId?: string;
}

const OrderDetails: React.FC<OrderDetailsProps> = ({ orderId }) => {
  const { back } = useRouter();
  const {
    data: order,
    error,
    refetch
  } = useQuery({
    queryKey: [`order-details-${orderId}`],
    queryFn: () => fetchOrderDetails(orderId as string),
    enabled: !!orderId,
    retry: 1
  });

  if (!order && !error) {
    return <div className="p-8 text-center">Loading order details...</div>;
  }

  if (error) {
    return (
      <ErrorContainer
        error={constructErrorMessage(
          error as ApiErrorResponseType,
          "Error fetching order details!"
        )}
        retryFunction={refetch}
      />
    );
  }

  return (
    <div className="bg-white rounded-lg shadow-sm">
      {/* Header */}
      <div className="border-b border-gray-200 p-6">
        <div className="flex items-center justify-between">
          <div className="flex items-center space-x-4">
            <button
              title="go back"
              aria-label="go back"
              onClick={back}
              className="p-2 rounded-full hover:bg-gray-100"
            >
              <ArrowLeft className="h-5 w-5 text-gray-500" />
            </button>
            <h1 className="text-xl font-medium">Order Details</h1>
          </div>
          <div className="flex space-x-2">
            <Button className="flex items-center gap-2 bg-white text-gray-700 border border-gray-300 hover:bg-gray-50">
              <Printer className="h-4 w-4" />
              Print
            </Button>
            <Button className="flex items-center gap-2 bg-white text-gray-700 border border-gray-300 hover:bg-gray-50">
              <Download className="h-4 w-4" />
              Download
            </Button>
          </div>
        </div>
      </div>

      {/* Order Summary */}
      <div className="p-6 border-b border-gray-200">
        <h2 className="text-lg font-medium mb-4">Order Summary</h2>
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
          <div>
            <p className="text-sm text-gray-500 mb-1">Order ID</p>
            <p className="font-medium">{order?.id}</p>
          </div>
          <div>
            <p className="text-sm text-gray-500 mb-1">Date Placed</p>
            <p className="font-medium">
              {order?.createdAt ? formatDate(order?.createdAt) : undefined}
            </p>
          </div>
          <div>
            <p className="text-sm text-gray-500 mb-1">Total Amount</p>
            <p className="font-medium">
              AED {order?.total?.formatted?.withCurrency}
            </p>
          </div>
          <div>
            <p className="text-sm text-gray-500 mb-1">Status</p>
            <StatusBadge status={order?.status} />
          </div>
        </div>
      </div>

      {/* Customer Information */}
      <div className="p-6 border-b border-gray-200">
        <h2 className="text-lg font-medium mb-4">Customer Information</h2>
        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
          <div>
            <p className="text-sm text-gray-500 mb-1">Customer Name</p>
            <p className="font-medium">{`${order?.user?.firstName} ${order?.user?.lastName}`}</p>
          </div>
          <div>
            <p className="text-sm text-gray-500 mb-1">Email</p>
            <p className="font-medium">{order?.user?.email}</p>
          </div>
          {/* <div>
            <p className="text-sm text-gray-500 mb-1">Phone</p>
            <p className="font-medium">{order?.user?.phone}</p>
          </div> */}
          <div>
            <p className="text-sm text-gray-500 mb-1">Delivery Address</p>
            <p className="font-medium">{order?.deliveryDetails?.address}</p>
          </div>
        </div>
      </div>

      {/* Order Items */}
      <div className="p-6">
        <h2 className="text-lg font-medium mb-4">Order Items</h2>
        <div className="overflow-x-auto">
          <table className="min-w-full divide-y divide-gray-200">
            <thead className="bg-gray-50">
              <tr>
                <th
                  scope="col"
                  className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider"
                >
                  Product
                </th>
                <th
                  scope="col"
                  className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider"
                >
                  Price
                </th>
                <th
                  scope="col"
                  className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider"
                >
                  Quantity
                </th>
                <th
                  scope="col"
                  className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider"
                >
                  Total
                </th>
              </tr>
            </thead>
            <tbody className="bg-white divide-y divide-gray-200">
              {(order?.carts || [])?.map((item) => (
                <tr key={item.id}>
                  <td className="px-6 py-4 whitespace-nowrap">
                    <div className="flex items-center">
                      <div className="flex-shrink-0 h-10 w-10 relative rounded-md overflow-hidden border border-gray-200 bg-gray-100">
                        <CustomImage
                          src={item?.product?.images[0] || ""}
                          alt={item?.product?.name || ""}
                          fill
                          className="object-cover"
                        />
                      </div>
                      <div className="ml-4">
                        <div className="text-sm font-medium text-gray-900">
                          {item?.product?.name}
                        </div>
                        {/* <div className="text-sm text-gray-500">
                          Size: {item.size}
                        </div> */}
                      </div>
                    </div>
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                    {item?.productPrice?.formatted?.withCurrency}
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                    {item.quantity}
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                    {item?.price?.formatted?.withCurrency}
                  </td>
                </tr>
              ))}
            </tbody>
            <tfoot>
              <tr>
                <td
                  colSpan={3}
                  className="px-6 py-4 text-right text-sm font-medium"
                >
                  Subtotal:
                </td>
                <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900 font-medium">
                  {order?.subTotal?.formatted?.withCurrency}
                </td>
              </tr>
              <tr>
                <td
                  colSpan={3}
                  className="px-6 py-4 text-right text-sm font-medium"
                >
                  Shipping:
                </td>
                <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900 font-medium">
                  {order?.shippingFee?.formatted?.withCurrency}
                </td>
              </tr>
              <tr>
                <td
                  colSpan={3}
                  className="px-6 py-4 text-right text-sm font-medium"
                >
                  Tax:
                </td>
                <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900 font-medium">
                  {order?.taxFee?.formatted?.withCurrency}
                </td>
              </tr>
              <tr>
                <td
                  colSpan={3}
                  className="px-6 py-4 text-right text-sm font-medium"
                >
                  Total:
                </td>
                <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900 font-bold">
                  {order?.total?.formatted?.withCurrency}
                </td>
              </tr>
            </tfoot>
          </table>
        </div>
      </div>

      {/* Payment Information */}
      <div className="p-6 border-t border-gray-200">
        <h2 className="text-lg font-medium mb-4">Payment Information</h2>
        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
          <div>
            <p className="text-sm text-gray-500 mb-1">Payment Method</p>
            <p className="font-medium">Credit Card</p>
          </div>
          <div>
            <p className="text-sm text-gray-500 mb-1">Payment Status</p>
            <StatusBadge status={order?.paidAt ? "PAID" : "PENDING"} />
          </div>
        </div>
      </div>
    </div>
  );
};

export default OrderDetails;
