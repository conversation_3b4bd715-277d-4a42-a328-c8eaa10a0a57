import React, { useCallback, useEffect, useState } from "react";
import StatCard from "./StatCard";
import { Banknote, Package, TrendingDown, Users } from "lucide-react";
import { useQuery } from "@tanstack/react-query";
import { getData } from "@/api";
import ErrorContainer from "../status/ErrorContainer";
import { constructErrorMessage } from "@/utils/functions";

export type DashboardStatDetails = {
  productCount: number;
  totalSales: number;
  totalUsers: number;
  activeUsers: number;
  deletedUsers: number;
  totalReviews: number;
};

const DashboardStat = () => {
  const [stat, setStat] = useState<DashboardStatDetails | null>();
  const queryFn = useCallback(async () => {
    return await getData<ApiCallResponseType<DashboardStatDetails>>(
      "/dashboard/stat"
    );
  }, []);
  const { data, error, refetch } = useQuery({
    queryKey: ["dashboard-stat"],
    queryFn
  });
  useEffect(() => {
    if (data) {
      setStat(data?.data?.data);
    }
  }, [data]);
  return (
    <div className="w-full">
      {error && !stat && (
        <ErrorContainer
          error={constructErrorMessage(
            error as ApiErrorResponseType,
            "Unable to get statistical data! Please try again"
          )}
          retryFunction={refetch}
        />
      )}
      {stat && (
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
          <StatCard
            title="Total Products"
            value={!stat ? "..." : stat?.productCount?.toString() || "0"}
            icon={<Package className="h-5 w-5 text-blue-500" />}
            iconBgColor="bg-blue-50"
          />
          <StatCard
            title="Total Sales"
            value={!stat ? "..." : stat?.totalSales?.toString() || "0"}
            icon={<Banknote className="h-5 w-5 text-green-500" />}
            iconBgColor="bg-green-50"
          />
          <StatCard
            title="Total Users"
            value={!stat ? "..." : stat?.totalUsers?.toString() || "0"}
            icon={<Users className="h-5 w-5 text-emerald-500" />}
            iconBgColor="bg-emerald-50"
          />
          <StatCard
            title="Total Reviews"
            value={!stat ? "..." : stat?.totalReviews?.toString() || "0"}
            icon={<TrendingDown className="h-5 w-5 text-red-500" />}
            iconBgColor="bg-red-50"
          />
        </div>
      )}
    </div>
  );
};

export default DashboardStat;
