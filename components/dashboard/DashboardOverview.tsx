// components/dashboard/DashboardOverview.tsx
import React from "react";
import TopCategoriesChart from "./TopCategoriesChart";
import SalesRevenueChart from "./SalesRevenueChart";
import RecentActivity from "./RecentActivity";
import TopProductsTable from "./TopProductsTable";
import DashboardStat from "./DashboardStat";

const DashboardOverview: React.FC = () => {
  return (
    <div className="space-y-6">
      {/* Stats Cards */}
      <DashboardStat />

      {/* Charts */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        <TopCategoriesChart />
        <SalesRevenueChart />
      </div>

      {/* Recent Activity and Top Products */}
      <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
        <RecentActivity className="lg:col-span-1" />

        <TopProductsTable className="lg:col-span-2" />
      </div>
    </div>
  );
};

export default DashboardOverview;
