"use client";

import { useEffect, useState } from "react";
import Link from "next/link";
import { LogOutIcon, Search, User } from "lucide-react";
// import CurrencyDropdown from "./CurrencyDropdown";
import { IoMdClose } from "react-icons/io";
import CustomImage from "../general/CustomImage";
import { CompetitionImage, PromotionImage } from "@/assets/images";
import useProduct from "@/hooks/useProduct";
import Modal from "../Modal";
import useUser from "@/hooks/useUser";
import InputField from "../general/InputField";
import { useDebouncedCallback } from "use-debounce";
import { useRouter } from "next/router";
import { MdCancel } from "react-icons/md";

interface MobileNavbarProps {
  isMenuOpened: boolean;
  setIsMenuOpen: (isOpen: boolean) => void;
}

export default function MobileNavbar({
  setIsMenuOpen,
  isMenuOpened
}: MobileNavbarProps) {
  const [searchText, setSearchText] = useState("");
  const { push, query } = useRouter();
  const { q } = query || {};
  const [shouldOpenSearchBox, setShouldOpenSearchBox] = useState(false);
  const { userToken, logoutUser } = useUser();
  const { categories } = useProduct();
  //   const [selectedCurrency, setSelectedCurrency] = useState<{
  //     code: string;
  //     symbol: string;
  //   }>({
  //     code: "GB",
  //     symbol: "£"
  //   });
  const [active, setActive] = useState("home");
  const productCategories = (categories || [])?.map((category) => ({
    name: category.name
  }));
  const menuItems = [
    { name: "Home", href: "/", id: "home" },
    { name: "Our Story", href: "/our-story", id: "our-story" },
    { name: "Careers", href: "/careers", id: "careers" },
    { name: "Contact Us", href: "/contact", id: "contact-us" },
    ...(userToken
      ? [
          { name: "Orders", href: "/orders", id: "orders" },
          { name: "Settings", href: "/settings", id: "settings" }
        ]
      : [])
  ];
  const promotions = [
    {
      name: "Children Summer Play set",
      discount: "UP TO 80% OFF",
      image: PromotionImage
    },
    {
      name: "Children Summer Play set",
      discount: "UP TO 80% OFF",
      image: CompetitionImage
    }
  ];
  const processSearch = useDebouncedCallback((q: string) => {
    push({
      pathname: `/products`,
      query: { q }
    });
  }, 1000);

  useEffect(() => {
    const queryFromUrl = q?.toString() || "";
    if (searchText && queryFromUrl !== searchText) {
      processSearch(searchText?.trim());
    }
  }, [searchText, processSearch, q]);

  return (
    <Modal
      opened={isMenuOpened}
      onClose={() => {
        setIsMenuOpen(false);
      }}
      dialogEnterFromAnimation="-translate-x-1/2 opacity-0"
      dialogEnterToAnimation="translate-x-0 opacity-1"
    >
      <div className="bg-white z-50 overflow-y-auto h-screen w-screen max-w-[27rem]">
        <div className="">
          {!shouldOpenSearchBox && (
            <div className="flex justify-between items-center px-4">
              {/* Close button */}
              <ul className="flex justify-center items-center mt-6 gap-5">
                <li>
                  <button
                    aria-label="Close modal"
                    title="Close modal"
                    onClick={() => {
                      setIsMenuOpen(false);
                    }}
                  >
                    <IoMdClose className="text-2xl cursor-pointer" />
                  </button>
                </li>
                <li>
                  <button
                    onClick={() => {
                      setShouldOpenSearchBox(true);
                    }}
                    aria-label="Open search"
                    title="Open search"
                  >
                    <Search size={18} />
                  </button>
                </li>
              </ul>

              <div className="flex justify-center items-center mt-6 gap-4">
                {/* <CurrencyDropdown
                  selectedCurrency={selectedCurrency}
                  setSelectedCurrency={setSelectedCurrency}
                /> */}
                {!userToken && (
                  <Link href="/auth">
                    <User size={24} />
                  </Link>
                )}
                {userToken && (
                  <button
                    onClick={() => logoutUser()}
                    className={
                      "inline-flex text-left items-center justify-start  text-sm gap-2 text-red-600 hover:bg-red-100"
                    }
                    title="Logout"
                  >
                    <span>
                      <LogOutIcon size={20} />
                    </span>
                  </button>
                )}
              </div>
            </div>
          )}
          {shouldOpenSearchBox && (
            <div className="w-full pt-3 px-4">
              <InputField
                inputClassName="bg-slate-100 !py-2 rounded-full"
                placeholder="Search here..."
                value={searchText}
                rightIconAction={() => {
                  setSearchText("");
                  setShouldOpenSearchBox(false);
                }}
                onChange={(e) => {
                  const value = (e?.target as HTMLInputElement)?.value;
                  setSearchText(value);
                }}
                rightIcon={<MdCancel />}
              />
            </div>
          )}

          {/* Currency Dropdown */}

          {/* Main Navigation Links */}
          <nav className="mt-6 bg-gray-50 px-4">
            <div className="overflow-x-auto whitespace-nowrap flex items-center scrollbar-hide">
              <ul className="flex gap-x-6 items-center py-1">
                {menuItems.map((item) => (
                  <li key={item.id}>
                    <Link
                      href={item.href}
                      className={`block text-sm ${
                        active === item.id
                          ? "font-semibold bg-gray-100 px-3 py-2 rounded-md"
                          : ""
                      }`}
                      onClick={() => setActive(item.id)}
                    >
                      {item.name}
                    </Link>
                  </li>
                ))}
              </ul>
            </div>
          </nav>

          {/* Shop All Products */}
          <div className="mt-6 px-4">
            <Link href="/shop" className="font-semibold text-base">
              Shop all products
            </Link>
          </div>

          {/* Product Categories */}
          <div className="mt-4 space-y-4 px-4">
            {productCategories.map((category, index) => (
              <Link
                href={`/products/${category.name}`}
                key={index}
                className="flex items-center space-x-3"
              >
                <div className="size-8 bg-gray-100 flex items-center justify-center rounded-md relative overflow-hidden">
                  <CustomImage
                    src={""}
                    alt={category.name}
                    width={12}
                    height={12}
                  />
                </div>
                <span className="uppercase text-sm font-medium">
                  {category.name}
                </span>
              </Link>
            ))}
          </div>

          {/* Promotions */}
          <div className="mt-6 grid grid-cols-2 gap-4 px-2">
            {promotions.map((promo, index) => (
              <Link
                href={`/products`}
                key={index}
                className="relative rounded-lg overflow-hidden"
              >
                <div className="aspect-square relative">
                  <CustomImage
                    src={promo.image || ""}
                    alt={promo.name}
                    fill
                    className="object-cover"
                  />
                </div>
                <span className="absolute top-24 left-3 bg-orange-500 text-white text-sm font-bold px-3 py-1 rounded-xl">
                  {promo.discount}
                </span>
                <div className="absolute bottom-3 left-3 text-white text-lg font-semibold tracking-tight leading-tight">
                  {promo.name}
                </div>
              </Link>
            ))}
          </div>
        </div>
      </div>
    </Modal>
  );
}
