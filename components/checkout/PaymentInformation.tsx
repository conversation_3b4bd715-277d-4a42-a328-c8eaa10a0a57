import React, { useCallback, useEffect, useState } from "react";
import InputField from "../general/InputField";
import Button from "../Button";
import { ChevronRight, X } from "lucide-react";
// import RadioButton from "../general/RadioButton";
import ErrorContainer from "../status/ErrorContainer";
import { toast } from "react-toastify";
import { getData, postData } from "@/api";
import { constructErrorMessage } from "@/utils/functions";
import { useQuery } from "@tanstack/react-query";
import useCart from "@/hooks/useCart";
import Spinner from "../general/Spinner";
import { CouponDiscountType, OrderDetailsType } from "@/store/useOrderStore";

export type CouponType = {
  discountAmount: AmountType | number;
  discountType: CouponDiscountType;
  code: string;
};

export type OrderSummaryType = {
  subtotal: AmountType;
  taxFee: AmountType;
  shippingFee: AmountType;
  total: AmountType;
  discount?: AmountType;
};

export type CreateOrderBodyType = {
  successURL: string;
  failedURL: string;
  addressId: string;
  coupon?: string;
};

const CheckoutStat: React.FC<{
  title: string;
  value: string;
  alternativeValue?: string;
}> = ({ title, value, alternativeValue }) => {
  return (
    <div className="flex items-center gap-10 opacity-60 justify-between text-sm">
      <h1>{title}</h1>
      <p className="flex items-center flex-col">
        {alternativeValue && <span className="block">{alternativeValue}</span>}
        <span
          className={`${
            alternativeValue ? "line-through text-[0.7rem]" : ""
          } block`}
        >
          {value}
        </span>
      </p>
    </div>
  );
};

// const labelClassName = "text-sm";
const PaymentInformation = () => {
  const [isCreatingOrder, setIsCreatingOrder] = useState(false);
  const { selectedAddress } = useCart();
  const [couponCode, setCouponCode] = useState("");
  const [applyingCoupon, setApplyingCoupon] = useState(false);
  const [fetchedCouponCode, setFetchedCouponCode] = useState<CouponType | null>(
    null
  );
  const [orderSummary, setOrderSummary] = useState<OrderSummaryType | null>(
    null
  );
  const [fetchingCouponError, setFetchingCouponError] = useState<string | null>(
    null
  );
  const queryFn = useCallback(() => {
    const urlSearchParams = new URLSearchParams("");

    if (fetchedCouponCode) {
      urlSearchParams.set("coupon", fetchedCouponCode?.code);
    }

    return getData<ApiCallResponseType<OrderSummaryType>>(
      `/order/summary?${urlSearchParams?.toString()}`
    );
  }, [fetchedCouponCode]);
  const { refetch, data, error, isLoading } = useQuery({
    queryFn,
    queryKey: ["order-summary"]
  });

  const handleCouponChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    setCouponCode(e.target.value);
  };

  const createOrder = useCallback(async () => {
    if (!selectedAddress) {
      return toast("Please select an address to continue");
    }
    const host = window.location.origin;
    setIsCreatingOrder(true);
    let body: CreateOrderBodyType = {
      addressId: selectedAddress?.id,
      failedURL: `${host}/order/{{orderId}}/failed`,
      successURL: `${host}/order/{{orderId}}/success`
    };

    if (couponCode) {
      body = { ...body, coupon: couponCode };
    }
    try {
      const { data } = await postData<
        CreateOrderBodyType,
        ApiCallResponseType<OrderDetailsType>
      >("/order", body);
      const { redirectUrl } = data?.data;
      window.open(redirectUrl, "_blank");
    } catch (error) {
      toast(
        constructErrorMessage(
          error as ApiErrorResponseType,
          "Error encountered whilst creating order!"
        )
      );
    } finally {
      setIsCreatingOrder(false);
    }
  }, [couponCode, selectedAddress]);

  const handleApplyCoupon = useCallback(async () => {
    if (couponCode.trim()?.length < 1) {
      toast.error("Please enter a coupon code");
      return;
    }
    setFetchingCouponError(null);
    setApplyingCoupon(true);
    try {
      const { data } = await getData<ApiCallResponseType<CouponType>>(
        `/coupon/${couponCode}`
      );
      if (data?.data) {
        setFetchedCouponCode(data?.data);
      }
    } catch (error) {
      setFetchingCouponError(
        constructErrorMessage(
          error as ApiErrorResponseType,
          "Unable to verify coupon code!"
        )
      );
    } finally {
      setApplyingCoupon(false);
    }
  }, [couponCode]);

  useEffect(() => {
    refetch();
  }, [fetchedCouponCode, refetch]);

  useEffect(() => {
    if (data) {
      setOrderSummary(data?.data?.data);
    }
  }, [data]);

  return (
    <div className="flex flex-col gap-6 p-5 rounded-md w-full md:max-w-[27rem] border sticky top-5">
      <div className="flex flex-col gap-6 border-b pb-7">
        <h1 className="font-bold">Payment information</h1>
        <div className="flex flex-col gap-6">
          <h1 className="font-semibold">Apply discount</h1>
          <div className="flex flex-col gap-1">
            <div className="flex items-center gap-6">
              <InputField
                inputClassName="bg-slate-100 !rounded-full focus:!border"
                className="flex-1"
                placeholder="Enter coupon code"
                value={couponCode}
                onChange={handleCouponChange}
                disabled={applyingCoupon || !!fetchedCouponCode}
              />
              {fetchedCouponCode && (
                <Button
                  variant="transparent"
                  type="button"
                  className="border-red-500 border !rounded-full inline-flex items-center gap-1 text-red-500 !py-2 pr-3 px-2"
                  onClick={() => {
                    setFetchedCouponCode(null);
                  }}
                  disabled={!fetchedCouponCode}
                >
                  <span>Remove</span>
                  <span>
                    <X size={17} />
                  </span>
                </Button>
              )}
              {!fetchedCouponCode && !applyingCoupon && (
                <Button
                  variant="transparent"
                  type="button"
                  className="border-secondary border !rounded-full inline-flex items-center gap-1 text-secondary !py-2 pr-3 px-2"
                  onClick={handleApplyCoupon}
                  disabled={applyingCoupon || !couponCode.trim()}
                  loading={applyingCoupon}
                >
                  <span>Apply now</span>
                  <span>
                    <ChevronRight size={17} />
                  </span>
                </Button>
              )}
              {applyingCoupon && <Spinner className="size-6 !border" />}
            </div>
            {fetchingCouponError && (
              <p className="text-red-600 text-xs">{fetchingCouponError}</p>
            )}
          </div>
          {fetchedCouponCode && (
            <div className="text-sm text-green-600 flex items-center gap-2">
              <span>Coupon applied: {fetchedCouponCode?.code}</span>
              {fetchedCouponCode && (
                <>
                  {fetchedCouponCode?.discountType === "PERCENTAGE" && (
                    <span className="font-bold">
                      ({fetchedCouponCode?.discountAmount?.toString()}% off)
                    </span>
                  )}
                  {fetchedCouponCode?.discountType === "FIXED" &&
                    typeof fetchedCouponCode?.discountAmount !== "number" && (
                      <span className="font-bold">
                        (
                        {
                          fetchedCouponCode?.discountAmount?.formatted
                            ?.withCurrency
                        }
                        off)
                      </span>
                    )}
                </>
              )}
            </div>
          )}
        </div>
      </div>
      {/* <div className="flex flex-col gap-6 border-b pb-7">
        <h1 className="font-semibold">Pay With</h1>
        <div className="flex flex-col gap-2">
          <RadioButton name="payment-type" label="Debit or Credit card" />
          <RadioButton name="payment-type" label="Pay on delivery" />
        </div>
      </div> */}
      <div className="flex flex-col gap-6">
        {/* <h1 className="font-semibold">Enter Card Information</h1>
        <div className="flex flex-col gap-4 border-b pb-6">
          <InputField
            placeholder="E.G John Doe"
            label="Card holder name"
            labelClassName={labelClassName}
          />
          <InputField
            placeholder="E.G 1234 1234 1234 1234"
            labelClassName={labelClassName}
            label="Card Number"
          />
        </div> */}
        {error && (
          <ErrorContainer
            error={"Unable to fetch summary! Please Retry"}
            retryFunction={refetch}
            className="border-b pb-6"
          />
        )}
        {((!orderSummary && !error) || isLoading) && (
          <div className="border-b pb-6 flex flex-col gap-4 animate-pulse">
            <div className="h-4 bg-slate-200 rounded w-full"></div>
            <div className="h-4 bg-slate-200 rounded w-full"></div>
            <div className="h-4 bg-slate-200 rounded w-full"></div>
          </div>
        )}
        {orderSummary && !error && (
          <div className="border-b pb-6 flex flex-col gap-4">
            <CheckoutStat
              title="Sub total"
              value={orderSummary?.subtotal?.formatted?.withCurrency}
              alternativeValue={orderSummary?.discount?.formatted?.withCurrency}
            />
            {/* {fetchedCouponCode && orderSummary?.discount && (
              <CheckoutStat
                title={`Discount (${fetchedCouponCode?.code})`}
                value={`-${orderSummary?.discount?.formatted?.withCurrency}`}
              />
            )} */}
            <CheckoutStat
              title="Tax"
              value={orderSummary?.taxFee?.formatted?.withCurrency}
            />
            <CheckoutStat
              title="Shipping"
              value={orderSummary?.shippingFee?.formatted?.withCurrency}
            />
          </div>
        )}
        <div className="flex item-center gap-10 justify-between">
          <p className="font-medium text-sm">Total</p>
          <p className="font-bold">
            {!orderSummary && <span className="animate-pulse">...</span>}
            {orderSummary && orderSummary?.total?.formatted?.withCurrency}
          </p>
        </div>
        <Button
          type="button"
          variant="secondary"
          onClick={createOrder}
          loading={isCreatingOrder}
          disabled={!orderSummary || !orderSummary || !selectedAddress}
        >
          {orderSummary &&
            `Pay ${orderSummary?.total?.formatted?.withCurrency}`}
          {!orderSummary && "Pay"}
        </Button>
      </div>
    </div>
  );
};

export default PaymentInformation;
