import dynamic from 'next/dynamic';
import SectionContainer from '@/components/layouts/SectionContainer';
import Link from 'next/link';
import { NotFoundAnimation } from '@/assets/lotties';

// Dynamically import Lottie without SSR
const Lottie = dynamic(() => import('lottie-react'), { ssr: false });

const NotFound = () => {
	return (
		<SectionContainer
			className=''
			contentContainerClassName='flex items-center justify-center py-10 pb-40'>
			<div className='text-center space-y-4'>
				<div className='flex justify-center items-center h-40 overflow-hidden'>
					<Lottie
						animationData={NotFoundAnimation}
						width={150}
						height={150}
						className='size-60'
					/>
				</div>
				<p className='text-sm md:text-base text-gray-600 max-w-md mx-auto'>
					The page you are looking for was moved, removed, renamed, or might
					never have existed. If you&apos;re still lost, try using our search in
					the top menu or return to the homepage.
				</p>
				<Link
					href='/'
					className='inline-block w-full py-3 bg-secondary text-white rounded-md hover:bg-secondary-300 transition-colors'>
					Return back home
				</Link>
			</div>
		</SectionContainer>
	);
};

NotFound.hideFooter = true;

export default NotFound;
