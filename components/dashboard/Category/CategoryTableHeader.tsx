import React from 'react';
import CheckBox from '@/components/general/CheckBox';

interface CategoryTableHeaderProps {
	isAllSelected: boolean;
	onSelectAll: (checked: boolean) => void;
}

const columns = [
	{ id: 'name', label: 'Category Name', sortable: true },
	{ id: 'dateAdded', label: 'Date Added', sortable: true },
	{ id: 'productCount', label: 'Num of Products', sortable: true },
];

const CategoryTableHeader: React.FC<CategoryTableHeaderProps> = ({
	isAllSelected,
	onSelectAll,
}) => {
	return (
		<thead className='bg-gray-50'>
			<tr>
				<th
					scope='col'
					className='px-4 py-3 text-left'>
					<CheckBox
						checked={isAllSelected}
						onChange={(e: React.ChangeEvent<HTMLInputElement>) =>
							onSelectAll(e.target.checked)
						}
						className='h-4 w-4'
					/>
				</th>
				{columns.map((column) => (
					<th
						key={column.id}
						scope='col'
						className='px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider'>
						<div className='flex items-center'>{column.label}</div>
					</th>
				))}
				<th
					scope='col'
					className='relative px-4 py-3'>
					<span className='sr-only'>Actions</span>
				</th>
			</tr>
		</thead>
	);
};

export default CategoryTableHeader;
