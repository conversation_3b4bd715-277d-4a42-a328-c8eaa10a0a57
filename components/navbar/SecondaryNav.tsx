// SecondaryNav.jsx
import React from "react";
import SectionContainer from "../layouts/SectionContainer";
import Link from "next/link";

const SecondaryNav = () => {
  return (
    <SectionContainer
      contentContainerClassName="md:flex hidden justify-between items-center px-6 py-3"
      className="bg-gray-50"
    >
      <nav className="hidden md:flex gap-6">
        <Link href="/" className="hover:text-purple-600">
          Home
        </Link>
        <Link href="/our-story" className="hover:text-purple-600">
          Our Story
        </Link>
        <Link href="/promotions" className="hover:text-purple-600">
          Promotions
        </Link>
      </nav>

      <nav className="hidden md:flex gap-6">
        <Link href="/news" className="hover:text-purple-600">
          News & Events
        </Link>
        <Link href="/media" className="hover:text-purple-600">
          Media
        </Link>
        <Link href="/careers" className="hover:text-purple-600">
          Careers
        </Link>
        <Link href="/contact" className="hover:text-purple-600">
          Contact Us
        </Link>
      </nav>
    </SectionContainer>
  );
};

export default SecondaryNav;
