import { useRouter } from "next/router";
import useUser from "./useUser";
import { useCallback, useEffect } from "react";
import { getSavedToken } from "@/localservices";
import Spinner from "@/components/general/Spinner";
import useAuth from "./useAuth";

const isAdmin = <P extends object>(Component: React.ComponentType<P>) => {
  const ProtectedPage = (props: P) => {
    const { performAuthOperations } = useAuth();
    const { userToken, userDetails } = useUser();
    const { push } = useRouter();

    const redirectToHomePage = useCallback(() => {
      push(`/`);
    }, [push]);

    useEffect(() => {
      if (window) {
        const savedToken = getSavedToken();
        if (
          (!userToken && !savedToken) ||
          (userDetails && userDetails?.role !== "ADMIN")
        ) {
          redirectToHomePage();
        }
        if (savedToken && !userToken) {
          performAuthOperations(savedToken);
        }
      }
    }, [redirectToHomePage, userToken, performAuthOperations, userDetails]);

    if (!userToken || !userDetails) {
      return (
        <div className="w-screen h-screen items-center justify-center flex">
          <Spinner />
        </div>
      );
    }

    return <Component {...props} />;
  };
  return ProtectedPage;
};

export default isAdmin;
