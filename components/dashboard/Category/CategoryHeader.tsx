import Button from "@/components/Button";
import { Plus } from "lucide-react";
import React, { useCallback, useState } from "react";
import AddCategoryModal from "./AddCategoryModal";

interface CategoryHeaderProps {
  onCategoryAdded?: () => void;
}

const CategoryHeader: React.FC<CategoryHeaderProps> = ({ onCategoryAdded }) => {
  const [isAddModalOpen, setIsAddModalOpen] = useState(false);

  const handleOpenAddModal = useCallback(() => {
    setIsAddModalOpen(true);
  }, []);

  const handleCloseAddModal = useCallback(() => {
    setIsAddModalOpen(false);
  }, []);

  const handleCategoryAdded = useCallback(() => {
    handleCloseAddModal();
    if (onCategoryAdded) {
      onCategoryAdded();
    }
  }, [handleCloseAddModal, onCategoryAdded]);

  return (
    <>
      <div className="flex flex-col md:flex-row md:items-center md:justify-between mb-6">
        <h1 className="text-2xl font-semibold text-gray-900 mb-4 md:mb-0">
          Categories
        </h1>
        <div className="flex flex-wrap items-center gap-2">
          <Button
            variant="primary"
            size="small"
            className="flex items-center gap-2"
            onClick={handleOpenAddModal}
          >
            <Plus className="h-4 w-4" />
            <span>Add Category</span>
          </Button>
        </div>
      </div>

      {isAddModalOpen && (
        <AddCategoryModal
          opened={isAddModalOpen}
          onClose={handleCloseAddModal}
          onCategoryAdded={handleCategoryAdded}
        />
      )}
    </>
  );
};

export default CategoryHeader;
