import React, { useState, useEffect, useCallback } from "react";
import { useRouter } from "next/router";
import InputField from "@/components/general/InputField";
import SelectBox from "@/components/general/SelectBox";
import TextArea from "@/components/general/TextArea";
import Button from "@/components/Button";
import { ArrowLeft, Upload, X } from "lucide-react";
import { postData } from "@/api";
import Image from "next/image";
import { toast } from "react-toastify";
import { constructErrorMessage } from "@/utils/functions";
import useProduct from "@/hooks/useProduct";
import { useForm } from "react-hook-form";
import { ProductDetailsType } from "@/components/product/ProductCard";

const allowedFileTypes = ["image/png", "image/jpeg", "application/pdf"];

const defaultValues = {
  name: "",
  quantity: 1,
  description: "",
  discount: 0,
  price: 0,
  categoryId: "",
  sku: "",
  gender: ""
};

const AddProduct: React.FC = () => {
  const router = useRouter();
  const [error, setRegistrationError] = useState<string | null>(null);
  const [imagePreviews, setImagePreviews] = useState<string[]>([]);
  const [selectedFiles, setSelectedFiles] = useState<File[]>([]);
  const { categories, fetchCategories } = useProduct();

  useEffect(() => {
    if (!categories) {
      fetchCategories();
    }
  }, [fetchCategories, categories]);

  const {
    register,
    handleSubmit,
    formState: { isSubmitting, errors, isValid },
    setError
  } = useForm({
    defaultValues,
    mode: "onChange"
  });

  const handleImageUpload = useCallback(
    (e: React.ChangeEvent<HTMLInputElement>) => {
      const files = e.target.files;
      if (files) {
        Array.from(files).forEach((file, index) => {
          if (!allowedFileTypes.includes(file.type) || index > 4) {
            return;
          }
          const previewUrl = URL.createObjectURL(file);
          setImagePreviews((prevState) =>
            prevState?.length < 5 ? [...prevState, previewUrl] : prevState
          );
          setSelectedFiles((prevState) =>
            prevState?.length < 5 ? [...prevState, file] : prevState
          );
        });
      }
    },
    []
  );

  const handleImageRemove = useCallback((index: number) => {
    setImagePreviews((prevState) =>
      prevState.filter((_, previewIndex) => previewIndex !== index)
    );
    setSelectedFiles((prevState) =>
      prevState.filter((_, previewIndex) => previewIndex !== index)
    );
  }, []);

  const addProduct = useCallback(
    async (formData: typeof defaultValues) => {
      if (selectedFiles.length < 1) {
        toast("Please select at least one image!");
        return;
      }

      try {
        // Create FormData object
        const productFormData = new FormData();

        productFormData.append("name", formData.name);
        productFormData.append("quantity", String(formData.quantity));
        productFormData.append("description", formData.description);
        if (formData?.discount > 0) {
          productFormData.append("discount", String(formData.discount));
        }
        productFormData.append("price", String(formData.price));
        productFormData.append("categoryId", formData.categoryId);
        productFormData.append("sku", formData.sku.trim());
        productFormData.append("gender", formData.gender);

        selectedFiles.forEach((file) => {
          productFormData.append("images", file);
        });

        await postData<FormData, ApiCallResponseType<ProductDetailsType>>(
          "/product",
          productFormData
        );

        toast.success("Product added successfully! 🎉");
        router.push("/account/products");
      } catch (err: unknown) {
        const errorMessage = constructErrorMessage(
          err as ApiErrorResponseType,
          "Failed to add product. Please try again."
        );
        const apiError = err as ApiErrorResponseType;
        if (apiError?.response?.data?.errors) {
          const errorKeys = Object.keys(apiError.response.data.errors || {});
          errorKeys.forEach((key) => {
            setError(key as keyof typeof defaultValues, {
              message:
                apiError.response?.data?.errors?.[key] || "Error occurred"
            });
          });
        }
        toast(
          constructErrorMessage(
            apiError,
            "Unable to process request! Please try again later"
          )
        );
        setRegistrationError(errorMessage);
        toast(errorMessage);
      }
    },
    [router, selectedFiles]
  );

  return (
    <div className="p-4 md:p-6">
      <div className="flex items-center mb-6">
        <button
          onClick={() => router.back()}
          className="mr-4 p-2 rounded-full hover:bg-gray-100"
          aria-label="Go back"
        >
          <ArrowLeft className="h-5 w-5" />
        </button>
        <h1 className="text-2xl font-semibold text-gray-900">
          Add New Product
        </h1>
      </div>

      <form
        onSubmit={handleSubmit(addProduct)}
        className="max-w-3xl mx-auto bg-white p-6 rounded-lg shadow-sm"
      >
        {error && (
          <div className="mb-4 p-3 bg-red-50 text-red-600 rounded-md">
            {error}
          </div>
        )}

        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
          {/* Left Inputs */}
          <div className="space-y-6">
            <InputField
              label="Product Name"
              id="name"
              placeholder="Enter product name"
              required
              error={errors?.name?.message}
              {...register("name", {
                required: "Provide your product name"
              })}
            />
            <InputField
              label="Price (AED)"
              id="price"
              type="number"
              placeholder="0.00"
              min="0"
              step="0.01"
              required
              error={errors?.price?.message}
              {...register("price", {
                required: "Provide your product price",
                min: {
                  value: 1,
                  message: "Allowed minimum product price is AED 1"
                },
                valueAsNumber: true
              })}
            />
            <InputField
              label="Discount (%)"
              id="discount"
              type="number"
              placeholder="E.G 10"
              min="0"
              max="100"
              step="1"
              error={errors?.discount?.message}
              {...register("discount", {
                valueAsNumber: true,
                max: {
                  value: 100,
                  message: "Maximum allowed discount is 100%s"
                }
              })}
            />
            <SelectBox
              label="Category"
              id="categoryId"
              options={
                categories?.map((cat) => ({
                  value: cat.id,
                  label: cat.name
                })) || []
              }
              emptyOptionLabel="Select a category"
              required
              error={errors?.categoryId?.message}
              {...register("categoryId", {
                required: "Please select your product category"
              })}
            />
            <InputField
              label="Quantity"
              id="quantity"
              type="number"
              placeholder="0"
              min="1"
              step="1"
              required
              error={errors?.quantity?.message}
              {...register("quantity", {
                required: "Provide your product quantity",
                min: {
                  value: 1,
                  message: "Minimum allowed product quantity is 1"
                },
                valueAsNumber: true
              })}
            />
            <InputField
              label="SKU"
              id="sku"
              type="text"
              placeholder="UR-1111"
              required
              error={errors?.sku?.message}
              {...register("sku", {
                required: "Provide your product code"
              })}
            />
            <SelectBox
              label="Gender"
              id="gender"
              required
              options={[
                { value: "MALE", label: "Male" },
                { value: "FEMALE", label: "Female" },
                { value: "UNISEX", label: "Unisex" }
              ]}
              error={errors?.gender?.message}
              {...register("gender", {
                required: "Provide your product sex"
              })}
            />
          </div>

          {/* Right Inputs */}
          <div className="space-y-6">
            {/* Image upload */}
            <div className="space-y-2">
              <label
                className="block text-sm font-medium text-gray-700"
                htmlFor="image-upload"
              >
                Product Image
              </label>
              <div className="mt-1 flex flex-col items-center">
                <div className="flex justify-center items-center w-full h-48 border-2 border-dashed border-gray-300 rounded-md relative">
                  <input
                    id="image-upload"
                    name="image-upload"
                    type="file"
                    max={5}
                    multiple
                    className="w-full h-full absolute left-0 top-0 z-10 cursor-pointer opacity-0"
                    accept="image/*"
                    onChange={handleImageUpload}
                  />
                  <div className="space-y-1 text-center">
                    <Upload className="mx-auto h-12 w-12 text-gray-400" />
                    <div className="text-sm text-gray-600">
                      <label
                        htmlFor="file-upload"
                        className="relative cursor-pointer bg-white rounded-md font-medium text-blue-600 hover:text-blue-500"
                      >
                        <span>Upload an image</span>
                      </label>
                      <p className="pl-1">or drag and drop</p>
                    </div>
                    <p className="text-xs text-gray-500">
                      PNG, JPG, GIF up to 10MB
                    </p>
                  </div>
                </div>
              </div>
              <div className="flex items-center gap-2 flex-wrap">
                {imagePreviews.map((fileUrl, index) => (
                  <div className="relative" key={index}>
                    <button
                      title="Remove image"
                      onClick={() => {
                        handleImageRemove(index);
                      }}
                      className="size-3 inline-flex items-center justify-center rounded-full bg-red-200 z-20 absolute -right-1 -top-1"
                    >
                      <X size={11} />
                    </button>
                    <div className="size-9 rounded-md shrink-0 overflow-hidden relative bg-slate-200">
                      <Image
                        alt=""
                        fill
                        src={fileUrl}
                        className="object-cover object-center"
                      />
                    </div>
                  </div>
                ))}
              </div>
            </div>

            <TextArea
              label="Description"
              id="description"
              placeholder="Enter product description"
              rows={5}
              required
              error={errors?.description?.message}
              {...register("description", {
                required: "Provide your product description"
              })}
            />
          </div>
        </div>

        {/* Buttons */}
        <div className="mt-8 flex justify-end">
          <Button
            type="button"
            variant="default"
            className="mr-4"
            onClick={() => router.back()}
          >
            Cancel
          </Button>
          <Button
            type="submit"
            variant="primary"
            disabled={isSubmitting || !isValid}
            loading={isSubmitting}
          >
            {isSubmitting ? "Adding Product..." : "Add Product"}
          </Button>
        </div>
      </form>
    </div>
  );
};

export default AddProduct;
