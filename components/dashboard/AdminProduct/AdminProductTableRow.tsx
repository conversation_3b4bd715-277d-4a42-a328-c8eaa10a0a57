import CustomImage from "@/components/general/CustomImage"; // Adjust path
import { MoreHorizontal } from "lucide-react";
import ProductDetailsModal from "./ProductDetailsModal";
import { ProductDetailsType } from "@/components/product/ProductCard";
import React, { useState } from "react";

interface AdminProductTableRowProps {
  product: ProductDetailsType;
  refetch: () => void;
}

const AdminProductTableRow: React.FC<AdminProductTableRowProps> = ({
  product,
  refetch = () => {}
}) => {
  const [isModalOpen, setIsModalOpen] = useState(false);

  return (
    <tr
      className={`hover:bg-gray-50 'bg-white ${
        product.deletedAt ? "opacity-40" : ""
      }`}
    >
      {/* Checkbox */}
      {/* <td className='pl-4 pr-2 py-3 whitespace-nowrap w-12'>
				<CheckBox
					checked={isSelected}
					onChange={(e) =>
						onSelect(product.id, (e.target as HTMLInputElement).checked)
					}
					aria-labelledby={`product-name-${product.id}`}
				/>
			</td> */}
      {/* Product Name/Image/ID */}
      <td className="px-3 py-3 text-sm text-gray-500 max-w-xs">
        {/* Added max-w-xs for name column */}
        <div className="flex items-center space-x-3">
          {/* Image */}
          <div className="flex-shrink-0 h-10 w-10 relative rounded-md overflow-hidden border border-gray-200 bg-gray-100">
            <CustomImage
              src={product.images?.[0] || ""}
              alt={product.name}
              fill
              className="object-cover"
            />
          </div>
          {/* Name and ID */}
          <div className="overflow-hidden">
            {/* Prevent long text overflow */}
            <div
              id={`product-name-${product.id}`}
              className="font-medium text-gray-900 truncate" // Truncate long names
              title={product.name} // Show full name on hover
            >
              {product.name}
            </div>
            <div className="text-gray-500 text-xs">{product.id}</div>
            {/* Product ID */}
          </div>
        </div>
      </td>
      {/* Price */}
      <td className="px-3 py-3 whitespace-nowrap text-sm text-gray-500">
        {product.price.formatted.withCurrency} {/* Display price */}
      </td>
      {/* Category */}
      <td className="px-3 py-3 whitespace-nowrap text-sm text-gray-500">
        {product.category} {/* Display category */}
      </td>
      {/* Stock */}
      <td className="px-3 py-3 whitespace-nowrap text-sm text-gray-500">
        {product.quantity} {/* Display stock */}
      </td>
      {/* Stock */}
      <td className="px-3 py-3 whitespace-nowrap text-sm text-white font-semibold">
        <span
          className={`px-2 py-1 rounded-full ${
            product.deletedAt ? "bg-red-400" : "bg-green-400"
          }`}
        >
          {product.deletedAt ? "Deleted" : "Active"}
        </span>
      </td>
      {/* Description - Usually omitted or truncated in main table view */}
      {/* Example: Truncated description - uncomment if needed and add column in header */}
      {/* <td className='px-3 py-3 text-sm text-gray-500 max-w-sm truncate' title={product.description}>
				{product.description}
			</td> */}
      {/* Actions */}
      <td className="pl-3 pr-4 py-3 whitespace-nowrap text-right text-sm font-medium w-12">
        <button
          className="text-gray-400 hover:text-gray-600"
          title={`View details for ${product.name}`}
          onClick={() => setIsModalOpen(true)}
        >
          <span className="sr-only">
            Actions for {product.name} ({product.id})
          </span>
          <MoreHorizontal className="h-5 w-5" />
        </button>

        {/* Product Details Modal */}
        <ProductDetailsModal
          product={product}
          isOpen={isModalOpen}
          onClose={() => setIsModalOpen(false)}
          refetch={refetch}
        />
      </td>
    </tr>
  );
};

export default AdminProductTableRow;
