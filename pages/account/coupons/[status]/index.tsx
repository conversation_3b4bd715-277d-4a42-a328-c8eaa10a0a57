import React from "react";
import DashboardLayout from "@/components/dashboard/DashboardLayout";
import AllCoupons from "@/components/dashboard/Coupons/AllCoupons";
import isAdmin from "@/hooks/isAdmin";
import Head from "next/head";
import { siteName } from "@/utils/variables";

interface CouponsPageProps extends React.FC {
  hideFooter?: boolean;
  hideNav?: boolean;
}

const CouponsPage: CouponsPageProps = () => {
  return (
    <>
      <Head>
        <title>Coupons | Admin | {siteName}</title>
      </Head>
      <DashboardLayout>
        <div className="p-8">
          <AllCoupons />
        </div>
      </DashboardLayout>
    </>
  );
};

CouponsPage.hideFooter = true;
CouponsPage.hideNav = true;

export default isAdmin(CouponsPage);
