import React, { useState } from "react";
import Link from "next/link";
import { IconType } from "react-icons";
import { LuChevronDown, LuChevronUp } from "react-icons/lu";

interface SidebarItemProps {
  icon: IconType;
  label: string;
  href?: string;
  isActive?: boolean;
  submenuItems?: { label: string; href: string }[];
}

const SidebarItem: React.FC<SidebarItemProps> = ({
  icon: Icon,
  label,
  href = "#",
  isActive = false,
  submenuItems = []
}) => {
  const [isOpen, setIsOpen] = useState(false);

  return (
    <li>
      <div
        className={`flex items-center px-6 py-3 text-sm cursor-pointer ${
          isActive
            ? "bg-secondary-400 text-white font-medium rounded-md"
            : "text-white hover:bg-secondary-600 rounded-sm"
        }`}
        onClick={() => submenuItems.length && setIsOpen(!isOpen)}
      >
        <Icon className="w-5 h-5 mr-3" />
        {href !== "#" ? (
          <Link href={href} className="flex-1">
            {label}
          </Link>
        ) : (
          <span>{label}</span>
        )}
        {submenuItems.length > 0 && (
          <span className="ml-auto">
            {isOpen ? <LuChevronUp /> : <LuChevronDown />}
          </span>
        )}
      </div>
      {submenuItems.length > 0 && isOpen && (
        <ul className="ml-8 mt-2 space-y-2">
          {submenuItems.map((item, index) => (
            <li key={index}>
              <Link
                href={item.href}
                className="block px-4 py-2 text-white hover:text-gray-100"
              >
                {item.label}
              </Link>
            </li>
          ))}
        </ul>
      )}
    </li>
  );
};

export default SidebarItem;
