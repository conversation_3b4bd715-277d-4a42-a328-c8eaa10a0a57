import React, { useEffect, useState } from "react";
import { Search } from "lucide-react";
import { useRouter } from "next/router";
import { useDebouncedCallback } from "use-debounce";

const CustomerSearchBar = () => {
  const [searchText, setSearchText] = useState("");
  const { query, push, pathname } = useRouter();
  const { q } = query || {};

  const processSearch = useDebouncedCallback((search = "") => {
    const { q: _, ...otherQueries } = query || {};
    if (search) {
      push({ pathname, query: { ...otherQueries, q: search, page: "1" } });
      return;
    }
    push({ pathname, query: { ...otherQueries, page: "1" } });
  }, 1000);

  useEffect(() => {
    setSearchText(q?.toString() || "");
  }, [q]);
  return (
    <div className="mb-6">
      <div className="relative">
        <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
          <Search className="h-5 w-5 text-gray-400" />
        </div>
        <input
          type="text"
          value={searchText}
          onChange={(e) => {
            const value = e?.target?.value;
            setSearchText(value);
            processSearch(value);
          }}
          placeholder="Search customers by name, email, ID, or phone..."
          className="block w-full pl-10 pr-3 py-2 border border-gray-300 rounded-md leading-5 bg-white placeholder-gray-500 focus:outline-none focus:placeholder-gray-400 focus:ring-1 focus:ring-primary focus:border-primary sm:text-sm"
        />
      </div>
    </div>
  );
};

export default CustomerSearchBar;
