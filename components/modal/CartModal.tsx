import React, { useEffect } from "react";
import Modal from "../Modal";
import useCart from "@/hooks/useCart";
import { XIcon } from "lucide-react";
import Button from "../Button";
import CartCardLoader from "../cart/CartCardLoader";
import CartCard from "../cart/CartCard";
import EmptyContainer from "../status/EmptyContainer";
import ErrorContainer from "../status/ErrorContainer";
import { useRouter } from "next/router";
import useUser from "@/hooks/useUser";

const CartModal = () => {
  const { userToken } = useUser();

  const {
    shouldOpenCart,
    setShouldOpenCart,
    carts,
    cartFetchingError,
    getCart
    // getLocalStorageCart
  } = useCart();
  const { push, pathname } = useRouter();

  const isCheckout = pathname === "/checkout";

  const totalPrice = (carts || []).reduce(
    (sum, item) => sum + item?.price?.amount,
    0
  );

  useEffect(() => {
    setShouldOpenCart(false);
  }, [pathname, setShouldOpenCart]);

  return (
    <Modal
      opened={shouldOpenCart}
      onClose={() => setShouldOpenCart(false)}
      position="top-right"
      dialogEnterFromAnimation="translate-x-1/2 opacity-0"
      dialogEnterToAnimation="translate-x-0 opacity-100"
      dialogLeaveToAnimation="translate-x-1/2 opacity-0"
    >
      <div className="w-screen h-screen bg-white max-w-[35rem] flex flex-col gap-10 p-5">
        <div className="flex items-center justify-between">
          <div className="flex items-center gap-2">
            <h1 className="text-2xl font-semibold">My Cart </h1>
            <span className="inline-flex size-7 bg-slate-200 rounded-full items-center justify-center text-sm font-medium">
              {(carts || [])?.length}
            </span>
          </div>

          <button
            title="Close cart"
            onClick={() => setShouldOpenCart(false)}
            aria-label="Close cart"
            className="hover:scale-125 transition-transform duration-200 ease-in-out "
          >
            <XIcon />
          </button>
        </div>

        <div className="flex-1 overflow-y-auto gap-6 flex flex-col">
          {!carts && !cartFetchingError && (
            <>
              {new Array(4).fill(0).map((_, index) => (
                <CartCardLoader key={index} />
              ))}
            </>
          )}

          {carts && !cartFetchingError && (
            <>
              {carts.length > 0 &&
                carts.map((cartItem) => (
                  <CartCard {...cartItem} key={cartItem?.id} />
                ))}

              {carts.length < 1 && (
                <EmptyContainer description="No cart at the moment" />
              )}
            </>
          )}

          {cartFetchingError && (
            <ErrorContainer error={cartFetchingError} retryFunction={getCart} />
          )}
        </div>
        {(carts || []).length > 0 && (
          <div className="flex flex-col gap-4">
            <div className="flex items-center justify-between py-4 border-t border-b">
              <p className="opacity-60">Subtotal:</p>
              <p className="font-bold">{totalPrice}</p>
            </div>

            <Button
              onClick={() => {
                if (!userToken) {
                  push("/auth?redirect=/checkout");
                  return;
                }
                if (isCheckout) {
                  setShouldOpenCart(false);
                  return;
                }
                push("/checkout");
              }}
              variant="secondary"
              className="rounded-none"
            >
              Checkout
            </Button>
          </div>
        )}
      </div>
    </Modal>
  );
};

export default CartModal;
