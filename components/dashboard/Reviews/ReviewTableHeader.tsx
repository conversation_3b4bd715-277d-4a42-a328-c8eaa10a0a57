import React from "react";

import CheckBox from "@/components/general/CheckBox";

interface ReviewTableHeaderProps {
  isAllSelected: boolean;
  onSelectAllReviews: (checked: boolean) => void;
}

const columns = [
  { id: "product", label: "Product", sortable: true },
  { id: "reviewId", label: "Review ID", sortable: true },
  { id: "date", label: "Date", sortable: true },
  { id: "customer", label: "Customer", sortable: false },
  { id: "rating", label: "Rating", sortable: true },
  { id: "comment", label: "Comment", sortable: false },
  { id: "reviewStatus", label: "Status", sortable: false }
];

const ReviewTableHeader: React.FC<ReviewTableHeaderProps> = ({
  isAllSelected,
  onSelectAllReviews
}) => {
  return (
    <thead className="bg-gray-50">
      <tr>
        <th className="px-4 py-3 w-[3rem] text-left">
          <CheckBox
            checked={isAllSelected}
            onChange={(e: React.ChangeEvent<HTMLInputElement>) =>
              onSelectAllReviews(e.target.checked)
            }
          />
        </th>
        {columns.map((column) => (
          <th
            key={column.id}
            className="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider"
          >
            <div className="flex items-center space-x-1">
              <span>{column.label}</span>
            </div>
          </th>
        ))}
        <th className="px-4 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
          Actions
        </th>
      </tr>
    </thead>
  );
};

export default ReviewTableHeader;
