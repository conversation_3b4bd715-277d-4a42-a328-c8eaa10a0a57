import React, { useCallback, useEffect, useState } from "react";
import InputField from "../general/InputField";
import useUser from "@/hooks/useUser";
import useCart from "@/hooks/useCart";
import { EditIcon, Mail, Map, Phone, Plus } from "lucide-react";
import Button from "../Button";
import { Controller, useForm } from "react-hook-form";
import ErrorContainer from "../status/ErrorContainer";
import { patchData, postData } from "@/api";
import { UserAddressDetailsType } from "@/store/useUserStore";
import { constructErrorMessage } from "@/utils/functions";
import { toast } from "react-toastify";
import Label from "../general/Label";
import Autocomplete from "react-google-autocomplete";

// Google Places API types
interface GooglePlaceData {
  address_components: Array<{
    long_name: string;
    short_name: string;
    types: string[];
  }>;
  formatted_address: string;
  geometry: {
    location: {
      lat: () => number;
      lng: () => number;
    };
  };
  name?: string;
  place_id: string;
  types: string[];
}

const AddressValue: React.FC<{ icon: React.ReactNode; value: string }> = ({
  icon,
  value
}) => {
  return (
    <div className="flex items-center gap-2">
      <span className="text-secondary-300">{icon}</span>
      <span className="opacity-60">{value}</span>
    </div>
  );
};

export type AddressBodyType = {
  address: string;
  landMark?: string;
  city: string;
  state: string;
  country: string;
  latitude: number;
  longitude: number;
  firstName: string;
  lastName: string;
  email: string;
  mobileNumber: string;
  zipCode: string;
};
const defaultValues: AddressBodyType = {
  address: "",
  city: "",
  state: "",
  country: "",
  latitude: 0,
  longitude: 0,
  firstName: "",
  lastName: "",
  email: "",
  mobileNumber: "",
  zipCode: ""
};

const labelClassName = "text-sm";
const Address = () => {
  const {
    register,
    handleSubmit,
    setError,
    control,
    setValue,
    reset,
    trigger,
    clearErrors,
    formState: { errors, isSubmitting, isValid }
  } = useForm({
    defaultValues
  });
  const {
    userAddresses,
    fetchingAddressError,
    getUserAddresses,
    updateAddress,
    setShouldOpenAddressModal
  } = useUser();
  const [shouldAllowAddAddress, setShouldAllowAddAddress] = useState(false);
  const [isAddressEdit, setIsAddressEdit] = useState(false);

  const { selectedAddress, setSelectedAddress } = useCart();
  const editAddress = useCallback(
    async (body: AddressBodyType) => {
      try {
        const { data } = await patchData<
          AddressBodyType,
          ApiCallResponseType<UserAddressDetailsType>
        >(`/address/${selectedAddress?.id}`, body);
        updateAddress(data?.data);
        setSelectedAddress(data?.data);
        reset(defaultValues);
        setIsAddressEdit(false);
        toast("Address updated successfully");
      } catch (error: unknown) {
        toast(
          constructErrorMessage(
            error as ApiErrorResponseType,
            "Error whilst updating address!"
          )
        );
        const errorObject = (error as ApiErrorResponseType)?.response?.data
          ?.errors;
        if (errorObject) {
          const errorKeys = Object.keys(errorObject || {});
          errorKeys.forEach((key) => {
            setError(key as keyof AddressBodyType, {
              message: errorObject[key],
              type: "value"
            });
          });
        }
      }
    },
    [selectedAddress, setError, reset, setSelectedAddress, updateAddress]
  );
  const addAddress = useCallback(
    async (body: AddressBodyType) => {
      try {
        const { data } = await postData<
          AddressBodyType,
          ApiCallResponseType<UserAddressDetailsType>
        >("/address", body);
        updateAddress(data?.data);
        setSelectedAddress(data?.data);
        setShouldAllowAddAddress(false);
        setIsAddressEdit(false);
        reset(defaultValues);
      } catch (error: unknown) {
        toast(
          constructErrorMessage(
            error as ApiErrorResponseType,
            "Error whilst updating address!"
          )
        );
        const errorObject = (error as ApiErrorResponseType)?.response?.data
          ?.errors;
        if (errorObject) {
          const errorKeys = Object.keys(errorObject || {});
          errorKeys.forEach((key) => {
            setError(key as keyof AddressBodyType, {
              message: errorObject[key],
              type: "value"
            });
          });
        }
      }
    },
    [updateAddress, setSelectedAddress, reset, setError]
  );

  const handleOnSubmit = useCallback(
    (body: AddressBodyType) => {
      if (isAddressEdit) {
        return editAddress(body);
      }

      return addAddress(body);
    },
    [isAddressEdit, addAddress, editAddress]
  );
  useEffect(() => {
    if (!selectedAddress) {
      setShouldAllowAddAddress(false);
      return;
    }
    if (!isAddressEdit) {
      reset(defaultValues);
      return;
    }

    reset({
      ...defaultValues,
      address: selectedAddress?.address,
      city: selectedAddress?.city,
      state: selectedAddress?.state,
      country: selectedAddress?.country,
      latitude: selectedAddress?.latitude,
      longitude: selectedAddress?.longitude,
      firstName: selectedAddress?.firstName,
      lastName: selectedAddress?.lastName,
      email: selectedAddress?.email,
      mobileNumber: selectedAddress?.mobileNumber,
      zipCode: selectedAddress?.zipCode
    });
  }, [selectedAddress, isAddressEdit, reset]);
  if (fetchingAddressError) {
    return (
      <ErrorContainer
        error={"Error fetching address list. Please click retry!"}
        retryFunction={getUserAddresses}
      />
    );
  }
  return (
    <>
      {selectedAddress && !shouldAllowAddAddress && (
        <div className="flex flex-col gap-3">
          <div className="flex flex-col gap-4 p-5 bg-secondary/20 rounded-md">
            <div className="flex items-center gap-10 justify-between">
              <h1 className="font-semibold text-lg">Delivery information</h1>
              <button
                title="Edit address"
                aria-label="Edit address"
                onClick={() => {
                  setIsAddressEdit(true);
                }}
                className="inline-flex items-center gap-1 text-secondary-300 text-sm mr-10"
              >
                <span>
                  <EditIcon size={15} />
                </span>
                <span>Edit</span>
              </button>
            </div>
            <div className="flex flex-col gap-2">
              <h2 className="font-semibold">
                {selectedAddress?.firstName} {selectedAddress?.lastName}
              </h2>
              <AddressValue
                icon={<Map size={16} />}
                value={selectedAddress?.address}
              />
              <AddressValue
                icon={<Phone size={16} />}
                value={selectedAddress?.mobileNumber}
              />
              <AddressValue
                icon={<Mail size={16} />}
                value={selectedAddress?.email}
              />
            </div>
          </div>
          <div className="flex justify-between items-center gap-10">
            <Button
              onClick={() => {
                setShouldOpenAddressModal(true);
              }}
              variant="transparent"
              className="border border-dotted border-secondary text-secondary inline-flex items-center gap-2"
            >
              Change address
            </Button>

            <Button
              onClick={() => {
                setShouldAllowAddAddress(true);
              }}
              variant="transparent"
              className="border inline-flex items-center gap-2 border-secondary text-secondary"
            >
              <span>
                <Plus size={15} />
              </span>
              <span>Add address</span>
            </Button>
          </div>
        </div>
      )}

      {((userAddresses && userAddresses.length < 1) ||
        shouldAllowAddAddress ||
        isAddressEdit) && (
        <form
          onSubmit={handleSubmit(handleOnSubmit)}
          className="flex flex-col gap-6 border rounded-md p-5"
        >
          <h1 className="font-semibold">Delivery Information</h1>

          <div className="grid grid-cols-1 sm:grid-cols-2 items-center gap-10">
            <InputField
              label="First name"
              labelClassName={labelClassName}
              placeholder="E.G John"
              error={errors?.firstName?.message}
              {...register("firstName", {
                required: "Please provide your first name"
              })}
            />
            <InputField
              label="Last name"
              labelClassName={labelClassName}
              placeholder="E.G Doe"
              error={errors?.lastName?.message}
              {...register("lastName", {
                required: "Please provide your last name"
              })}
            />
          </div>

          <Controller
            control={control}
            name="address"
            rules={{
              required: "Please provide your delivery address!"
            }}
            render={({
              field: { value, onChange, ref },
              fieldState: { error }
            }) => (
              <div className={`flex flex-col gap-2`}>
                <div className={`flex flex-col gap-2`}>
                  <Label>Address</Label>
                  <div
                    className={`w-full relative flex items-stretch justify-center`}
                  >
                    <Autocomplete
                      ref={ref}
                      defaultValue={value}
                      value={value}
                      onChange={(e: React.ChangeEvent<HTMLInputElement>) => {
                        onChange((e?.target as HTMLInputElement)?.value);
                      }}
                      options={{
                        types: ["geocode"]
                      }}
                      className={`w-full peer/radio-btn border ${
                        error?.message && "!border-red-400"
                      } py-3 px-5 outline-none rounded-md w-full h-full`}
                      apiKey={process.env.NEXT_PUBLIC_GOOGLE_MAP_API_KEY}
                      onPlaceSelected={(place: GooglePlaceData) => {
                        if (!place || !place?.address_components) {
                          return;
                        }
                        const addressComponents = place?.address_components;
                        const { lat, lng } = place?.geometry?.location || {};
                        const getComponent = (type: string) =>
                          addressComponents.find(
                            (c: { types: string[]; long_name: string }) =>
                              c.types.includes(type)
                          )?.long_name || "";
                        const locationData = {
                          landMark: place.name || "",
                          city:
                            getComponent("locality") ||
                            getComponent("administrative_area_level_2"),
                          state: getComponent("administrative_area_level_1"),
                          country: getComponent("country"),
                          zipCode: getComponent("postal_code")
                        };
                        onChange(place?.formatted_address);
                        setValue("zipCode", locationData?.zipCode);
                        setValue("state", locationData?.state);
                        setValue("city", locationData?.city);
                        if (locationData?.landMark) {
                          setValue("landMark", locationData?.landMark);
                        }
                        setValue("country", locationData?.country);
                        if (lat && lng) {
                          const latitude = lat();
                          const longitude = lng();
                          setValue("latitude", latitude);
                          setValue("longitude", longitude);
                        }
                        clearErrors([
                          "address",
                          "city",
                          "zipCode",
                          "state",
                          "landMark",
                          "country",
                          "latitude",
                          "longitude"
                        ]);
                        trigger([
                          "address",
                          "city",
                          "zipCode",
                          "state",
                          "landMark",
                          "country",
                          "latitude",
                          "longitude"
                        ]);
                      }}
                    />
                  </div>
                  <p className="text-red-600 text-sm">
                    {error?.message ||
                      errors?.zipCode?.message ||
                      errors?.city?.message ||
                      errors?.state?.message ||
                      errors?.landMark?.message ||
                      errors?.country?.message ||
                      errors?.latitude?.message ||
                      errors?.longitude?.message}
                  </p>
                </div>
              </div>
            )}
          />

          <div className="grid grid-cols-1 sm:grid-cols-2 items-center gap-10">
            <InputField
              label="Mobile Number"
              labelClassName={labelClassName}
              placeholder="E.G +971 50 123 4567"
              error={errors?.mobileNumber?.message}
              {...register("mobileNumber", {
                required: true
              })}
            />
            <InputField
              label="Email address"
              labelClassName={labelClassName}
              placeholder="E.G <EMAIL>"
              error={errors?.email?.message}
              {...register("email", {
                required: true
              })}
            />
          </div>
          <div className="flex items-center justify-between">
            {((shouldAllowAddAddress && (userAddresses || [])?.length > 0) ||
              isAddressEdit) && (
              <Button
                onClick={() => {
                  setShouldAllowAddAddress(false);
                  setIsAddressEdit(false);
                }}
                type="button"
                variant="transparent"
                className="border border-secondary border-dotted text-secondary"
              >
                Cancel
              </Button>
            )}
            <Button
              disabled={isSubmitting || !isValid}
              loading={isSubmitting}
              type="submit"
              variant="secondary"
            >
              {isSubmitting ? "Submitting..." : "Submit"}
            </Button>
          </div>
        </form>
      )}
    </>
  );
};

export default Address;
