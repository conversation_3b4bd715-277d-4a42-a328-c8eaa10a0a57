import React from "react";
import SectionContainer from "../layouts/SectionContainer";
import { ArrowLeft } from "lucide-react";
import { useRouter } from "next/router";
import Link from "next/link";
import { useParams } from "next/navigation";
import { formatText } from "@/utils/functions";
import { BsFilter } from "react-icons/bs";
import { siteName, websiteUrl } from "@/utils/variables";
import Head from "next/head";

const BreadCrumbs = () => {
  const { back } = useRouter();
  const param = useParams();
  const { slug } = param || {};
  const stringifiedSlug = slug?.toString() || "all";
  const isAllProducts = stringifiedSlug === "all";
  const itemList = stringifiedSlug?.split(",") || [];
  const canonicalUrl = `${websiteUrl}/products/${itemList?.join("/")}`;
  const categoryName = itemList[itemList.length - 1];
  const title = `${isAllProducts ? "" : categoryName} Products | ${siteName}`;
  const description = `Browse our curated collection of quality ${categoryName} at Buabed Banafa. Discover luxury, craftsmanship, and timeless fragrance in every product.`;

  return (
    <>
      <Head>
        <link rel="canonical" href={canonicalUrl} />
        <title>{title}</title>
        <meta property="og:title" content={title} />
        <meta name="description" content={description} />
        <meta property="og:description" content={description} />
        <meta name="twitter:description" content={description} />
      </Head>
      <SectionContainer
        className="sticky md:relative top-0 md:top-[unset] z-10 bg-white border-b md:border-b-none"
        contentContainerClassName="flex items-center gap-4 justify-between flex-wrap"
      >
        <div className="flex items-center gap-3">
          <div className="flex items-center gap-2 py-4">
            <button
              onClick={back}
              title="go back"
              className=" text-slate-500 text-sm inline-flex items-center gap-2"
            >
              <span className="inline-flex border border-slate-200 px-2 py-1">
                <ArrowLeft size={15} />
              </span>
              <span className="inline-block">Go back</span>
            </button>
          </div>
          <div className="flex items-center gap-1">
            <Link href="/" className="hover:text-secondary hover:font-semibold">
              Home
            </Link>
            <span className="inline-block">/</span>
            <Link
              href="/products"
              className="hover:text-secondary hover:font-semibold"
            >
              Products
            </Link>
            {!isAllProducts && (
              <>
                <span className="inline-block">/</span>
                {itemList?.length > 0 &&
                  itemList?.map((item, index) => {
                    const isLast = index === itemList?.length - 1;
                    return (
                      <div
                        key={index}
                        className="inline-flex items-center gap-1"
                      >
                        <Link
                          className={`${isLast && "opacity-60"} ${
                            !isLast &&
                            "hover:text-secondary hover:font-semibold"
                          }`}
                          key={index}
                          href={`/products/${itemList
                            ?.slice(0, index + 1)
                            ?.join("/")}`}
                        >
                          {formatText(item)}
                        </Link>
                        {!isLast && <span>/</span>}
                      </div>
                    );
                  })}
              </>
            )}
          </div>
        </div>
        <button
          title="Open filter"
          aria-label="Open filter"
          className="md:hidden inline-flex text-xl"
        >
          <span>
            <BsFilter />
          </span>
        </button>
      </SectionContainer>
    </>
  );
};

export default BreadCrumbs;
