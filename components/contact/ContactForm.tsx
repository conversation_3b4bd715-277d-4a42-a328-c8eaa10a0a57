import React from 'react';
import SectionContainer from '../layouts/SectionContainer';
import InputField from '../general/InputField';
import SelectBox from '../general/SelectBox';
import TextArea from '../general/TextArea';
import RadioButton from '../general/RadioButton';
import Image from 'next/image';
import { ContactImage } from '@/assets/images';

const ContactForm = () => {
	return (
		<SectionContainer contentContainerClassName='flex flex-col lg:flex-row lg:items-center justify-between gap-9 my-20 w-full'>
			<div className='flex flex-col gap-4 flex-1'>
				<div>
					<h1 className='text-3xl font-semibold text-gray-900'>Get in touch</h1>
					<p className='text-gray-600 text-lg'>
						Our friendly team would love to hear from you.
					</p>
				</div>

				<div className='flex items-center gap-4'>
					<InputField
						id='firstName'
						name='firstName'
						type='text'
						label='First name'
						placeholder='First name'
						className='w-full'
					/>
					<InputField
						id='lastName'
						name='lastName'
						type='text'
						label='Last name'
						placeholder='Last name'
						className='w-full'
					/>
				</div>

				<div>
					<InputField
						id='email'
						name='email'
						type='email'
						label='Email'
						placeholder='<EMAIL>'
					/>
				</div>

				<div className='flex flex-col gap-2'>
					<label className='text-sm font-medium'>Phone number</label>
					<div className='flex gap-0'>
						<SelectBox
							label=''
							className='w-1/4'
							inputClassName='rounded-r-none border-r-0'
							options={[
								{ value: '+1', label: 'US (+1)' },
								{ value: '+44', label: 'UK (+44)' },
							]}
							placeholder='UK (+44)'
						/>

						<InputField
							label=''
							className='w-2/3'
							inputClassName='rounded-l-none border-l-0'
							placeholder='(*************'
							type='tel'
						/>
					</div>
				</div>

				<div>
					<TextArea
						id='message'
						name='message'
						label='Message'
						placeholder='Leave us a message...'
					/>
				</div>

				<div className='flex items-center gap-2'>
					<RadioButton
						type='checkbox'
						id='privacy'
						label='You agree to our friendly privacy policy.'
					/>
				</div>

				<button className='w-full bg-secondary-300 text-white py-4 rounded-lg font-medium hover:bg-secondary-400 focus:scale-95 transition-all duration-200'>
					Send message
				</button>
			</div>

			<div className=' w-[57%] space-y-8'>
				<Image
					src={ContactImage}
					alt='company'
					className='object-cover object-center'
				/>
			</div>
		</SectionContainer>
	);
};

export default ContactForm;
