import React from "react";
import DashboardLayout from "@/components/dashboard/DashboardLayout";
import AllCategories from "@/components/dashboard/Category/AllCategories";
import isAdmin from "@/hooks/isAdmin";
import Head from "next/head";
import { siteName } from "@/utils/variables";

interface CategoriesPageProps extends React.FC {
  hideFooter?: boolean;
  hideNav?: boolean;
}

const CategoriesPage: CategoriesPageProps = () => {
  return (
    <>
      <Head>
        <title>Product categories | Admin | {siteName}</title>
      </Head>
      <DashboardLayout>
        <div className="p-8">
          <AllCategories />
        </div>
      </DashboardLayout>
    </>
  );
};

CategoriesPage.hideFooter = true;
CategoriesPage.hideNav = true;

export default isAdmin(CategoriesPage);
