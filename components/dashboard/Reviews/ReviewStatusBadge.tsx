import React from "react";

export type ReviewStatus = "APPROVED" | "DISAPPROVED" | "PENDING";

interface ReviewStatusBadgeProps {
  status?: ReviewStatus;
}

const ReviewStatusBadge: React.FC<ReviewStatusBadgeProps> = ({ status }) => {
  let bgColor = "";
  let textColor = "";

  switch (status) {
    case "APPROVED":
      bgColor = "bg-green-100";
      textColor = "text-green-800";
      break;
    case "PENDING":
      bgColor = "bg-blue-100";
      textColor = "text-blue-800";
      break;
    case "DISAPPROVED":
      bgColor = "bg-red-100";
      textColor = "text-red-800";
      break;
    default:
      bgColor = "bg-gray-100";
      textColor = "text-gray-800";
  }

  return (
    <span
      className={`px-3 py-1 rounded-full text-xs font-medium ${bgColor} ${textColor}`}
    >
      {status}
    </span>
  );
};

export default ReviewStatusBadge;
