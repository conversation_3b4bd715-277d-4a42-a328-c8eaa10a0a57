"use client";
import React from "react";
import { usePathname } from "next/navigation"; // Import usePathname
import TopBanner from "../navbar/TopBanner";
import SecondaryNav from "../navbar/SecondaryNav";
import SearchAndEssentials from "../navbar/SearchAndEssentials";
import MainNav from "../navbar/MainNav";

const Navbar: React.FC = () => {
  const pathname = usePathname(); // Get the current route

  // Check if the current route is /auth
  const isAuthRoute = pathname === "/auth";

  return (
    <div className="w-full font-[500]">
      <TopBanner />
      <SecondaryNav />
      {!isAuthRoute && <SearchAndEssentials />}
      {!isAuthRoute && <MainNav />}
    </div>
  );
};

export default Navbar;
