import React from "react";
import DashboardLayout from "@/components/dashboard/DashboardLayout";
import CustomerDetails from "@/components/dashboard/Customers/CustomerDetails";
import { useRouter } from "next/router";
import isAdmin from "@/hooks/isAdmin";
import Head from "next/head";

interface CustomerDetailsPageProps extends React.FC {
  hideFooter?: boolean;
  hideNav?: boolean;
}

const CustomerDetailsPage: CustomerDetailsPageProps = () => {
  const router = useRouter();
  const { customerId } = router.query;

  return (
    <>
      <Head>
        <title>User details</title>
      </Head>
      <DashboardLayout>
        <div className="p-8">
          <CustomerDetails userId={customerId as string} />
        </div>
      </DashboardLayout>
    </>
  );
};

CustomerDetailsPage.hideFooter = true;
CustomerDetailsPage.hideNav = true;

export default isAdmin(CustomerDetailsPage);
