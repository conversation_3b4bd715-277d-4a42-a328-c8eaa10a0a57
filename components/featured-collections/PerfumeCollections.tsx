import React from "react";
import SectionContainer from "../layouts/SectionContainer";
import SectionHeader from "../general/SectionHeader";
import { FaAngleRight } from "react-icons/fa";
import Image from "next/image";
import {
  featuredImageOne,
  featuredImageThree,
  featuredImageTwo
} from "@/assets/images";
import { siteName } from "@/utils/variables";
import { useRouter } from "next/router";

const dividedContainerClassName =
  "lg:flex-1 h-[clamp(32rem,50vw,50rem)] rounded-lg overflow-hidden uppercase overflow-hidden relative";

const Tag: React.FC<{ text: string }> = ({ text }) => (
  <span className="text-xs font-medium px-2 py-1 text-white bg-tetiary rounded-full">
    {text}
  </span>
);

const ContainerInnerContent: React.FC<{ tagText: string; text: string }> = ({
  tagText,
  text
}) => {
  const { push } = useRouter();
  return (
    <div className="h-full w-full flex flex-col items-start justify-end p-10 bg-black/30 text-white relative">
      <Tag text={tagText} />
      <h1 className="text-[clamp(1.2rem,1.7vw,1.7rem)] max-w-[30rem] font-medium">
        {text}
      </h1>
      <button
        title="Shop now"
        onClick={() => {
          push("/products");
        }}
        className="font-medium pl-5 inline-flex items-center gap-1 py-2"
      >
        <span>Shop now</span>
        <span>
          <FaAngleRight />
        </span>
      </button>
    </div>
  );
};

const PerfumeCollections = () => {
  const { push } = useRouter();
  return (
    <SectionContainer contentContainerClassName="flex flex-col gap-6">
      <SectionHeader title="Featured collection" />
      <div className="flex flex-col lg:flex-row gap-6">
        <div className={`${dividedContainerClassName} bg-gray-100`}>
          <Image
            fill
            src={featuredImageOne}
            className="object-cover object-left"
            alt={`${siteName}`}
          />
          <div className="flex flex-col gap-20 justify-between h-full w-full p-10 pb-24 bg-black/30 text-white relative">
            <div className="flex items-center justify-between">
              <Tag text="WORLD GIRLFRIEND’S DAY" />
            </div>
            <div className="flex flex-col gap-2">
              <h1 className="text-[clamp(1.2rem,1.7vw,1.7rem)] max-w-[30rem] font-medium">
                Free Delivery on all Perfume ordered until February 28
              </h1>
              <p className="opacity-60">When you spend £200 or more</p>
              <button
                title="Shop now"
                onClick={() => {
                  push("/products");
                }}
                className="font-medium pl-5 inline-flex items-center gap-1 py-2"
              >
                <span>Shop now</span>
                <span>
                  <FaAngleRight />
                </span>
              </button>
            </div>
          </div>
        </div>
        <div className={`${dividedContainerClassName} flex flex-col gap-6`}>
          <div className="flex flex-col bg-gray-100 flex-1 rounded-lg overflow-hidden relative">
            <Image
              fill
              src={featuredImageTwo}
              className="object-cover object-right"
              alt={`${siteName}`}
            />
            <ContainerInnerContent
              tagText="UP TO 80% OFF"
              text="Fragrances for him"
            />
          </div>
          <div className="flex flex-col bg-gray-100 flex-1 rounded-lg overflow-hidden relative">
            <Image
              fill
              src={featuredImageThree}
              className="object-cover object-left"
              alt={`${siteName}`}
            />
            <ContainerInnerContent
              tagText="UP TO 80% OFF"
              text="Fragrances for her"
            />
          </div>
        </div>
      </div>
    </SectionContainer>
  );
};

export default PerfumeCollections;
