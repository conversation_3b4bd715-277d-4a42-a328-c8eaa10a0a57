import React, { useEffect, useState } from "react";
import InputField from "../general/InputField";
import { CheckIcon, PlusIcon, SearchIcon } from "lucide-react";
import { useParams } from "next/navigation";
import { formatText } from "@/utils/functions";
import { useRouter } from "next/router";
import { useDebouncedCallback } from "use-debounce";
import useUser from "@/hooks/useUser";

const minMaxInputFieldClassName = "flex-1 min-w-[5rem]";

const FilterValueCard: React.FC<{
  value: string;
  isActive: boolean;
  action: () => void;
}> = ({ value, isActive, action }) => {
  return (
    <div className="flex items-center gap-2 py-3 border-b justify-between">
      <p>{value}</p>
      <button
        title={value}
        onClick={action}
        className={`opacity-60 ${isActive && "text-primary"}`}
      >
        {isActive && <CheckIcon size={15} />}
        {!isActive && <PlusIcon size={15} />}
      </button>
    </div>
  );
};

const FilterContainer = () => {
  const { userToken } = useUser();
  const { query, pathname, push } = useRouter();
  const { q, min, max, best_seller, latest, is_favorite } = query || {};
  const param = useParams();
  const { slug } = param || {};
  const [searchText, setSearchText] = useState(q?.toString() || "");
  const [maxAmount, setMaxAmount] = useState(min?.toString() || "");
  const [minAmount, setMinAmount] = useState(max?.toString() || "");
  const stringifiedSlug = slug?.toString();
  const itemList = stringifiedSlug?.split(",") || [];
  const hasBestSeller = best_seller?.toString() === "true";
  const hasLatest = latest?.toString() === "true";
  const hasFavorite = is_favorite?.toString() === "true";

  const processSearch = useDebouncedCallback((q: string) => {
    if (!q) {
      const { q: _, ...remainingQueries } = query;
      return push({
        pathname: pathname,
        query: { ...remainingQueries }
      });
    }
    push({
      pathname: pathname,
      query: { ...query, q }
    });
  }, 1000);

  const processMinPrice = useDebouncedCallback((min: string) => {
    if (!min) {
      const { min: _, ...remainingQueries } = query;
      return push({
        pathname: pathname,
        query: { ...remainingQueries }
      });
    }
    push({
      pathname: pathname,
      query: { ...query, min }
    });
  }, 1000);
  const processMaxPrice = useDebouncedCallback((max: string) => {
    if (!max) {
      const { max: _, ...remainingQueries } = query;
      return push({
        pathname: pathname,
        query: { ...remainingQueries }
      });
    }
    push({
      pathname: pathname,
      query: { ...query, max }
    });
  }, 1000);

  useEffect(() => {
    const searchTextFromUrl = q?.toString() || "";
    setSearchText(searchTextFromUrl);
  }, [q]);

  useEffect(() => {
    const minAmountFromUrl = min?.toString() || "";
    setMinAmount(minAmountFromUrl);
  }, [min]);

  useEffect(() => {
    const maxAmountFromUrl = max?.toString() || "";
    setMaxAmount(maxAmountFromUrl);
  }, [max]);

  return (
    <div className="w-full hidden md:flex max-w-[12rem] lg:max-w-[18rem] flex-col gap-6">
      <div className="flex flex-col w-full gap-2">
        <h1 className="font-bold md:text-lg">
          {formatText(itemList?.[itemList.length - 1] || "Filters")}
        </h1>
        <InputField
          placeholder="Search items"
          leftIcon={<SearchIcon size={15} />}
          value={searchText}
          onChange={(e) => {
            const value = (e?.target as HTMLInputElement)?.value;
            setSearchText(value);
            processSearch(value);
          }}
          inputClassName="!pl-10"
        />
      </div>
      <div className="flex flex-col gap-2">
        <p className="font-medium">Price, $</p>
        <div className="flex items-stretch gap-4 w-full">
          <InputField
            value={minAmount}
            className={minMaxInputFieldClassName}
            placeholder="Min"
            onChange={(e) => {
              const value = (e?.target as HTMLInputElement)?.value;
              if (isNaN(Number(value))) {
                return;
              }
              setMinAmount(value);
              processMinPrice(value);
            }}
          />
          <InputField
            value={max?.toString()}
            className={maxAmount}
            placeholder="Max"
            onChange={(e) => {
              const value = (e?.target as HTMLInputElement)?.value;
              if (isNaN(Number(value))) {
                return;
              }
              setMaxAmount(value);
              processMaxPrice(value);
            }}
          />
        </div>
      </div>
      <FilterValueCard
        value="Best seller"
        isActive={hasBestSeller}
        action={() => {
          if (hasBestSeller) {
            const { best_seller: _, ...remainingQuery } = query;
            return push({
              pathname,
              query: { ...remainingQuery }
            });
          }
          return push({
            pathname,
            query: { ...query, best_seller: true }
          });
        }}
      />
      <FilterValueCard
        value="Latest"
        isActive={hasLatest}
        action={() => {
          if (hasLatest) {
            const { latest: _, ...remainingQuery } = query;
            return push({
              pathname,
              query: { ...remainingQuery }
            });
          }
          return push({
            pathname,
            query: { ...query, latest: true }
          });
        }}
      />
      {userToken && (
        <FilterValueCard
          value="Favorite"
          isActive={hasFavorite}
          action={() => {
            if (hasFavorite) {
              const { is_favorite: _, ...remainingQuery } = query;
              return push({
                pathname,
                query: { ...remainingQuery }
              });
            }
            return push({
              pathname,
              query: { ...query, is_favorite: true }
            });
          }}
        />
      )}
      {/* <FilterValueCard value="Exclusive" isActive={false} action={() => {}} /> */}
      {/* <FilterValueCard
        value="Special Offers"
        isActive={false}
        action={() => {}}
      /> */}
    </div>
  );
};

export default FilterContainer;
