import React from "react";
import { useRouter } from "next/router";
import { useQuery } from "@tanstack/react-query";
import { getData } from "@/api";
import SectionContainer from "@/components/layouts/SectionContainer";
import ErrorContainer from "@/components/status/ErrorContainer";
import EmptyContainer from "@/components/status/EmptyContainer";
import { constructErrorMessage, formatDate } from "@/utils/functions";
import {
  ArrowLeft,
  PackageCheck,
  Truck,
  CreditCard,
  CalendarClock
} from "lucide-react";
import Image from "next/image";
import Button from "@/components/Button";
import { useParams } from "next/navigation";
import protectRoute from "@/hooks/protectRoute";
import Head from "next/head";
import { siteName } from "@/utils/variables";
import { OrderDetailsType, OrderStatusType } from "@/store/useOrderStore";

export const fetchOrderDetails = async (orderId: string) => {
  const { data } = await getData<ApiCallResponseType<OrderDetailsType>>(
    `/order/${orderId}`
  );
  return data.data;
};

const OrderDetailsPage = () => {
  const params = useParams();
  const { orderId } = params || {};
  const router = useRouter();

  const {
    data: order,
    isLoading,
    isError,
    error,
    refetch
  } = useQuery({
    queryKey: [`order-details-${orderId}`],
    queryFn: () => fetchOrderDetails(orderId as string),
    enabled: !!orderId,
    retry: 1
  });

  const getStatusColorClass = (status: OrderStatusType) => {
    switch (status) {
      case "PENDING":
        return "bg-yellow-100 text-yellow-800";
      // case "processing":
      //   return "bg-blue-100 text-blue-800";
      case "PAID":
        return "bg-green-100 text-green-800";
      case "SHIPPED":
        return "bg-purple-100 text-purple-800";
      case "DELIVERED":
        return "bg-teal-100 text-teal-800";
      case "CANCELLED":
        return "bg-red-100 text-red-800";
      // case "failed":
      //   return "bg-gray-100 text-gray-800";
      default:
        return "bg-gray-100 text-gray-800";
    }
  };

  return (
    <>
      <Head>
        <title>Order details | {siteName}</title>
      </Head>
      <SectionContainer contentContainerClassName="flex items-center gap-4">
        <div className="flex items-center gap-2 py-4">
          <button
            onClick={() => router.back()}
            title="go back"
            className="text-slate-500 text-sm inline-flex items-center gap-2"
          >
            <span className="inline-flex border border-slate-200 px-2 py-1">
              <ArrowLeft size={15} />
            </span>
            <span className="inline-block">Go back</span>
          </button>
        </div>
      </SectionContainer>

      <SectionContainer>
        <div className="py-8">
          <h1 className="text-2xl font-semibold mb-6">Order Details</h1>

          {isLoading && (
            <div className="space-y-4">
              {[...Array(4)].map((_, index) => (
                <div
                  key={index}
                  className="bg-gray-100 animate-pulse h-24 rounded-lg"
                />
              ))}
            </div>
          )}

          {isError && (
            <ErrorContainer
              error={constructErrorMessage(
                error as ApiErrorResponseType,
                "Failed to load order details."
              )}
              retryFunction={refetch}
            />
          )}

          {!isLoading && !isError && !order && (
            <EmptyContainer description="No order found." />
          )}

          {!isLoading && !isError && order && (
            <div className="space-y-6">
              {/* Order Summary */}
              <div className="bg-white border rounded-lg p-6 shadow-sm">
                <div className="flex flex-col md:flex-row justify-between mb-4">
                  <div>
                    <h2 className="text-xl font-semibold">
                      Order #{order.orderCode}
                    </h2>
                    <p className="text-gray-500 text-sm mt-1">
                      Placed on {formatDate(order.createdAt)}
                    </p>
                  </div>
                  <div className="mt-4 md:mt-0">
                    <span
                      className={`inline-block px-3 py-1 rounded-full text-sm font-medium capitalize ${getStatusColorClass(
                        order.status
                      )}`}
                    >
                      {order.status}
                    </span>
                  </div>
                </div>

                <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4 mt-6">
                  {/* Order Created */}
                  <div className="flex items-start gap-3">
                    <div className="bg-gray-100 p-2 rounded-full">
                      <CalendarClock size={20} className="text-gray-600" />
                    </div>
                    <div>
                      <p className="text-sm text-gray-500">Order Date</p>
                      <p className="font-medium">
                        {formatDate(order.createdAt)}
                      </p>
                    </div>
                  </div>

                  {/* Payment */}
                  <div className="flex items-start gap-3">
                    <div className="bg-gray-100 p-2 rounded-full">
                      <CreditCard size={20} className="text-gray-600" />
                    </div>
                    <div>
                      <p className="text-sm text-gray-500">Payment</p>
                      <p className="font-medium">
                        {order.paidAt ? formatDate(order.paidAt) : "Not paid"}
                      </p>
                    </div>
                  </div>

                  {/* Shipping */}
                  <div className="flex items-start gap-3">
                    <div className="bg-gray-100 p-2 rounded-full">
                      <Truck size={20} className="text-gray-600" />
                    </div>
                    <div>
                      <p className="text-sm text-gray-500">Shipping</p>
                      <p className="font-medium">
                        {order.shippedAt
                          ? formatDate(order.shippedAt)
                          : "Not shipped"}
                      </p>
                    </div>
                  </div>

                  {/* Delivery */}
                  <div className="flex items-start gap-3">
                    <div className="bg-gray-100 p-2 rounded-full">
                      <PackageCheck size={20} className="text-gray-600" />
                    </div>
                    <div>
                      <p className="text-sm text-gray-500">Delivery</p>
                      <p className="font-medium">
                        {order.deliveredAt
                          ? formatDate(order.deliveredAt)
                          : "Not delivered"}
                      </p>
                    </div>
                  </div>
                </div>
              </div>

              {/* Order Items */}
              <div className="bg-white border rounded-lg p-6 shadow-sm">
                <h2 className="text-lg font-semibold mb-4">
                  Order Items ({order.totalCarts || order.carts?.length || 0})
                </h2>
                <div className="divide-y">
                  {order.carts?.map((item, index) => (
                    <div
                      key={index}
                      className="py-4 flex flex-col sm:flex-row items-start gap-4"
                    >
                      {item.product?.images?.[0] && (
                        <div className="w-20 h-20 relative rounded overflow-hidden border bg-gray-50 flex-shrink-0">
                          <Image
                            src={item.product.images[0]}
                            alt={item.product?.name || "Product image"}
                            fill
                            className="object-cover"
                          />
                        </div>
                      )}
                      <div className="flex-1">
                        <p className="font-medium text-lg">
                          {item.product?.name ?? "Unnamed Product"}
                        </p>
                        <div className="grid grid-cols-1 sm:grid-cols-3 gap-2 mt-2">
                          <p className="text-sm text-gray-600">
                            <span className="font-medium">Quantity:</span>
                            {item.quantity}
                          </p>
                          <p className="text-sm text-gray-600">
                            <span className="font-medium">Price:</span>
                            {item.price?.formatted?.withCurrency ?? "N/A"}
                          </p>
                          <p className="text-sm text-gray-600">
                            <span className="font-medium">Subtotal:</span>
                            {item.price?.amount && item.quantity
                              ? {
                                  amount: item.price.amount * item.quantity,
                                  formatted: {
                                    withCurrency: `$${(
                                      item.price.amount * item.quantity
                                    ).toFixed(2)}`
                                  }
                                }.formatted.withCurrency
                              : "N/A"}
                          </p>
                        </div>
                      </div>
                    </div>
                  ))}
                </div>
              </div>

              {/* Order Cost Summary */}
              <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
                {/* Delivery Details */}
                <div className="md:col-span-2 bg-white border rounded-lg p-6 shadow-sm">
                  <h2 className="text-lg font-semibold mb-4">
                    Delivery Information
                  </h2>
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div>
                      <h3 className="font-medium text-gray-700 mb-2">
                        Shipping Address
                      </h3>
                      <p className="text-gray-600">
                        {order.deliveryDetails.firstName}
                        {order.deliveryDetails.lastName}
                      </p>
                      <p className="text-gray-600">
                        {order.deliveryDetails.address}
                      </p>
                      {order.deliveryDetails.landmark && (
                        <p className="text-gray-600">
                          Landmark: {order.deliveryDetails.landmark}
                        </p>
                      )}
                      <p className="text-gray-600">
                        {order.deliveryDetails.city},
                        {order.deliveryDetails.state}
                        {order.deliveryDetails.zipCode}
                      </p>
                      <p className="text-gray-600">
                        {order.deliveryDetails.country}
                      </p>
                    </div>
                    <div>
                      <h3 className="font-medium text-gray-700 mb-2">
                        Contact Information
                      </h3>
                      <p className="text-gray-600">
                        <span className="font-medium">Email:</span>
                        {order.deliveryDetails.email ||
                          order.user?.email ||
                          "N/A"}
                      </p>
                      <p className="text-gray-600">
                        <span className="font-medium">Phone:</span>
                        {order.deliveryDetails.mobileNumber || "N/A"}
                      </p>
                    </div>
                  </div>
                </div>

                {/* Payment Summary */}
                <div className="bg-white border rounded-lg p-6 shadow-sm">
                  <h2 className="text-lg font-semibold mb-4">
                    Payment Summary
                  </h2>
                  <div className="space-y-2">
                    <div className="flex justify-between">
                      <span className="text-gray-600">Subtotal</span>
                      <span>
                        {order.subTotal?.formatted?.withCurrency || "N/A"}
                      </span>
                    </div>
                    <div className="flex justify-between">
                      <span className="text-gray-600">Shipping Fee</span>
                      <span>
                        {order.shippingFee?.formatted?.withCurrency || "Free"}
                      </span>
                    </div>
                    {order.taxFee && order.taxFee.amount > 0 && (
                      <div className="flex justify-between">
                        <span className="text-gray-600">Tax</span>
                        <span>
                          {order.taxFee?.formatted?.withCurrency || "N/A"}
                        </span>
                      </div>
                    )}
                    {order.discount && order.discount.amount > 0 && (
                      <div className="flex justify-between">
                        <span className="text-gray-600">Discount</span>
                        <span className="text-green-600">
                          -{order.discount?.formatted?.withCurrency || "N/A"}
                        </span>
                      </div>
                    )}
                    {order.couponAmount && (
                      <div className="flex justify-between">
                        <span className="text-gray-600">
                          Coupon ({order.couponType})
                        </span>
                        <span className="text-green-600">
                          -
                          {order?.couponType === "FIXED" &&
                            (order?.couponAmount as AmountType)?.formatted
                              ?.withCurrency}
                          {order?.couponType === "PERCENTAGE" &&
                            `${order?.couponAmount?.toString()}%`}
                        </span>
                      </div>
                    )}
                    <div className="border-t pt-2 mt-2">
                      <div className="flex justify-between font-semibold text-lg">
                        <span>Total</span>
                        <span className="text-primary">
                          {order.total?.formatted?.withCurrency || "N/A"}
                        </span>
                      </div>
                    </div>
                  </div>
                </div>
              </div>

              <div className="flex justify-end pt-4">
                <Button variant="primary" onClick={() => router.back()}>
                  Back to Orders
                </Button>
              </div>
            </div>
          )}
        </div>
      </SectionContainer>
    </>
  );
};

export default protectRoute(OrderDetailsPage);
