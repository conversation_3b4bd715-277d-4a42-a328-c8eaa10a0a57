interface TabNavigationProps {
	showRegister: boolean;
	setShowRegister: (showRegister: boolean) => void;
}

const TabNavigation: React.FC<TabNavigationProps> = ({
	showRegister,
	setShowRegister,
}) => {
	return (
		<div className='flex relative border-b border-gray-200'>
			<button
				className={`flex-1 py-4 text-lg font-medium ${
					!showRegister ? 'text-secondary-300' : 'text-gray-500'
				}`}
				onClick={() => setShowRegister(false)}>
				Sign In
			</button>
			<button
				className={`flex-1 py-4 text-lg font-medium ${
					showRegister ? 'text-secondary-300' : 'text-gray-500'
				}`}
				onClick={() => setShowRegister(true)}>
				Register
			</button>
			{/* Sliding border */}
			<div
				className='absolute bottom-0 h-0.5 bg-secondary-300 transition-all duration-300 ease-in-out'
				style={{
					width: '50%',
					left: showRegister ? '50%' : '0',
				}}
			/>
		</div>
	);
};

export default TabNavigation;
