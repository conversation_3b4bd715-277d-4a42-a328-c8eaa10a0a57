import DashboardLayout from "@/components/dashboard/DashboardLayout";
import AllOrders from "@/components/dashboard/Orders/AllOrders";
import isAdmin from "@/hooks/isAdmin";
import { siteName } from "@/utils/variables";
import Head from "next/head";
import React from "react";

interface OrdersProps extends React.FC {
  hideFooter?: boolean;
  hideNav?: boolean;
}

const Orders: OrdersProps = () => {
  return (
    <>
      <Head>
        <title>Orders | Admin | {siteName}</title>
      </Head>
      <DashboardLayout>
        <div className="p-8">
          <AllOrders />
        </div>
      </DashboardLayout>
    </>
  );
};

Orders.hideFooter = true;
Orders.hideNav = true;
export default isAdmin(Orders);
