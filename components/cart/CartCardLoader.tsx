import React from "react";

const CartCardLoader = () => {
  return (
    <div className="flex items-center gap-4">
      <div className="size-24 bg-slate-200 animate-pulse" />

      <div className="flex-1 flex flex-col gap-2">
        <div className="h-3 bg-slate-200 rounded-full animate-pulse max-w-[40%]" />
        <div className="h-3 bg-slate-200 rounded-full animate-pulse max-w-[20%]" />
        <div className="h-3 bg-slate-200 rounded-full animate-pulse max-w-[30%]" />
      </div>
    </div>
  );
};

export default CartCardLoader;
