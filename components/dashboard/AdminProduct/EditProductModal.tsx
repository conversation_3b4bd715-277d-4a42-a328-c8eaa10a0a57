import React, { useState, useEffect, useCallback } from "react";
import Modal from "@/components/Modal";
import InputField from "@/components/general/InputField";
import SelectBox from "@/components/general/SelectBox";
import TextArea from "@/components/general/TextArea";
import Button from "@/components/Button";
import { X, Loader } from "lucide-react";
import { patchData, setHeaderAuthorization } from "@/api";
import { toast } from "react-toastify";
import { constructErrorMessage } from "@/utils/functions";
import useProduct from "@/hooks/useProduct";
import { useForm } from "react-hook-form";
import { getSavedToken } from "@/localservices";
import { ProductDetailsType } from "@/components/product/ProductCard";

interface EditProductModalProps {
  product: ProductDetailsType;
  isOpen: boolean;
  onClose: () => void;
  onProductUpdated?: () => void;
}

type EditProductFormData = {
  name: string;
  quantity: number;
  description: string;
  price: number;
  categoryId: string;
  sku: string;
  gender: GenderType;
};

const EditProductModal: React.FC<EditProductModalProps> = ({
  product,
  isOpen,
  onClose,
  onProductUpdated
}) => {
  const [error, setError] = useState<string | null>(null);
  const { categories, fetchCategories } = useProduct();

  // Initialize form with product data
  const {
    register,
    handleSubmit,
    reset,
    formState: { isSubmitting, errors }
  } = useForm<EditProductFormData>({
    defaultValues: {
      name: product?.name,
      quantity: product?.quantity,
      description: product?.description,
      price: product.price?.amount,
      sku: product?.sku || "",
      gender: product?.gender,
      categoryId: "" // Will be set after categories are loaded
    },
    mode: "onChange"
  });

  // Fetch categories when component mounts
  useEffect(() => {
    if (!categories) {
      fetchCategories();
    }
  }, [fetchCategories, categories]);

  // Reset form when product changes
  useEffect(() => {
    if (product) {
      const categoryObj = (categories || []).find(
        (cat) => cat.name === product?.category
      );
      reset({
        name: product.name,
        quantity: product.quantity,
        description: product.description,
        price: product.price.amount,
        gender: product?.gender,
        sku: product?.sku || "",
        categoryId: categoryObj?.id || "" // Will be set after categories are loaded
      });
    }
  }, [product, reset, isOpen, categories]);
  const updateProduct = useCallback(
    async (formData: EditProductFormData) => {
      setError(null);

      try {
        // Ensure authorization header is set
        const token = getSavedToken();
        if (token) {
          setHeaderAuthorization(token);
        }
        await patchData<
          typeof formData,
          ApiCallResponseType<ProductDetailsType>
        >(`/product/${product.id}`, formData);

        toast.success("Product updated successfully!");

        // Call the callback to refresh the product list
        if (onProductUpdated) {
          onProductUpdated();
        }

        // Close the modal
        onClose();
      } catch (err) {
        const errorMessage = constructErrorMessage(
          err as ApiErrorResponseType,
          "Failed to update product. Please try again."
        );
        setError(errorMessage);
        toast.error(errorMessage);
        console.error("[Update Product Error]:", err);
      }
    },
    [product.id, onClose, onProductUpdated]
  );
  return (
    <Modal
      opened={isOpen}
      onClose={onClose}
      position="center"
      contentClassName="bg-white shadow-xl w-full max-w-2xl p-0 rounded-lg"
    >
      <div className="flex flex-col">
        {/* Header */}
        <div className="flex justify-between items-center p-4 border-b">
          <h2 className="text-xl font-semibold">Edit Product</h2>
          <button
            onClick={onClose}
            className="p-1 rounded-full hover:bg-gray-100 transition-colors"
            aria-label="Close"
          >
            <X className="h-5 w-5" />
          </button>
        </div>

        {/* Content */}
        <div className="p-4">
          <form onSubmit={handleSubmit(updateProduct)}>
            {error && (
              <div className="mb-4 p-3 bg-red-50 text-red-600 text-sm rounded-md">
                {error}
              </div>
            )}

            <div className="space-y-4">
              {/* Row 1: Product Name & Price */}
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <InputField
                  label="Product Name"
                  id="name"
                  placeholder="Enter product name"
                  required
                  error={errors?.name?.message}
                  {...register("name", {
                    required: "Product name is required"
                  })}
                />

                <InputField
                  label="Price (AED)"
                  id="price"
                  type="number"
                  placeholder="0.00"
                  min="0"
                  step="0.01"
                  required
                  error={errors?.price?.message}
                  {...register("price", {
                    required: "Product price is required",
                    min: {
                      value: 1,
                      message: "Minimum product price is AED 1"
                    },
                    valueAsNumber: true
                  })}
                />
              </div>

              {/* Row 2: Quantity & Category */}
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <InputField
                  label="Quantity"
                  id="quantity"
                  type="number"
                  placeholder="0"
                  min="0"
                  step="1"
                  required
                  error={errors?.quantity?.message}
                  {...register("quantity", {
                    required: "Product quantity is required",
                    min: {
                      value: 0,
                      message: "Quantity cannot be negative"
                    },
                    valueAsNumber: true
                  })}
                />

                <SelectBox
                  label="Category"
                  id="categoryId"
                  options={
                    categories?.map((cat) => ({
                      value: cat.id,
                      label: cat.name
                    })) || []
                  }
                  emptyOptionLabel="Select a category"
                  required
                  error={errors?.categoryId?.message}
                  {...register("categoryId", {
                    required: "Please select a category"
                  })}
                />
              </div>

              {/* Row 3: Description (Full Width) */}
              <TextArea
                label="Description"
                id="description"
                placeholder="Enter product description"
                required
                rows={5}
                error={errors?.description?.message}
                {...register("description", {
                  required: "Product description is required"
                })}
              />
            </div>

            <div className="mt-6 flex justify-end space-x-3">
              <Button
                variant="default"
                type="button"
                onClick={onClose}
                disabled={isSubmitting}
              >
                Cancel
              </Button>
              <Button
                variant="primary"
                type="submit"
                disabled={isSubmitting}
                className="flex items-center gap-2"
              >
                {isSubmitting ? (
                  <>
                    <Loader className="h-4 w-4 animate-spin" />
                    <span>Updating...</span>
                  </>
                ) : (
                  <span>Update Product</span>
                )}
              </Button>
            </div>
          </form>
        </div>
      </div>
    </Modal>
  );
};

export default EditProductModal;
