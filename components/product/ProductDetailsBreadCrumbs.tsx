import React from 'react';
import SectionContainer from '../layouts/SectionContainer';
import { ProductDetailsType } from './ProductCard';
import { ArrowLeft } from 'lucide-react';
import Link from 'next/link';
import { useRouter } from 'next/router';

const ProductDetailsBreadCrumbs: React.FC<ProductDetailsType> = ({
	category,
	name,
}) => {
	const { back } = useRouter();
	return (
		<SectionContainer contentContainerClassName='flex items-center gap-4 mt-4'>
			<div className='flex items-center gap-2 py-4'>
				<button
					onClick={back}
					title='go back'
					className=' text-slate-500 text-sm inline-flex items-center gap-2'>
					<span className='inline-flex border border-slate-200 px-2 py-1'>
						<ArrowLeft size={15} />
					</span>
					<span className='inline-block'>Go back</span>
				</button>
			</div>
			<div className='flex items-center gap-1'>
				<Link
					href={`/products/${category}`}
					className='hover:text-secondary hover:font-semibold'>
					{category}
				</Link>{' '}
				<span className='inline-block'>/</span>
				<span className='opacity-60'>{name}</span>
			</div>
		</SectionContainer>
	);
};

export default ProductDetailsBreadCrumbs;
