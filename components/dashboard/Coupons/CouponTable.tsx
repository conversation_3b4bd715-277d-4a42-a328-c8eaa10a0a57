import React, { useCallback, useEffect, useState } from "react";

import CouponTableRow from "./CouponTableRow";
import { ChevronLeft, ChevronRight, Loader2 } from "lucide-react";
import ErrorContainer from "@/components/status/ErrorContainer";
import CouponTableHeader from "./CouponTableHeader";
import { CouponDetailsType } from "@/store/useOrderStore";
import { useQuery } from "@tanstack/react-query";
import { getData } from "@/api";
import { constructErrorMessage } from "@/utils/functions";
import { useRouter } from "next/router";
import { useParams } from "next/navigation";
import Button from "@/components/Button";

const CouponTable: React.FC<{ isCreateModalOpen: boolean }> = ({
  isCreateModalOpen
}) => {
  const [totalPage, setTotalPage] = useState(0);
  const numberOfColumns = 7;
  const { query, pathname, push } = useRouter();
  const params = useParams();
  const { status } = params || {};
  const { page: queryPage = "1" } = query || {};
  const page = parseInt(queryPage?.toString() || "1");
  const getCoupons = useCallback(
    async (page: number) => {
      const urlSearchParams = new URLSearchParams();
      urlSearchParams.set("page", page?.toString());
      if (status && status !== "all") {
        urlSearchParams.set("status", status?.toString());
      }
      return await getData<ApiCallResponseType<CouponDetailsType[]>>(
        `/coupons?${urlSearchParams?.toString()}`
      );
    },
    [status]
  );
  const [coupons, setCoupons] = useState<CouponDetailsType[] | null>(null);
  const { error, data, refetch, isLoading } = useQuery({
    queryKey: [`coupon-list`, params, page],
    queryFn: () => getCoupons(page)
  });

  const handlePageChange = useCallback(
    (page: number) => {
      push({ pathname, query: { ...query, page } });
    },
    [query, push, pathname]
  );

  useEffect(() => {
    if (data) {
      setCoupons(data?.data?.data);
      setTotalPage(data?.data?.pagination?.totalPage);
    }
  }, [data]);

  useEffect(() => {
    refetch();
  }, [isCreateModalOpen, refetch]);

  return (
    <div className="overflow-x-auto">
      <div className="align-middle inline-block min-w-full">
        <div className="shadow border-b border-gray-200 sm:rounded-lg">
          <table className="min-w-full divide-y divide-gray-200">
            <CouponTableHeader />
            <tbody className="bg-white divide-y divide-gray-200">
              {!coupons && !error && (
                <tr>
                  <td
                    colSpan={numberOfColumns}
                    className="px-4 py-10 text-center text-sm text-gray-500"
                  >
                    <div className="flex justify-center items-center">
                      <Loader2 className="h-6 w-6 animate-spin mr-2" />
                      Loading coupons...
                    </div>
                  </td>
                </tr>
              )}
              {error && !coupons && (
                <tr>
                  <td
                    colSpan={numberOfColumns}
                    className="px-4 py-10 text-center text-sm"
                  >
                    <ErrorContainer
                      error={constructErrorMessage(
                        error as ApiErrorResponseType,
                        "Error loading coupons! Please try again"
                      )}
                      retryFunction={refetch}
                    />
                  </td>
                </tr>
              )}
              {coupons &&
                coupons.length > 0 &&
                coupons.map((coupon) => (
                  <CouponTableRow
                    key={coupon.id}
                    coupon={coupon}
                    refetch={refetch}
                  />
                ))}
              {coupons && coupons.length < 1 && (
                <tr>
                  <td
                    colSpan={numberOfColumns}
                    className="px-4 py-10 text-center text-sm text-gray-500"
                  >
                    No coupons found.
                  </td>
                </tr>
              )}
            </tbody>
          </table>
        </div>
        {!error && (
          <div className="flex justify-center items-center mt-4 space-x-2">
            <Button
              variant="primary"
              size="small"
              loading={isLoading}
              onClick={() => {
                handlePageChange(page - 1);
              }}
              disabled={page <= 1}
            >
              <ChevronLeft className="h-4 w-4" />
            </Button>
            <span className="text-sm text-gray-700">
              Page {page} of {totalPage}
            </span>
            <Button
              variant="primary"
              size="small"
              loading={isLoading}
              onClick={() => {
                handlePageChange(page + 1);
              }}
              disabled={page >= totalPage}
            >
              <ChevronRight className="h-4 w-4" />
            </Button>
          </div>
        )}
      </div>
    </div>
  );
};

export default CouponTable;
