import { shortSiteName } from "@/utils/variables";
import { Html, Head, Main, NextScript } from "next/document";

export default function Document() {
  return (
    <Html lang="en">
      <Head>
        <meta name="robots" content="follow, index, noarchive" />
        <meta name="Googlebot" content="follow, index, noarchive" />
        <meta name="application-name" content={`${shortSiteName}`} />
        <meta name="apple-mobile-web-app-capable" content="yes" />
        <meta name="apple-mobile-web-app-status-bar-style" content="default" />
        <meta name="apple-mobile-web-app-title" content={`${shortSiteName}`} />
        <meta name="format-detection" content="telephone=yes" />
        <meta name="mobile-web-app-capable" content="yes" />
        <meta name="msapplication-TileColor" content="#ffffff" />
        <meta name="msapplication-tap-highlight" content="no" />
        <meta name="theme-color" content="#0A434F" />
        <link rel="robots" href="/robots.txt" />
        <link rel="sitemap" type="application/xml" href="/sitemap.xml" />
        <meta
          name="keywords"
          content="Banafa, banafa, banafa website, best perfumes, long-lasting perfumes, perfumes for women, perfumes for men, luxury perfumes, affordable perfumes, floral perfumes, sweet perfumes, musky perfumes, 
         unisex perfumes, branded perfumes, top perfumes 2025, best perfume for date night, signature scents, trending fragrances"
        />

        <link rel="manifest" href="/site.webmanifest" />
        <link rel="shortcut icon" href="/favicon-16x16.png" type="image/png" />
        <link rel="shortcut icon" href="/favicon-32x32.png" type="image/png" />
        <link
          rel="icon"
          href="/favicon-16x16.png"
          type="image/png"
          sizes="16x16"
        />
        <link
          rel="icon"
          href="/favicon-32x32.png"
          type="image/png"
          sizes="32x32"
        />
        {/* <script
          type="application/ld+json"
          dangerouslySetInnerHTML={{ __html: JSON.stringify(schema) }}
        /> */}

        {/* <meta
            name="google-site-verification"
            content={process.env.NEXT_PUBLIC_GOOGLE_INDEX_VERIFICATION_ID}
          /> */}
      </Head>
      <body className="antialiased font-ttchocolates bg-primary/5">
        <Main />
        <NextScript />
      </body>
    </Html>
  );
}
