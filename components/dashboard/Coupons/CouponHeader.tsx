import React from 'react';
import Button from '@/components/Button';
import { Plus } from 'lucide-react';

interface CouponHeaderProps {
  onCreateCoupon: () => void;
}

const CouponHeader: React.FC<CouponHeaderProps> = ({ onCreateCoupon }) => {
  return (
    <div className='flex flex-col md:flex-row md:items-center md:justify-between mb-6'>
      <h1 className='text-2xl font-semibold text-gray-900 mb-4 md:mb-0'>
        Coupons
      </h1>
      <div className='flex flex-wrap items-center gap-2'>
        <Button
          variant='primary'
          size='small'
          className='flex items-center gap-2'
          onClick={onCreateCoupon}>
          <Plus className='h-4 w-4' />
          <span>Create Coupon</span>
        </Button>
      </div>
    </div>
  );
};

export default CouponHeader;
