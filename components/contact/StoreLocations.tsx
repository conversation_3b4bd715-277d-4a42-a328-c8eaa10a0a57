import React from 'react';
import SectionContainer from '../layouts/SectionContainer';

const StoreLocations = () => {
	const locations = [
		{
			city: 'Dubai - UAE',
			company: 'Buabed Banafa Perfumes Industry FZCO',
			poBox: 'PO Box: 61266',
			address: 'Jebel Ali Free Zone',
			country: 'Dubai, United Arab Emirates',
			tel: '+971 4 8819580',
			fax: '+971 4 8819584',
			whatsapp: '+971 55 387 6053',
			email: '<EMAIL>',
		},
		{
			city: 'Jeddah - Saudi Arabia',
			company: '<PERSON> Hu<PERSON>n Buabed Banafa Factory',
			poBox: 'P.O.Box: 16787, Jeddah 21474',
			tel: '+966 12 62 72 600, +966 12 62 74 447',
			fax: '+966 12 62 74 600',
			mobile: '00966555657111',
			whatsapp: '00971503214955',
			email: '<EMAIL>',
		},
	];

	return (
		<SectionContainer
			contentContainerClassName='max-w-7xl mx-auto py-28 px-4 py-12'
			className='bg-gray-50'>
			<div className='grid grid-cols-[1fr_2fr] gap-12 items-start'>
				{/* Left Section */}
				<div className='max-w-sm'>
					<p className='text-sm font-semibold text-secondary-400'>
						Our locations
					</p>
					<h3 className='font-bold text-3xl mt-1'>Visit our stores</h3>
					<p className='text-gray-600 mt-2'>Find us at these locations.</p>
				</div>

				{/* Right Section - Locations */}
				<div className='grid md:grid-cols-2 gap-6'>
					{locations.map((location, index) => (
						<div
							key={index}
							className='flex flex-col gap-1'>
							<h4 className='text-lg font-semibold mb-1'>{location.city}</h4>
							<p className='text-gray-700'>{location.company}</p>
							<p className='text-gray-700'>{location.poBox}</p>
							{location.address && (
								<p className='text-gray-700'>{location.address}</p>
							)}
							<p className='text-gray-700'>{location.country}</p>

							<div className='mt-3 space-y-1'>
								{location.tel && (
									<p className='text-gray-700'>
										<span className='font-medium'>Tel: </span>
										{location.tel}
									</p>
								)}
								{location.fax && (
									<p className='text-gray-700'>
										<span className='font-medium'>Fax: </span>
										{location.fax}
									</p>
								)}
								{location.mobile && (
									<p className='text-gray-700'>
										<span className='font-medium'>Mobile: </span>
										{location.mobile}
									</p>
								)}
								{location.whatsapp && (
									<p className='text-gray-700'>
										<span className='font-medium'>Whatsapp: </span>
										{location.whatsapp}
									</p>
								)}
								<p className='text-gray-700'>
									<span className='font-medium'>Email: </span>
									<a
										href={`mailto:${location.email}`}
										className='hover:underline'>
										{location.email}
									</a>
								</p>
							</div>
						</div>
					))}
				</div>
			</div>
		</SectionContainer>
	);
};

export default StoreLocations;
