import { format } from "date-fns";

export const convertObjectToArray = <T extends Record<string, any>>(
  data: T
): Array<T[keyof T]> => {
  return Object.values(data);
};

export const constructErrorMessage = (
  error: ApiErrorResponseType,
  defaultMessage: string
) => {
  return error?.response?.data?.message ?? error?.message ?? defaultMessage;
};

export const formatText = (text: string) => {
  const newText = text?.split("-")?.join(" ")?.split("_")?.join(" ");
  return `${newText?.slice(0, 1)?.toUpperCase()}${newText?.slice(1)}`;
};

export const constructPriceResponse = (amount: number): AmountType => {
  if (isNaN(amount)) {
    amount = 0;
  }

  const oneDubaiFil = 100;
  const countryCode = "ar-AE";
  const currencyCode = "AED";

  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat(countryCode, {
      style: "currency",
      currency: currencyCode
    }).format(amount);
  };
  const formatNumber = (amount: number) => {
    return new Intl.NumberFormat(countryCode).format(amount);
  };
  const fils = Number(String(amount).split(".")[1]) || 0;
  return {
    amount: amount,
    whole: amount * oneDubaiFil,
    currency: {
      symbol: "AED",
      name: "United Arab Emirates Dirham"
    },
    fils,
    formatted: {
      withCurrency: formatCurrency(amount),
      withoutCurrency: formatNumber(amount)
    }
  };
};

export const formatDate = (date: Date) => {
  const formattedDate = format(date, "do MMMM, yyyy");
  return formattedDate;
};

export const changeFirstLetterToCapitalLetter = (text: string) => {
  return `${text.slice(0, 1)?.toUpperCase()}${text.slice(1)}`;
};
