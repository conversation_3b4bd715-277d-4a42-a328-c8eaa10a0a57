import InputField from "@/components/general/InputField";
import { Search } from "lucide-react";
import { usePathname } from "next/navigation";
import { useRouter } from "next/router";
import React, { useEffect, useState } from "react";
import { useDebouncedCallback } from "use-debounce";

const AdminSearchBar = () => {
  const { query, push } = useRouter();
  const pathname = usePathname();
  const [searchText, setSearchText] = useState("");

  const processSearch = useDebouncedCallback((search = "") => {
    const { q: _, ...otherQueries } = query || {};
    if (search) {
      push({ pathname, query: { ...otherQueries, q: search, page: "1" } });
      return;
    }
    push({ pathname, query: { ...otherQueries, page: "1" } });
  }, 1000);

  useEffect(() => {
    const searchTextFromQuery = query?.q?.toString() || "";
    setSearchText(searchTextFromQuery);
  }, [query]);

  return (
    <div className="flex items-center w-full md:w-auto">
      {/* Search Input */}
      <div className="relative flex-grow max-w-md">
        <div className="absolute inset-y-0 right-6 z-10 flex items-center pointer-events-none cursor-pointer">
          <Search className="h-5 w-5 text-gray-400" />
        </div>
        <InputField
          type="text"
          placeholder="Search products..."
          value={searchText}
          onChange={(e) => {
            const value = (e?.target as HTMLInputElement)?.value;
            setSearchText(value);
            processSearch(value);
          }}
          className="block w-full pl-10 pr-3 py-2 rounded-lg 
                    focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 
                    transition-colors duration-200 ease-in-out"
        />
      </div>
    </div>
  );
};

export default AdminSearchBar;
