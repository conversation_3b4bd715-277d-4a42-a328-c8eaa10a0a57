import { postData } from '@/api';
import AuthHeader from '@/components/auth/AuthHeader';
import Button from '@/components/Button';
import InputField from '@/components/general/InputField';
import { constructErrorMessage } from '@/utils/functions';
import { emailRegExp } from '@/utils/regex';
import { verificationTypes } from '@/utils/variables';
import { useRouter } from 'next/router';
import React, { useCallback } from 'react';
import { useForm } from 'react-hook-form';
import { toast } from 'react-toastify';

export type ForgotPasswordBodyType = {
	email: string;
};

export type ForgotPasswordResponseType = {
	data: {
		message: string;
		token: string;
	};
};

const defaultValues: ForgotPasswordBodyType = {
	email: '',
};

const ForgetPassword = () => {
	const { push } = useRouter();
	const {
		register,
		formState: { errors, isSubmitting, isValid },
		setError,
		handleSubmit,
	} = useForm({
		defaultValues,
		mode: 'onChange',
	});
	const processForgotPassword = useCallback(
		async (body: ForgotPasswordBodyType) => {
			try {
				const response = await postData<
					ForgotPasswordBodyType,
					ForgotPasswordResponseType
				>('/auth/forgot-password', body);
				const { data } = response?.data;
				push(
					`/auth/verify-otp?token=${data?.token}&email=${body?.email}&type=${verificationTypes?.forgotPassword}`,
				);
				toast(data?.message);
			} catch (error: unknown) {
				const apiError = error as ApiErrorResponseType;
				if (apiError?.response?.data?.errors) {
					const errorKeys = Object.keys(apiError.response.data.errors || {});
					errorKeys.forEach((key) => {
						setError(key as keyof ForgotPasswordBodyType, {
							message:
								apiError.response?.data?.errors?.[key] || 'Error occurred',
						});
					});
				}
				toast(
					constructErrorMessage(
						apiError,
						'Unable to process request! Please try again later',
					),
				);
			}
		},
		[push, setError],
	);
	return (
		<div className='flex flex-col items-center pt-28 min-h-screen p-4'>
			<div className='w-full max-w-md space-y-8'>
				<AuthHeader title='Forgot Password' />
				<p className='text-center text-gray-600 text-lg'>
					Enter your email to reset your password
				</p>
				<form
					onSubmit={handleSubmit(processForgotPassword)}
					className='flex flex-col gap-4'>
					<InputField
						id='email'
						type='email'
						label='Email*'
						placeholder='Enter your email'
						error={errors?.email?.message}
						{...register('email', {
							required: 'Email is required',
							pattern: {
								value: emailRegExp,
								message: 'Invalid email address',
							},
						})}
					/>

					<Button
						loading={isSubmitting}
						disabled={!isValid}
						variant='secondary'
						className=' w-full'>
						Send
					</Button>
				</form>
			</div>
		</div>
	);
};

export default ForgetPassword;
