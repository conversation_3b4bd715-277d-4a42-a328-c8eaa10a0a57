import React from "react";
import { useRouter } from "next/router";
import { ArrowLeft, Mail } from "lucide-react";
import { useQuery } from "@tanstack/react-query";
import { getData } from "@/api";
import { constructErrorMessage, formatDate } from "@/utils/functions";
// import CustomImage from "@/components/general/CustomImage";
import Button from "@/components/Button";
import ErrorContainer from "@/components/status/ErrorContainer";
import { UserDetailsType } from "@/store/useUserStore";

interface UserDetailsProps {
  userId?: string;
}

const UserDetails: React.FC<UserDetailsProps> = ({ userId }) => {
  const router = useRouter();

  const {
    data: userDetails,
    error,
    refetch
  } = useQuery({
    queryKey: ["user-details", userId],
    queryFn: async () => {
      if (!userId) return null;
      const { data } = await getData<ApiCallResponseType<UserDetailsType>>(
        `/user/${userId}`
      );
      return data.data;
    },
    enabled: !!userId
  });

  const handleGoBack = () => {
    router.back();
  };

  if (error) {
    return (
      <div className="p-4">
        <Button onClick={handleGoBack}>
          <ArrowLeft className="h-4 w-4 mr-2" />
          Back
        </Button>
        <div className="mt-4">
          <ErrorContainer
            error={constructErrorMessage(
              error as ApiErrorResponseType,
              "Failed to load user details"
            )}
            retryFunction={refetch}
          />
        </div>
      </div>
    );
  }

  if (!userDetails) {
    return (
      <div className="p-4 flex justify-center items-center h-64">
        <div className="flex items-center">
          <p>Loading user details...</p>
        </div>
      </div>
    );
  }

  return (
    <div className="bg-white shadow-sm rounded-lg overflow-hidden">
      {/* Header with back button */}
      <div className="p-6 border-b border-gray-200">
        <div className="flex items-center justify-between">
          <button
            onClick={handleGoBack}
            className="flex items-center text-gray-600 hover:text-gray-900"
          >
            <ArrowLeft className="h-5 w-5 mr-2" />
            Back to Customers
          </button>
        </div>
      </div>

      {/* Customer Profile */}
      <div className="p-6 border-b border-gray-200">
        <div className="flex flex-col md:flex-row items-start md:items-center">
          <div className="h-20 w-20 flex-shrink-0 mr-6 mb-4 md:mb-0 bg-gray-200 rounded-full flex items-center justify-center">
            <span className="text-gray-600 text-2xl">
              {userDetails?.firstName.charAt(0)}
            </span>
          </div>
          <div>
            <h2 className="text-2xl font-bold text-gray-900">
              {`${userDetails?.firstName} ${userDetails?.lastName}`}
            </h2>
            <p className="text-gray-500">{userDetails.id}</p>
            <div className="mt-2">
              <span
                className={`px-2 py-1 inline-flex text-xs leading-5 font-semibold rounded-full ${
                  userDetails?.isEmailVerified
                    ? "bg-green-100 text-green-800"
                    : "bg-red-100 text-red-800"
                }`}
              >
                {userDetails?.isEmailVerified ? "Verified" : "Unverified"}
              </span>
            </div>
          </div>
        </div>
      </div>

      {/* Customer Information */}
      <div className="p-6 border-b border-gray-200">
        <h3 className="text-lg font-medium text-gray-900 mb-4">
          Customer Information
        </h3>
        <div className="grid grid-cols-1 gap-6">
          <div className="flex items-start">
            <Mail className="h-5 w-5 text-gray-400 mt-0.5 mr-3" />
            <div>
              <p className="text-sm text-gray-500">Email</p>
              <p className="font-medium">{userDetails?.email}</p>
            </div>
          </div>
          <div>
            <p className="text-sm text-gray-500">Role</p>
            <p className="font-medium">{userDetails?.role}</p>
          </div>
          <div>
            <p className="text-sm text-gray-500">Join Date</p>
            <p className="font-medium">{formatDate(userDetails?.createdAt)}</p>
          </div>
        </div>
      </div>
    </div>
  );
};

export default UserDetails;
