import React, { useState, useRef, useEffect } from "react";
import { MoreH<PERSON>zon<PERSON>, Eye } from "lucide-react";
import CustomImage from "@/components/general/CustomImage";
import Button from "@/components/Button";
import { UserDetailsType } from "@/store/useUserStore";
import { useRouter } from "next/router";
import { formatDate } from "@/utils/functions";

interface CustomerTableRowProps {
  user: UserDetailsType;
}

const CustomerTableRow: React.FC<CustomerTableRowProps> = ({ user }) => {
  const { push } = useRouter();
  const [showDropdown, setShowDropdown] = useState(false);
  const dropdownRef = useRef<HTMLDivElement>(null);

  // Close dropdown when clicking outside
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (
        dropdownRef.current &&
        !dropdownRef.current.contains(event.target as Node)
      ) {
        setShowDropdown(false);
      }
    };

    document.addEventListener("mousedown", handleClickOutside);
    return () => {
      document.removeEventListener("mousedown", handleClickOutside);
    };
  }, []);

  return (
    <tr className="hover:bg-gray-50">
      {/* <td className='px-4 py-4 whitespace-nowrap'>
				<CheckBox
					checked={isSelected}
					onChange={(e: React.ChangeEvent<HTMLInputElement>) =>
						onSelect(customer.id, e.target.checked)
					}
					className='h-4 w-4'
				/>
			</td> */}
      <td className="px-4 py-4 whitespace-nowrap">
        <div className="flex items-center gap-3">
          {user?.avatar && (
            <div className="h-8 w-8 flex-shrink-0 mr-3">
              <CustomImage
                src={user?.avatar}
                alt={user?.name}
                className="h-8 w-8 rounded-full object-cover"
              />
            </div>
          )}
          {!user?.avatar && (
            <div className="h-8 w-8 flex-shrink-0 bg-gray-200 rounded-full flex items-center justify-center">
              <span className="text-gray-600 text-sm">
                {user?.name.charAt(0)}
              </span>
            </div>
          )}
          <div className="font-medium text-gray-900">{user?.name}</div>
          {user?.isNew && (
            <span className="bg-cyan-100 text-teal-700 px-2 py-1 text-[0.5rem] font-semibold rounded-full uppercase">
              New User
            </span>
          )}
        </div>
      </td>
      {/* <td className='px-4 py-4 whitespace-nowrap text-sm text-gray-500'>
				{customer.customerId}
			</td> */}
      <td className="px-4 py-4 whitespace-nowrap text-sm text-gray-500">
        {formatDate(user?.createdAt)}
      </td>
      <td className="px-4 py-4 whitespace-nowrap text-sm text-gray-500">
        {user?.email}
      </td>
      <td className="px-4 py-4 whitespace-nowrap text-sm text-gray-500">
        {user?.role}
      </td>
      {/* <td className='px-4 py-4 whitespace-nowrap text-sm text-gray-500'>
				{customer.totalOrders}
			</td>
			<td className='px-4 py-4 whitespace-nowrap text-sm text-gray-500'>
				AED{customer.totalSpent.toFixed(2)}
			</td> */}
      {/* <td className='px-4 py-4 whitespace-nowrap'>
				<span
					className={`px-2 inline-flex text-xs leading-5 font-semibold rounded-full ${
						customer.status === 'Active'
							? 'bg-green-100 text-green-800'
							: 'bg-red-100 text-red-800'
					}`}>
					{customer.status}
				</span>
			</td> */}
      <td className="px-4 py-4 whitespace-nowrap text-right text-sm font-medium">
        <div className="relative" ref={dropdownRef}>
          <Button
            onClick={() => setShowDropdown(!showDropdown)}
            className="text-gray-400 hover:text-gray-500 bg-transparent"
          >
            <MoreHorizontal className="h-5 w-5" />
          </Button>
          {showDropdown && (
            <div className="origin-top-right absolute right-0 mt-2 w-48 rounded-md shadow-lg bg-white ring-1 ring-black ring-opacity-5 z-50">
              <div
                className="py-1"
                role="menu"
                aria-orientation="vertical"
                aria-labelledby="options-menu"
              >
                <button
                  onClick={() => {
                    push(`/account/customers/details/${user?.id}`);
                    setShowDropdown(false);
                  }}
                  className="flex items-center px-4 py-2 text-sm text-gray-700 hover:bg-gray-100 w-full text-left z-50"
                  role="menuitem"
                >
                  <Eye className="mr-3 h-4 w-4 text-gray-400" />
                  View Details
                </button>
                {/* <button
									className='flex items-center px-4 py-2 text-sm text-gray-700 hover:bg-gray-100 w-full text-left'
									role='menuitem'>
									<Edit className='mr-3 h-4 w-4 text-gray-400' />
									Edit
								</button>
								<button
									className='flex items-center px-4 py-2 text-sm text-red-600 hover:bg-gray-100 w-full text-left'
									role='menuitem'>
									<Trash className='mr-3 h-4 w-4 text-red-400' />
									Delete
								</button> */}
              </div>
            </div>
          )}
        </div>
      </td>
    </tr>
  );
};

export default CustomerTableRow;
