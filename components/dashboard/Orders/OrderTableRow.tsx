import React, { useState, useRef, useEffect, useCallback } from "react";
import StatusBadge from "./StatusBadge";
import { MoreHorizontal, Eye, XCircle } from "lucide-react";
import CheckBox from "@/components/general/CheckBox";
import Button from "@/components/Button";
import { OrderDetailsType } from "@/store/useOrderStore";
import { constructErrorMessage, formatDate } from "@/utils/functions";
import { useRouter } from "next/router";
import { deleteData } from "@/api";
import { toast } from "react-toastify";

interface OrderTableRowProps {
  order: OrderDetailsType;
  isSelected: boolean;
  onSelect: (id: string, checked: boolean) => void;
  refetch: () => void;
}

const OrderTableRow: React.FC<OrderTableRowProps> = ({
  order,
  isSelected,
  onSelect,
  refetch = () => {}
}) => {
  const [cancelingOrder, setCancelingOrder] = useState(false);
  const [isDropdownOpen, setIsDropdownOpen] = useState(false);
  const dropdownRef = useRef<HTMLDivElement>(null);
  const { push } = useRouter();

  const cancelOrder = useCallback(async () => {
    setCancelingOrder(true);
    try {
      await deleteData(`/order/${order?.id}`);
      refetch();
    } catch (error) {
      toast(
        constructErrorMessage(
          error as ApiErrorResponseType,
          "Error canceling order! Please try again"
        )
      );
    } finally {
      setCancelingOrder(false);
    }
  }, [refetch, order]);

  // Close dropdown when clicking outside
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (
        dropdownRef.current &&
        !dropdownRef.current.contains(event.target as Node)
      ) {
        setIsDropdownOpen(false);
      }
    };

    document.addEventListener("mousedown", handleClickOutside);
    return () => {
      document.removeEventListener("mousedown", handleClickOutside);
    };
  }, []);

  return (
    <tr
      className={`hover:bg-gray-50 ${isSelected ? "bg-gray-50" : "bg-white"}`}
    >
      {/* Checkbox */}
      <td className="pl-4 pr-2 py-3 whitespace-nowrap w-12">
        {/* Adjusted padding */}
        <CheckBox
          checked={isSelected}
          onChange={(e) =>
            onSelect(order?.id, (e.target as HTMLInputElement).checked)
          }
          aria-labelledby={`order-${order?.id}`}
        />
      </td>

      {/* Order ID */}
      <td
        id={`order-${order?.id}`}
        className="px-3 py-3 whitespace-nowrap text-sm font-medium text-gray-900"
      >
        <div className="flex items-center gap-3">
          <span>
            {order?.orderCode}
            {order?.isNew && (
              <span className="bg-secondary-900 text-secondary-600 px-2 py-1 text-[0.5rem] font-semibold rounded-full uppercase">
                New
              </span>
            )}
          </span>
        </div>
      </td>

      {/* Date */}
      <td className="px-3 py-3 whitespace-nowrap text-sm text-gray-500">
        {formatDate(order?.createdAt)}
      </td>

      {/* Customer */}
      <td className="px-3 py-3 whitespace-nowrap text-sm text-gray-500">
        {order?.user?.email}
      </td>

      {/* Total */}
      <td className="px-3 py-3 whitespace-nowrap text-sm text-gray-500">
        {order?.total?.formatted?.withCurrency}
      </td>

      {/* Payment Status */}
      <td className="px-3 py-3 whitespace-nowrap text-sm text-gray-500">
        <StatusBadge status={order?.paidAt ? "PAID" : "PENDING"} />
      </td>

      {/* Items */}
      <td className="px-3 py-3 whitespace-nowrap text-sm text-gray-500">
        {order?.totalCarts} item{order?.totalCarts !== 1 ? "s" : ""}
      </td>

      {/* Order Status */}
      <td className="px-3 py-3 whitespace-nowrap text-sm text-gray-500">
        <StatusBadge status={order?.status} />
      </td>

      {/* Actions */}
      <td className="pl-3 pr-4 py-3 whitespace-nowrap text-right text-sm font-medium w-12 relative">
        <div ref={dropdownRef} className="relative">
          <button
            className="text-gray-400 hover:text-gray-600 rounded-full p-1 hover:bg-gray-100 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
            onClick={() => setIsDropdownOpen(!isDropdownOpen)}
          >
            <span className="sr-only">Actions for {order?.orderCode}</span>
            <MoreHorizontal className="h-5 w-5" />
          </button>

          {/* Dropdown Menu */}
          {isDropdownOpen && (
            <div className="absolute right-0 mt-2 w-56 rounded-2xl shadow-xl bg-white ring-1 ring-gray-200 z-20">
              <div className="">
                <Button
                  className="flex items-center mb-3 rounded-2xl rounded-b-none hover:bg-gray-100 bg-transparent gap-3 w-full px-5 py-3 text-sm text-gray-800 hover:text-black transition-colors duration-200"
                  role="menuitem"
                  onClick={() => {
                    push(`/account/orders/details/${order?.id}`);
                  }}
                >
                  <Eye className="h-5 w-5 text-gray-500" />
                  View Order Details
                </Button>
                <Button
                  className="flex rounded-2xl  items-center rounded-t-none gap-3 hover:bg-red-50 bg-transparent w-full px-5 py-3 text-sm text-red-600 hover:text-red-700 transition-colors duration-200"
                  role="menuitem"
                  loading={cancelingOrder}
                  onClick={cancelOrder}
                >
                  <XCircle className="h-5 w-5 text-red-500" />
                  Cancel Order
                </Button>
              </div>
            </div>
          )}
        </div>
      </td>
    </tr>
  );
};

export default OrderTableRow;
