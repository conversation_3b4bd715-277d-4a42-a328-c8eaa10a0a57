"use client";
import React, { useRef, useState } from "react";
import Image from "next/image";
import { Swiper, SwiperRef, SwiperSlide } from "swiper/react";
import { Navigation, Autoplay } from "swiper/modules";
import "swiper/css";
import "swiper/css/navigation";
import "swiper/css/pagination";
import { BsArrowLeftCircle, BsArrowRightCircle } from "react-icons/bs";

import {
  HeroImageOne,
  HeroImageTwo,
  HeroImageThree,
  HeroImageFour
} from "../../assets/images";
import SectionContainer from "../layouts/SectionContainer";
import Button from "../Button";
import { useRouter } from "next/router";

const images = [HeroImageOne, HeroImageTwo, HeroImageThree, HeroImageFour];

const slideButtonClassName =
  "text-white rounded-full size-12 flex items-center justify-center opacity-75 hover:opacity-100 focus:outline-none";

const Hero: React.FC = () => {
  const { push } = useRouter();
  const swiperRef = useRef<SwiperRef | null>(null);
  const [activeIndex, setActiveIndex] = useState(0);

  const nextImage = () => {
    if (swiperRef.current) swiperRef?.current?.swiper?.slideNext();
  };

  const prevImage = () => {
    if (swiperRef.current) swiperRef?.current?.swiper?.slidePrev();
  };

  return (
    <div className="relative w-full h-[clamp(30rem,40vw,40rem)] overflow-hidden">
      <Swiper
        modules={[Navigation, Autoplay]}
        slidesPerView={1}
        onSlideChange={(swiper) => {
          setActiveIndex(swiper?.realIndex || 0);
        }}
        loop={true}
        autoplay={{ delay: 5000, disableOnInteraction: false }}
        ref={swiperRef}
        className="w-full h-full"
      >
        {images.map((image, index) => (
          <SwiperSlide key={index}>
            <div className="relative w-full h-full">
              <Image
                src={image}
                alt={`Hero ${index + 1}`}
                fill
                className="object-cover"
                priority={index === 0}
              />
              <SectionContainer
                className="relative w-full  bg-black/30 h-full text-white"
                contentContainerClassName="flex flex-col gap-4  justify-center items-start h-full"
              >
                <span className="text-xs uppercase text-white bg-secondary rounded-full py-2 px-4">
                  NOW AVAILABLE
                </span>
                <h1 className="uppercase text-[clamp(2rem,5.1vw,5.1rem)] max-w-[40rem]">
                  buabedbanafa holiday set
                </h1>
                <p className="opacity-60 text-[clamp(1.1rem,1.3vw,1.3rem)] font-medium max-w-[33rem]">
                  Shop our limited-edition fragrance gift sets that capture the
                  magnetic and addictive cosmic scent.
                </p>
                <Button
                  onClick={() => {
                    push("/products");
                  }}
                  className="text-xs rounded-sm text-black bg-white !py-2"
                >
                  Shop now
                </Button>
              </SectionContainer>
            </div>
          </SwiperSlide>
        ))}
      </Swiper>

      {/* Navigation Buttons (Positioned Like Original) */}

      <div className="absolute right-10 bottom-10 z-10 flex items-center gap-2">
        <button
          onClick={prevImage}
          className={slideButtonClassName}
          aria-label="Previous Slide"
        >
          <BsArrowLeftCircle className="h-10 w-10" />
        </button>

        {images.map((_, index) => {
          const isActive = index === activeIndex;
          return (
            <span
              key={index}
              className={`${
                isActive ? "bg-secondary" : "bg-slate-400"
              } size-2 rounded-full transition-all`}
            ></span>
          );
        })}

        <button
          onClick={nextImage}
          className={slideButtonClassName}
          aria-label="Next Slide"
        >
          <BsArrowRightCircle className="h-10 w-10" />
        </button>
      </div>
    </div>
  );
};

export default Hero;
