import { getData } from "@/api";
import ErrorContainer from "@/components/status/ErrorContainer";
import { constructErrorMessage } from "@/utils/functions";
import { useQuery } from "@tanstack/react-query";
import React, { useCallback, useEffect, useState } from "react";

interface StatCardProps {
  title: string;
  value?: string;
}

export type ProductStatResponseType = {
  totalProduct: number | null;
  averageRating: number | null;
  totalCategories: number | null;
  totalRevenue: AmountType | null;
};

const StatCard: React.FC<StatCardProps> = ({ title, value }) => (
  <div className="bg-white p-4 rounded-lg border border-gray-200">
    <p className="text-sm text-gray-500 mb-1 truncate" title={title}>
      {title}
    </p>
    {value && (
      <p className="text-2xl font-semibold text-gray-900">
        {value?.toString()}
      </p>
    )}
    {!value && <span className="font-bold">...</span>}
  </div>
);

const AdminProductStats = () => {
  const [stats, setStats] = useState<ProductStatResponseType | null>(null);
  const queryFn = useCallback(async () => {
    return await getData<ApiCallResponseType<ProductStatResponseType>>(
      "/products/stat"
    );
  }, []);
  const { data, refetch, error } = useQuery({
    queryFn,
    queryKey: ["products-stat"]
  });

  useEffect(() => {
    if (data) {
      setStats(data?.data?.data);
    }
  }, [data]);

  return (
    <div className="flex justify-center mb-6">
      {/* Re-added margin bottom */}
      {/* Adjusted grid columns for responsiveness */}
      {stats && (
        <div className="w-full grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-4">
          <StatCard
            title="Total Products"
            value={stats?.totalProduct?.toString()}
          />
          <StatCard
            title="Total Revenue (AED)" // More specific title
            // Format sales as currency, handle potential floating point issues
            value={stats?.totalRevenue?.formatted?.withCurrency || ""}
          />
          <StatCard
            title="Product Categories" // More specific title
            value={stats?.totalCategories?.toString()} // Use updated prop name
          />
          <StatCard
            title="Average Rating"
            // Format rating to one decimal place
            value={stats?.averageRating?.toFixed(1)?.toString()}
          />
        </div>
      )}
      {!stats && error && (
        <ErrorContainer
          error={constructErrorMessage(
            error as ApiErrorResponseType,
            "Unable to get stats"
          )}
          retryFunction={refetch}
        />
      )}
    </div>
  );
};

export default AdminProductStats;
