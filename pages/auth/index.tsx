import React, { useState } from "react";
import AuthHeader from "@/components/auth/AuthHeader";
import TabNavigation from "@/components/auth/TabNavigation";
import SignInForm from "@/components/auth/SignInForm";
import RegisterForm from "@/components/auth/RegisterForm";
import { Transition } from "@headlessui/react";
import isLoggedIn from "@/hooks/isLoggedIn";
import Head from "next/head";
import { siteName } from "@/utils/variables";

const Auth = () => {
  const [showRegister, setShowRegister] = useState<boolean>(false);
  const description = `Forgot your password? No worries. We’ll help you securely reset your ${siteName} account and get you back to shopping in no time.`;

  return (
    <>
      <Head>
        <title>Reset password | {siteName}</title>
        <meta name="description" content={description} />
        <meta property="og:title" content="Reset password" />
        <meta property="og:description" content={description} />
        <meta name="twitter:description" content={description} />
      </Head>
      <div className="flex flex-col items-center justify-center min-h-screen p-4">
        <div className="w-full max-w-md space-y-8">
          <AuthHeader title="Welcome back" />
          <TabNavigation
            showRegister={showRegister}
            setShowRegister={setShowRegister}
          />
          <Transition
            show={!showRegister}
            enter="transition ease-out duration-300 transform"
            enterFrom="opacity-0 translate-y-4"
            enterTo="opacity-100 translate-y-0"
            leave="transition ease-in duration-200 transform"
            leaveFrom="opacity-100 translate-y-0"
            leaveTo="opacity-0 -translate-y-4"
            as="div"
          >
            <div>
              <SignInForm />
            </div>
          </Transition>

          <Transition
            show={showRegister}
            enter="transition ease-out duration-300 transform"
            enterFrom="opacity-0 translate-y-4"
            enterTo="opacity-100 translate-y-0"
            leave="transition ease-in duration-200 transform"
            leaveFrom="opacity-100 translate-y-0"
            leaveTo="opacity-0 -translate-y-4"
            as="div"
          >
            <div>
              <RegisterForm />
            </div>
          </Transition>

          <p className="mt-8 text-center text-gray-600">
            Don&apos;t have an account?
            <button
              onClick={() => setShowRegister(!showRegister)}
              className="text-secondary-300 font-medium hover:text-secondary-400"
            >
              {showRegister ? "Login" : "Register"}
            </button>
          </p>
        </div>
      </div>
    </>
  );
};

export default isLoggedIn(Auth);
