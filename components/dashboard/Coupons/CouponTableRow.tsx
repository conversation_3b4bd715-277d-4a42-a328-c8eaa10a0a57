import React, { useState, useRef, useEffect, useCallback } from "react";
import { MoreH<PERSON><PERSON>tal, Trash2, <PERSON><PERSON><PERSON><PERSON><PERSON>, Co<PERSON> } from "lucide-react";
import Button from "@/components/Button";
import Modal from "@/components/Modal";
import { toast } from "react-toastify";
import { CouponDetailsType } from "@/store/useOrderStore";
import { constructErrorMessage, formatDate } from "@/utils/functions";
import { deleteData } from "@/api";

interface CouponTableRowProps {
  coupon: CouponDetailsType;
  refetch: () => void;
}

const CouponTableRow: React.FC<CouponTableRowProps> = ({
  coupon,
  refetch = () => {}
}) => {
  const [isDeleting, setIsDeleting] = useState(false);
  const [showDropdown, setShowDropdown] = useState(false);
  const [showDeleteModal, setShowDeleteModal] = useState(false);
  const [isCopied, setIsCopied] = useState(false);
  const dropdownRef = useRef<HTMLDivElement>(null);
  const isCouponExpired = new Date(coupon?.expiresAt || "") <= new Date();

  // Close dropdown when clicking outside
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (
        dropdownRef.current &&
        !dropdownRef.current.contains(event.target as Node)
      ) {
        setShowDropdown(false);
      }
    };

    document.addEventListener("mousedown", handleClickOutside);
    return () => {
      document.removeEventListener("mousedown", handleClickOutside);
    };
  }, []);

  const handleCopyCode = useCallback(async () => {
    try {
      await navigator.clipboard.writeText(coupon.code);
      setIsCopied(true);
      toast.success("Code copied to clipboard!");
      setTimeout(() => setIsCopied(false), 2000); // Reset after 2 seconds
    } catch (err) {
      console.error("Failed to copy:", err);
    }
  }, [coupon]);

  const handleDeleteClick = useCallback(() => {
    setShowDropdown(false);
    setShowDeleteModal(true);
  }, []);

  const handleConfirmDelete = useCallback(async () => {
    setIsDeleting(true);
    try {
      await deleteData(`/coupon/${coupon?.id}`);
      refetch();
      toast("Coupon deleted successfully!");
      // setShowDeleteModal(false);
    } catch (error) {
      toast(
        constructErrorMessage(
          error as ApiErrorResponseType,
          "Unable to delete coupon!"
        )
      );
    } finally {
      setIsDeleting(false);
    }
  }, [coupon, refetch]);

  const handleCancelDelete = useCallback(() => {
    setShowDeleteModal(false);
  }, []);
  return (
    <>
      <tr className={`hover:bg-gray-50 ${isCouponExpired ? "opacity-40" : ""}`}>
        {/* Coupon Code */}
        <td className="px-4 py-4 whitespace-nowrap text-sm font-medium text-gray-900">
          <div className="flex items-center space-x-2">
            <span>{coupon?.code}</span>
            <button
              onClick={handleCopyCode}
              className="text-gray-400 hover:text-gray-600 transition-colors"
              title={isCopied ? "Copied!" : "Copy code"}
            >
              <Copy className={`h-4 w-4 ${isCopied ? "text-green-500" : ""}`} />
            </button>
          </div>
        </td>

        {/* Discount Type */}
        <td className="px-4 py-4 whitespace-nowrap text-sm text-gray-500">
          {coupon?.discountType}
        </td>

        {/* Discount Amount */}
        <td className="px-4 py-4 whitespace-nowrap text-sm text-gray-500">
          {coupon?.discountType === "FIXED"
            ? (coupon?.discountAmount as AmountType)?.formatted?.withCurrency
            : `${coupon?.discountAmount}%`}
        </td>

        {/* Expires At */}
        <td className="px-4 py-4 whitespace-nowrap text-sm text-gray-500">
          {formatDate(coupon?.expiresAt || new Date())}
        </td>
        <td className="px-4 py-4 whitespace-nowrap text-sm text-white">
          <span
            className={`px-2 py-1 rounded-full ${
              isCouponExpired ? "bg-red-400" : "bg-green-400"
            }`}
          >
            {isCouponExpired ? "Expired" : "Active"}
          </span>
        </td>

        {/* Actions */}
        <td className="px-4 py-4 whitespace-nowrap text-right text-sm font-medium">
          <div className="relative" ref={dropdownRef}>
            <Button
              onClick={() => setShowDropdown(!showDropdown)}
              className="text-gray-400 hover:text-gray-500 bg-transparent"
            >
              <MoreHorizontal className="h-5 w-5" />
            </Button>
            {showDropdown && (
              <div
                className="origin-top-right absolute right-0 mt-2 w-48 rounded-md shadow-lg bg-white ring-1 ring-black ring-opacity-5 z-[10000]"
                // Ensure dropdown is above table
              >
                <div
                  className="py-1"
                  role="menu"
                  aria-orientation="vertical"
                  aria-labelledby="options-menu"
                >
                  <button
                    onClick={handleDeleteClick}
                    className="flex items-center w-full px-4 py-2 text-sm text-red-600 hover:bg-gray-100"
                    role="menuitem"
                  >
                    <Trash2 className="mr-3 h-4 w-4" />
                    Delete
                  </button>
                </div>
              </div>
            )}
          </div>
        </td>
      </tr>

      {/* Delete Confirmation Modal */}
      <Modal
        opened={showDeleteModal}
        onClose={handleCancelDelete}
        title="Confirm Delete"
        position="center"
        contentClassName="bg-white p-6 rounded-2xl w-[400px]"
      >
        <div className="flex flex-col items-center">
          <div className="flex items-center justify-center w-12 h-12 rounded-full bg-red-100 mb-4">
            <AlertTriangle className="h-6 w-6 text-red-600" />
          </div>

          <h3 className="text-lg font-medium text-gray-900 mb-2">
            Delete Coupon
          </h3>

          <p className="text-sm text-gray-500 text-center mb-6">
            Are you sure you want to delete the coupon
            <span className="font-medium">{coupon?.code}</span>? This action
            cannot be undone.
          </p>

          <div className="flex justify-end space-x-3 w-full">
            <Button
              variant="default"
              onClick={handleCancelDelete}
              className="px-4 py-2"
            >
              Cancel
            </Button>
            <Button
              loading={isDeleting}
              variant="default"
              onClick={handleConfirmDelete}
              className="px-4 py-2 bg-red-600 hover:bg-red-700 text-white"
            >
              {isDeleting ? "Deleting..." : "Delete"}
            </Button>
          </div>
        </div>
      </Modal>
    </>
  );
};

export default CouponTableRow;
