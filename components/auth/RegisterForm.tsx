import { useForm } from "react-hook-form";
import Input<PERSON>ield from "../general/InputField";
import { useCallback, useState } from "react";
import Button from "../Button";
import { postData } from "@/api";
import { UserDetailsType } from "@/store/useUserStore";
import { constructErrorMessage } from "@/utils/functions";
import { useRouter } from "next/router";
import { toast } from "react-toastify";
import { verificationTypes } from "@/utils/variables";

export type RegisterBodyType = {
  firstName: string;
  lastName: string;
  email: string;
  password: string;
  repeatPassword: string;
};

export type RegisterResponseType = {
  message: string;
  token: string;
  user: UserDetailsType;
};

const defaultValues: RegisterBodyType = {
  firstName: "",
  lastName: "",
  email: "",
  password: "",
  repeatPassword: ""
};

const RegisterForm: React.FC = () => {
  const [registerError, setRegisterError] = useState<string | null>(null);
  const {
    register,
    handleSubmit,
    watch,
    setError,
    formState: { errors, isSubmitting, isValid }
  } = useForm({
    defaultValues,
    mode: "onChange"
  });

  const { push, query } = useRouter();
  const { redirect } = query || {};

  const registerUser = useCallback(
    async (body: RegisterBodyType) => {
      setRegisterError(null);
      try {
        // Exclude repeatPassword before sending request
        const { repeatPassword: _, ...registerData } = body;

        const response = await postData<
          Omit<RegisterBodyType, "repeatPassword">,
          ApiCallResponseType<RegisterResponseType>
        >("/auth/register", registerData);
        const { data } = response?.data;

        // Display success message
        toast(
          data?.message ||
            "Registration successful! Please verify your account."
        );
        // Display success message
        toast(
          data?.message ||
            "Registration successful! Please verify your account."
        );

        // Redirect to OTP verification page
        push(
          `/auth/verify-otp?token=${data?.token}&email=${body?.email}&type=${
            verificationTypes?.emailVerification
          }${redirect ? `&redirect=${redirect}` : ""}`
        );
      } catch (error: unknown) {
        const apiError = error as ApiErrorResponseType;
        if (apiError?.response?.data?.errors) {
          const errorKeys = Object.keys(apiError.response.data.errors || {});
          errorKeys.forEach((key) => {
            setError(key as keyof RegisterBodyType, {
              message:
                apiError.response?.data?.errors?.[key] || "Error occurred"
            });
          });
        }
        toast(
          constructErrorMessage(
            apiError,
            "Unable to process request! Please try again later"
          )
        );
      }
    },
    [push, setError, redirect]
  );

  return (
    <form onSubmit={handleSubmit(registerUser)} className="mt-8 space-y-3">
      {registerError && (
        <p
          onClick={() => {
            setRegisterError(null);
          }}
          className="text-sm py-3 px-5 text-center bg-red-100 text-red-800 rounded-md"
        >
          {registerError}
        </p>
      )}
      <div className="space-y-1">
        <InputField
          id="firstName"
          type="text"
          label="First name*"
          required
          className="w-full py-3 text-gray-500 rounded-lg"
          placeholder="Enter your first name"
          {...register("firstName", {
            required: "Please provide your first name"
          })}
          error={errors?.firstName?.message}
        />
        <InputField
          id="lastName"
          type="text"
          label="Last name*"
          required
          className="w-full py-3 text-gray-500 rounded-lg"
          placeholder="Enter your last name"
          {...register("lastName", {
            required: "Please provide your last name"
          })}
          error={errors?.lastName?.message}
        />
        <InputField
          id="email"
          type="email"
          label="Email*"
          required
          className="w-full py-3 text-gray-500 rounded-lg"
          placeholder="Enter your email"
          {...register("email", {
            required: "Please provide your email",
            pattern: {
              value: /^[A-Z0-9._%+-]+@[A-Z0-9.-]+\.[A-Z]{2,}$/i,
              message: "Invalid email address"
            }
          })}
          error={errors?.email?.message}
        />
        <div>
          <InputField
            id="password"
            type="password"
            label="Password*"
            required
            className="w-full py-3 text-gray-500 rounded-lg"
            placeholder="Create a password"
            {...register("password", {
              required: "Please provide a password",
              minLength: {
                value: 8,
                message: "Password must be at least 8 characters long"
              }
            })}
            error={errors?.password?.message}
          />
          <p className="mt-1 text-sm text-gray-500">
            Must be at least 8 characters.
          </p>
        </div>
        <InputField
          id="repeatPassword"
          type="password"
          label="Repeat Password *"
          required
          className="w-full py-3 text-gray-500 rounded-lg"
          placeholder="Repeat your password"
          {...register("repeatPassword", {
            required: "Please confirm your password",
            validate: (value) =>
              value === watch("password") || "Passwords do not match"
          })}
          error={errors?.repeatPassword?.message}
        />
      </div>
      <Button
        type="submit"
        className="w-full !bg-secondary-300 text-white hover:bg-secondary-400 focus:scale-95 transition-all duration-200"
        disabled={!isValid || isSubmitting}
        loading={isSubmitting}
      >
        Get Started
      </Button>
    </form>
  );
  return (
    <form onSubmit={handleSubmit(registerUser)} className="mt-8 space-y-3">
      {registerError && (
        <p
          onClick={() => {
            setRegisterError(null);
          }}
          className="text-sm py-3 px-5 text-center bg-red-100 text-red-800 rounded-md"
        >
          {registerError}
        </p>
      )}
      <div className="space-y-1">
        <InputField
          id="firstName"
          type="text"
          label="First name*"
          required
          className="w-full py-3 text-gray-500 rounded-lg"
          placeholder="Enter your first name"
          {...register("firstName", {
            required: "Please provide your first name"
          })}
          error={errors?.firstName?.message}
        />
        <InputField
          id="lastName"
          type="text"
          label="Last name*"
          required
          className="w-full py-3 text-gray-500 rounded-lg"
          placeholder="Enter your last name"
          {...register("lastName", {
            required: "Please provide your last name"
          })}
          error={errors?.lastName?.message}
        />
        <InputField
          id="email"
          type="email"
          label="Email*"
          required
          className="w-full py-3 text-gray-500 rounded-lg"
          placeholder="Enter your email"
          {...register("email", {
            required: "Please provide your email",
            pattern: {
              value: /^[A-Z0-9._%+-]+@[A-Z0-9.-]+\.[A-Z]{2,}$/i,
              message: "Invalid email address"
            }
          })}
          error={errors?.email?.message}
        />
        <div>
          <InputField
            id="password"
            type="password"
            label="Password*"
            required
            className="w-full py-3 text-gray-500 rounded-lg"
            placeholder="Create a password"
            {...register("password", {
              required: "Please provide a password",
              minLength: {
                value: 8,
                message: "Password must be at least 8 characters long"
              }
            })}
            error={errors?.password?.message}
          />
          <p className="mt-1 text-sm text-gray-500">
            Must be at least 8 characters.
          </p>
        </div>
        <InputField
          id="repeatPassword"
          type="password"
          label="Repeat Password *"
          required
          className="w-full py-3 text-gray-500 rounded-lg"
          placeholder="Repeat your password"
          {...register("repeatPassword", {
            required: "Please confirm your password",
            validate: (value) =>
              value === watch("password") || "Passwords do not match"
          })}
          error={errors?.repeatPassword?.message}
        />
      </div>
      <Button
        type="submit"
        className="w-full !bg-secondary-300 text-white hover:bg-secondary-400 focus:scale-95 transition-all duration-200"
        disabled={!isValid || isSubmitting}
        loading={isSubmitting}
      >
        Get Started
      </Button>
    </form>
  );
};

export default RegisterForm;
