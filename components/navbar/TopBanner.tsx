// TopBanner.tsx
"use client";
import React, { useState } from "react";
import { Transition } from "@headlessui/react";
import { Phone, X } from "lucide-react";
import SectionContainer from "../layouts/SectionContainer";
import Link from "next/link";

const TopBanner = () => {
  // State to manage the visibility of the banner
  const [isOpen, setIsOpen] = useState(true);

  return (
    <Transition
      show={isOpen}
      enter="transition ease-out duration-300 transform"
      enterFrom="opacity-0 -translate-y-4"
      enterTo="opacity-100 translate-y-0"
      leave="transition ease-in duration-200 transform"
      leaveFrom="opacity-100 translate-y-0"
      leaveTo="opacity-0 -translate-y-4"
    >
      <SectionContainer
        className="bg-[#463C6C] text-white"
        contentContainerClassName="flex justify-between items-center py-2"
      >
        {/* Phone Number */}
        <Link
          href="tel:+966 12 62 74 600"
          className="md:flex hidden items-center gap-2"
        >
          <Phone size={16} />
          <span>+966 12 62 74 600</span>
        </Link>

        {/* Promo Text */}
        <div className="text-center text-sm font-medium">
          SIGN UP TO EMAILS AND SAVE 20% OFF YOUR ORDER
        </div>

        {/* Close Button */}
        <button
          title="Close notification"
          aria-label="Close notification"
          onClick={() => setIsOpen(false)}
          className="text-white hover:text-gray-300 transition"
        >
          <X size={16} />
        </button>
      </SectionContainer>
    </Transition>
  );
};

export default TopBanner;
