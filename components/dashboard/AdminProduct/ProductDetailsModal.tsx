import React, { useCallback, useState } from "react";
import Modal from "@/components/Modal";
import CustomImage from "@/components/general/CustomImage";
import Button from "@/components/Button";
import {
  Edit,
  Trash2,
  X,
  ChevronDown,
  ChevronUp,
  DollarSign,
  Tag,
  Box,
  Loader
} from "lucide-react";

import { deleteData } from "@/api";
import { toast } from "react-toastify";
import { constructErrorMessage } from "@/utils/functions";
import useProduct from "@/hooks/useProduct";
import EditProductModal from "./EditProductModal";
import { ProductDetailsType } from "@/components/product/ProductCard";

interface ProductDetailsModalProps {
  product: ProductDetailsType;
  isOpen: boolean;
  onClose: () => void;
  refetch: () => void;
}

const ProductDetailsModal: React.FC<ProductDetailsModalProps> = ({
  product,
  isOpen,
  onClose,
  refetch = () => {}
}) => {
  const [confirmDelete, setConfirmDelete] = useState(false);
  const [showFullDescription, setShowFullDescription] = useState(false);
  const [isDeleting, setIsDeleting] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [isEditModalOpen, setIsEditModalOpen] = useState(false);
  const { fetchProducts } = useProduct();

  const handleEdit = useCallback(() => {
    setIsEditModalOpen(true);
  }, []);

  const handleDelete = useCallback(async () => {
    if (!confirmDelete) {
      setConfirmDelete(true);
      return;
    }
    setIsDeleting(true);
    try {
      await deleteData(`/product/${product?.id}`);
      toast("Product deleted successfully!", { autoClose: 1000 });
      refetch();
      onClose();
    } catch (error) {
      setError(
        constructErrorMessage(
          error as ApiErrorResponseType,
          "Error deleting products!"
        )
      );
      setConfirmDelete(false);
    } finally {
      setIsDeleting(false);
    }
  }, [confirmDelete, product, onClose, refetch]);

  const cancelDelete = useCallback(() => {
    setConfirmDelete(false);
    setError(null);
  }, []);

  // Truncate description if too long
  const shortDescription =
    product.description?.length > 150
      ? `${product.description.substring(0, 150)}...`
      : product.description;

  const stockStatus = product.quantity > 0 ? "In Stock" : "Out of Stock";
  const stockStatusColor =
    product.quantity > 0 ? "text-green-600" : "text-red-600";

  return (
    <>
      {/* Edit Product Modal */}
      <EditProductModal
        product={product}
        isOpen={isEditModalOpen}
        onClose={() => setIsEditModalOpen(false)}
        onProductUpdated={fetchProducts}
      />

      {/* Product Details Modal */}
      <Modal
        opened={isOpen}
        onClose={onClose}
        position="center-right"
        dialogEnterFromAnimation="opacity-0 translate-x-full"
        dialogEnterToAnimation="opacity-100 translate-x-0"
        dialogLeaveFromAnimation="opacity-100 translate-x-0"
        dialogLeaveToAnimation="opacity-0 translate-x-full"
      >
        <div className="flex flex-col h-screen bg-white w-screen max-w-[23rem]">
          {/* Header */}
          <div className="flex justify-between items-center p-4 border-b sticky top-0 bg-white z-10">
            <h2 className="text-xl font-semibold">Product Details</h2>
            <button
              onClick={onClose}
              className="p-1 rounded-full hover:bg-gray-100 transition-colors"
              aria-label="Close"
            >
              <X className="h-5 w-5" />
            </button>
          </div>

          {/* Content */}
          <div className="flex-1 overflow-y-auto p-4">
            {/* Product Image */}
            <div className="mb-6">
              <div className="relative w-full h-64 rounded-lg overflow-hidden border border-gray-200 bg-gray-100">
                <CustomImage
                  src={product.images?.[0] || ""}
                  alt={product.name}
                  fill
                  className="object-contain"
                />
              </div>
            </div>

            {/* Product Info */}
            <div className="space-y-5">
              {/* Basic Info */}
              <div>
                <h3 className="text-lg font-medium text-gray-900">
                  {product.name}
                </h3>
                <p className="text-sm text-gray-500">ID: {product.id}</p>
              </div>

              {/* Details Grid */}
              <div className="grid grid-cols-2 gap-4">
                <div className="bg-gray-50 p-3 rounded-md">
                  <div className="flex items-center text-gray-500 mb-1">
                    <DollarSign className="h-3 w-3 mr-1" />
                    <p className="text-xs">Price</p>
                  </div>
                  <p className="font-medium">
                    {product.price.formatted.withCurrency}
                  </p>
                </div>
                <div className="bg-gray-50 p-3 rounded-md">
                  <div className="flex items-center text-gray-500 mb-1">
                    <Tag className="h-3 w-3 mr-1" />
                    <p className="text-xs">Category</p>
                  </div>
                  <p className="font-medium">{product.category}</p>
                </div>
                <div className="bg-gray-50 p-3 rounded-md">
                  <div className="flex items-center text-gray-500 mb-1">
                    <Box className="h-3 w-3 mr-1" />
                    <p className="text-xs">Stock</p>
                  </div>
                  <p className="font-medium">{product.quantity} units</p>
                </div>
                <div className="bg-gray-50 p-3 rounded-md">
                  <p className="text-xs text-gray-500 mb-1">Status</p>
                  <p
                    className={`font-medium ${
                      product.deletedAt ? "text-red-600" : stockStatusColor
                    }`}
                  >
                    {product.deletedAt ? "Deleted" : stockStatus || "In Stock"}
                  </p>
                </div>
              </div>

              {/* Description */}
              <div>
                <h4 className="text-sm font-medium text-gray-700 mb-2">
                  Description
                </h4>
                <div className="bg-gray-50 p-3 rounded-md">
                  <p className="text-sm text-gray-600 whitespace-pre-line">
                    {showFullDescription
                      ? product.description
                      : shortDescription}
                  </p>
                  {product.description?.length > 150 && (
                    <button
                      onClick={() =>
                        setShowFullDescription(!showFullDescription)
                      }
                      className="text-blue-500 text-xs flex items-center mt-2 hover:underline"
                    >
                      {showFullDescription ? (
                        <>
                          <ChevronUp className="h-3 w-3 mr-1" /> Show less
                        </>
                      ) : (
                        <>
                          <ChevronDown className="h-3 w-3 mr-1" /> Read more
                        </>
                      )}
                    </button>
                  )}
                </div>
              </div>
            </div>
          </div>

          {/* Footer with Actions */}
          <div className="border-t p-4 sticky bottom-0 bg-white z-10">
            {error && (
              <div className="mb-4 p-3 bg-red-50 text-red-600 text-sm rounded-md">
                {error}
              </div>
            )}
            {confirmDelete ? (
              <div className="space-y-2">
                <p className="text-sm text-red-600 font-medium">
                  Are you sure you want to delete this product?
                </p>
                <div className="flex justify-between">
                  <Button
                    variant="default"
                    className="flex items-center gap-2"
                    onClick={cancelDelete}
                    disabled={isDeleting}
                  >
                    <span>Cancel</span>
                  </Button>
                  <Button
                    variant="primary"
                    className="flex items-center gap-2 bg-red-600 hover:bg-red-700"
                    onClick={handleDelete}
                    disabled={isDeleting}
                  >
                    {isDeleting ? (
                      <Loader className="h-4 w-4 animate-spin" />
                    ) : (
                      <Trash2 className="h-4 w-4" />
                    )}
                    <span>{isDeleting ? "Deleting..." : "Confirm Delete"}</span>
                  </Button>
                </div>
              </div>
            ) : (
              <div className="flex justify-between">
                <Button
                  variant="default"
                  className="flex items-center gap-2"
                  onClick={handleDelete}
                  disabled={isDeleting || !!product.deletedAt}
                >
                  <Trash2 className="h-4 w-4" />
                  <span>Delete</span>
                </Button>
                <Button
                  variant="primary"
                  className="flex items-center gap-2"
                  onClick={handleEdit}
                  disabled={isDeleting}
                >
                  <Edit className="h-4 w-4" />
                  <span>Edit</span>
                </Button>
              </div>
            )}
          </div>
        </div>
      </Modal>
    </>
  );
};

export default ProductDetailsModal;
