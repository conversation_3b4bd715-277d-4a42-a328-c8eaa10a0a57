import { changeFirstLetterToCapitalLetter } from "@/utils/functions";
import { useParams } from "next/navigation";
import { useRouter } from "next/router";
import React, { useEffect, useState } from "react";

const filters = ["all", "active", "inactive"];

const CouponFilterTabs = () => {
  const { push } = useRouter();
  const params = useParams();
  const { status } = params || {};
  const [activeTab, setActiveTab] = useState("");

  useEffect(() => {
    setActiveTab(status?.toString() || "");
  }, [status]);
  return (
    <div className="mb-6 border-b border-gray-200">
      <div className="flex overflow-x-auto">
        {filters.map((tab) => (
          <button
            key={tab}
            className={`px-4 py-2 text-sm font-medium whitespace-nowrap ${
              activeTab?.toLowerCase() === tab?.toLowerCase()
                ? "text-primary border-b-2 border-primary"
                : "text-gray-500 hover:text-gray-700 hover:border-gray-300"
            }`}
            onClick={() => push(`/account/coupons/${tab}`)}
          >
            {changeFirstLetterToCapitalLetter(tab)}
          </button>
        ))}
      </div>
    </div>
  );
};

export default CouponFilterTabs;
