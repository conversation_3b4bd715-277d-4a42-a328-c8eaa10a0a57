import React from "react";
import SectionContainer from "../layouts/SectionContainer";
import { ArrowLeft } from "lucide-react";
import { useRouter } from "next/router";
import Link from "next/link";
import { useParams } from "next/navigation";
import { formatText } from "@/utils/functions";

const BreadCrumbs = () => {
  const { back } = useRouter();
  const param = useParams();
  const { slug } = param || {};
  const stringifiedSlug = slug?.toString() || "all";
  const isAllProducts = stringifiedSlug === "all";
  const itemList = stringifiedSlug?.split(",") || [];
  return (
    <SectionContainer contentContainerClassName="flex items-center gap-4">
      <div className="flex items-center gap-2 py-4">
        <button
          onClick={back}
          title="go back"
          className=" text-slate-500 text-sm inline-flex items-center gap-2"
        >
          <span className="inline-flex border border-slate-200 px-2 py-1">
            <ArrowLeft size={15} />
          </span>
          <span className="inline-block">Go back</span>
        </button>
      </div>
      <div className="flex items-center gap-1">
        <Link href="/" className="hover:text-secondary hover:font-semibold">
          Home
        </Link>
        <span className="inline-block">/</span>
        {!isAllProducts &&
          itemList?.length > 0 &&
          itemList?.map((item, index) => {
            const isLast = index === itemList?.length - 1;
            return (
              <div key={index} className="inline-flex items-center gap-1">
                <Link
                  className={`${isLast && "opacity-60"} ${
                    !isLast && "hover:text-secondary hover:font-semibold"
                  }`}
                  key={index}
                  href={`/products/${itemList?.slice(0, index + 1)?.join("/")}`}
                >
                  {formatText(item)}
                </Link>
                {!isLast && <span>/</span>}
              </div>
            );
          })}
        {isAllProducts && (
          <Link href={"/favorite"} className="opacity-60">
            Favorite
          </Link>
        )}
      </div>
    </SectionContainer>
  );
};

export default BreadCrumbs;
