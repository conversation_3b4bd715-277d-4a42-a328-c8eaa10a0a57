import React from "react";
import SectionContainer from "../layouts/SectionContainer";
import Image from "next/image";
import { productListingHeroImage } from "@/assets/images";
import { siteName } from "@/utils/variables";

const HeroSection = () => {
  return (
    <div className="w-full h-[clamp(20rem,35vw,35rem)] bg-slate-100 relative">
      <Image
        fill
        src={productListingHeroImage}
        className="object-cover object-left-center"
        alt={`${siteName}`}
      />
      <SectionContainer
        className="h-full w-full bg-black/30 relative"
        contentContainerClassName="flex flex-col items-end gap-4 h-full w-full justify-center text-right text-white"
      >
        <h1 className="font-bold text-[clamp(1.5rem,2.5vw,2.5rem)]">
          All Perfumes
        </h1>
        <p className="text-[clamp(1rem,1.5vw,1.5rem)] max-w-[40ch]">
          Our collection of Eau de Parfums span the domains of woody, oriental,
          fresh and floral
        </p>
      </SectionContainer>
    </div>
  );
};

export default HeroSection;
