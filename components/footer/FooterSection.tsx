import React from 'react';
import Link from 'next/link';

interface FooterSectionProps {
	title: string;
	links: { label: string; href: string }[];
}

export const FooterSection: React.FC<FooterSectionProps> = ({
	title,
	links,
}) => {
	return (
		<div>
			<h6 className='font-semibold mb-4'>{title}</h6>
			<ul className=''>
				{links.map((link, index) => (
					<li
						key={index}
						className='mb-2'>
						<Link
							href={link.href}
							className='hover:text-blue-500'>
							{link.label}
						</Link>
					</li>
				))}
			</ul>
		</div>
	);
};
