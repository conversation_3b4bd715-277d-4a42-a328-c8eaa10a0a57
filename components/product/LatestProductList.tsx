import React, { useCallback, useEffect, useRef, useState } from "react";
import ProductCard, { ProductDetailsType } from "./ProductCard";
import { Navigation, Autoplay } from "swiper/modules";
import SectionContainer from "../layouts/SectionContainer";
import { Swiper, SwiperRef, SwiperSlide } from "swiper/react";
import SectionHeader from "../general/SectionHeader";
import { ArrowLeft, ArrowRight } from "lucide-react";
import { getData } from "@/api";
import { useQuery } from "@tanstack/react-query";
import ProductCardLoader from "./ProductCardLoader";
import ErrorContainer from "../status/ErrorContainer";
import { constructErrorMessage } from "@/utils/functions";
import EmptyContainer from "../status/EmptyContainer";

const buttonClassName =
    "bg-slate-200 rounded-full size-10 inline-flex items-center absolute top-1/2 -translate-y-1/2 md:top-[unset] md:-translate-y-[unset] md:relative justify-center shrink-0 text-secondary hover:text-white hover:bg-secondary duration-300 z-10",
  leftButtonClassName = "left-3 md:left-[unset]",
  rightButtonClassName = "right-3 md:right-[unset]";

const LatestProductList = () => {
  const [fetchedProductList, setFetchedProductList] = useState<
    ProductDetailsType[] | null
  >(null);
  const queryFn = useCallback(async () => {
    return await getData<ApiCallResponseType<ProductDetailsType[]>>(
      "/products?latest=true"
    );
  }, []);
  const { isPending, error, data, refetch } = useQuery({
    queryKey: ["latest-products"],
    queryFn
  });
  const swiperRef = useRef<SwiperRef | null>(null);

  const nextProducts = () => {
    if (swiperRef.current) swiperRef?.current?.swiper?.slideNext();
  };

  const previousProducts = () => {
    if (swiperRef.current) swiperRef?.current?.swiper?.slidePrev();
  };

  useEffect(() => {
    if (data) {
      setFetchedProductList(data?.data?.data?.filter((item) => !!item));
    }
  }, [data]);

  return (
    <SectionContainer contentContainerClassName="py-10 md:py-20 flex flex-col relative">
      <div className="flex items-center gap-6 relative">
        <button
          onClick={previousProducts}
          title="slide latest product left"
          aria-label="slide latest product left"
          className={`${buttonClassName} ${leftButtonClassName}`}
        >
          <ArrowLeft size={15} />
        </button>
        <div className="flex flex-col gap-4 w-[100%] md:w-[calc(100%-8.2rem)]">
          <SectionHeader title="Latest products" />
          {isPending && !fetchedProductList && !error && (
            <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
              {new Array(4).fill(0).map((_, index) => (
                <ProductCardLoader key={index} />
              ))}
            </div>
          )}
          {!isPending && fetchedProductList && !error && (
            <>
              {fetchedProductList?.length > 0 && (
                <Swiper
                  modules={[Navigation, Autoplay]}
                  slidesPerView={1}
                  spaceBetween={10}
                  autoplay={false}
                  loop={false}
                  breakpoints={{
                    768: {
                      // Medium screens and up
                      slidesPerView: 4,
                      spaceBetween: 15
                    }
                  }}
                  slidesPerGroup={1}
                  ref={swiperRef}
                  className="w-full h-full"
                >
                  {fetchedProductList.map((item, index) => (
                    <SwiperSlide key={index}>
                      <ProductCard {...item} />
                    </SwiperSlide>
                  ))}
                </Swiper>
              )}
              {fetchedProductList.length < 1 && (
                <EmptyContainer description="No new products for now" />
              )}
            </>
          )}

          {error && (
            <ErrorContainer
              error={constructErrorMessage(
                error as ApiErrorResponseType,
                "Error occurred whilst fetching latest product"
              )}
              retryFunction={refetch}
            />
          )}
        </div>
        <button
          onClick={nextProducts}
          title="slide latest product left"
          aria-label="slide latest product left"
          className={`${buttonClassName} ${rightButtonClassName}`}
        >
          <ArrowRight size={15} />
        </button>
      </div>
    </SectionContainer>
  );
};

export default LatestProductList;
