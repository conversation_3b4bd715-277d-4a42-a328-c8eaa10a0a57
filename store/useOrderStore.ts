import { create } from "zustand";
import { CartDetailsType } from "./useCartStore";

export type CartBodyType = {
  firstName: string;
  lastName: string;
  email: string;
  mobileNumber: string;
};

export type CouponDetailsType = {
  discountAmount: AmountType | number;
  code: string;
  expiresAt?: Date;
  id?: string;
  createdAt?: Date;
  usageCount?: number;
  discountType: CouponDiscountType;
  user?: {
    id: string;
    firstName: string;
    lastName: string;
  };
};

export type OrderStatusType =
  | "PAID"
  | "PENDING"
  | "DELIVERED"
  | "SHIPPED"
  | "CANCELLED";

export type PaymentDetailsType = {
  authorization_url: string;
  access_code: string;
  reference: string;
};

export type CartResponse = {
  id: string;
  quantity?: number;
  price?: AmountType | number;
  // Add any other CartResponse properties you need
};

export type CouponDiscountType = "PERCENTAGE" | "FIXED";

export type DeliveryDetailsType = {
  firstName?: string;
  lastName?: string;
  address?: string;
  landmark?: string;
  longitude?: number;
  latitude?: number;
  zipCode?: string;
  email?: string;
  mobileNumber?: string;
  state?: string;
  country?: string;
  city?: string;
};

export type OrderDetailsType = {
  id: string;
  paidAt?: Date | null;
  deliveredAt?: Date | null;
  shippedAt?: Date | null;
  paymentInitiatedAt?: Date | null;
  couponType?: CouponDiscountType | null;
  couponAmount?: AmountType | number | null;
  createdAt: Date;
  shippingFee?: AmountType;
  total?: AmountType;
  subTotal?: AmountType;
  discount?: AmountType;
  taxFee?: AmountType;
  orderCode: string;
  status: OrderStatusType;
  redirectUrl?: string;
  deliveryDetails: DeliveryDetailsType;
  totalCarts: number;
  carts?: (CartResponse & CartDetailsType)[];
  user?: {
    firstName: string;
    lastName: string;
    email: string;
  } | null;
  isNew?: boolean;
};

const removeDuplicateOrders = (
  orderArray: OrderDetailsType[]
): OrderDetailsType[] => {
  // Create a map to track the highest quantity cart for each product ID
  const orderMap: { [id: string]: OrderDetailsType } = {};

  // Loop through each cart item
  for (const order of orderArray) {
    // Check if the product ID is already in the map
    if (!orderMap[order.id]) {
      orderMap[order.id] = order;
    }
  }

  // Return the values of the cartMap, which now contains only unique cart items with the highest quantity
  return Object.values(orderMap);
};

type OrderStoreType = {
  orders: OrderDetailsType[] | null;
  fetchingOrderError: string | null;
  isLoadingNextOrder: boolean;

  setOrders: (order: OrderDetailsType[]) => void;
  setFetchingOrderError: (error?: string | null) => void;
  isFetchingNextOrder: () => void;
  updateOrder: (order: OrderDetailsType) => void;
};

const initialValue = {
  orders: null,
  fetchingOrderError: null,
  isLoadingNextOrder: false
};

const useOrderStore = create<OrderStoreType>((set) => ({
  ...initialValue,
  setOrders: (orders) => {
    set({
      orders,
      isLoadingNextOrder: false,
      fetchingOrderError: null
    });
  },
  setFetchingOrderError: (fetchingOrderError = null) => {
    set({
      fetchingOrderError,
      isLoadingNextOrder: false
    });
  },
  isFetchingNextOrder: () => {
    set({
      isLoadingNextOrder: true,
      fetchingOrderError: null
    });
  },

  updateOrder: (order) => {
    set((prevState) => {
      const previousOrder = prevState.orders;
      let orders: OrderDetailsType[] = [];
      if (previousOrder) {
        const orderExists = !!previousOrder.find(
          (savedOrder) => savedOrder?.id === order?.id
        );

        if (orderExists) {
          orders = previousOrder?.map((savedOrder) =>
            savedOrder?.id === order?.id ? order : savedOrder
          );
        }
        if (!orderExists) {
          orders = [...previousOrder, order];
        }
      }

      if (!previousOrder) {
        orders = [order];
      }
      return {
        ...prevState,
        orders: removeDuplicateOrders(orders)
      };
    });
  }
}));

export default useOrderStore;
