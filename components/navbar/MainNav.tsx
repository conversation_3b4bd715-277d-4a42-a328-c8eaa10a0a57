// MainNav.tsx
import React from "react";
import SectionContainer from "../layouts/SectionContainer";
import Link from "next/link";
import useProduct from "@/hooks/useProduct";

const MainNav: React.FC = () => {
  const { categories, fetchingCategoriesError, fetchCategories } = useProduct();

  return (
    <SectionContainer
      className="border-gray-200"
      contentContainerClassName="md:flex hidden justify-center items-center px-6 py-5 border-t border-b"
    >
      {categories && !fetchingCategoriesError && (
        <nav className="hidden md:flex gap-8">
          {categories.map((data) => {
            const href = `/products/${data.name}`;
            return (
              <Link key={href} href={href} className="hover:text-purple-600">
                {data.name}
              </Link>
            );
          })}
        </nav>
      )}
      {!categories && !fetchingCategoriesError && (
        <div className="flex items-center gap-6">
          {new Array(6).fill(0).map((_, index) => (
            <span
              className="bg-slate-200 w-[5rem] h-3 rounded-full animate-pulse"
              key={index}
            />
          ))}
        </div>
      )}

      {fetchingCategoriesError && (
        <div className="items-center flex justify-center">
          <p>
            Error fetching categories
            <button
              onClick={() => fetchCategories()}
              title="refetch categories"
              aria-label="refetch-categories"
              className="underline text-red-500"
            >
              Refetch
            </button>
          </p>
        </div>
      )}
    </SectionContainer>
  );
};

export default MainNav;
