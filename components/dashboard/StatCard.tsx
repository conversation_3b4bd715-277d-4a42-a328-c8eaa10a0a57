import React from "react";

interface StatCardProps {
  title: string;
  value?: string;
  icon?: React.ReactNode;
  iconBgColor?: string;
  textColor?: string;
  className?: string;
}

const StatCard: React.FC<StatCardProps> = ({
  title,
  value,
  icon,
  iconBgColor = "bg-blue-50",
  textColor = "text-gray-900",
  className = ""
}) => {
  return (
    <div className={`bg-white p-5 rounded-lg shadow-sm ${className}`}>
      <div className="flex items-start justify-between">
        <div>
          <p className="text-sm text-gray-500 mb-1">{title}</p>
          {value && (
            <p className={`text-2xl font-semibold ${textColor}`}>{value}</p>
          )}
          {!value && (
            <span
              className="font-bold
		  "
            >
              ...
            </span>
          )}
        </div>
        {icon && (
          <div className={`p-2 rounded-full ${iconBgColor}`}>{icon}</div>
        )}
      </div>
    </div>
  );
};

export default StatCard;
