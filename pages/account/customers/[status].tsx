import React from "react";
import DashboardLayout from "@/components/dashboard/DashboardLayout";
import AllCustomers from "@/components/dashboard/Customers/AllCustomers";
import isAdmin from "@/hooks/isAdmin";
import Head from "next/head";
import { siteName } from "@/utils/variables";

interface CustomersPageProps extends React.FC {
  hideFooter?: boolean;
  hideNav?: boolean;
}

const CustomersPage: CustomersPageProps = () => {
  return (
    <>
      <Head>
        <title>All Users | Admin | {siteName}</title>
      </Head>
      <DashboardLayout>
        <div className="p-8">
          <AllCustomers />
        </div>
      </DashboardLayout>
    </>
  );
};

CustomersPage.hideFooter = true;
CustomersPage.hideNav = true;
export default isAdmin(CustomersPage);
