import React from "react";
import { CategoryDetailsType } from "@/store/useProductStore";
import CategoryTableRow from "./CategoryTableRow";
import CategoryTableHeader from "./CategoryTableHeader";

interface CategoryTableProps {
  categories: CategoryDetailsType[];
  selectedRows: Set<string>;
  onRowSelect: (id: string, checked: boolean) => void;
  onSelectAll: (checked: boolean) => void;
}

const CategoryTable: React.FC<CategoryTableProps> = ({
  categories,
  selectedRows,
  onRowSelect,
  onSelectAll
}) => {
  const isAllSelected =
    categories.length > 0 && selectedRows.size === categories.length;

  return (
    <div className="overflow-x-auto">
      <div className="align-middle inline-block min-w-full">
        <div className="shadowborder-b border-gray-200 sm:rounded-lg">
          <table className="min-w-full divide-y divide-gray-200">
            <CategoryTableHeader
              isAllSelected={isAllSelected}
              onSelectAll={onSelectAll}
            />
            <tbody className="bg-white divide-y divide-gray-200">
              {categories.length > 0 &&
                categories.map((category) => (
                  <CategoryTableRow
                    key={category.id}
                    category={category}
                    isSelected={selectedRows.has(category.id)}
                    onSelect={onRowSelect}
                  />
                ))}
              {categories.length < 1 && (
                <tr>
                  <td
                    colSpan={5}
                    className="px-4 py-10 text-center text-sm text-gray-500"
                  >
                    No categories found.
                  </td>
                </tr>
              )}
            </tbody>
          </table>
        </div>
      </div>
    </div>
  );
};

export default CategoryTable;
