import { getData } from '@/api';
import useProductStore, {
	CategoryDetailsType,
	AdminProducts,
} from '@/store/useProductStore';
import { constructErrorMessage } from '@/utils/functions';
import { useCallback } from 'react';

const useProduct = () => {
	const {
		setCategories,
		setProducts,
		setFetchingCategoriesError,
		setFetchingProductsError,
		...details
	} = useProductStore();

	const fetchCategories = useCallback(async () => {
		try {
			setFetchingCategoriesError();
			const { data } = await getData<
				ApiCallResponseType<CategoryDetailsType[]>
			>('/categories');
			setCategories(data?.data);
		} catch (error) {
			setFetchingCategoriesError(
				constructErrorMessage(
					error as ApiErrorResponseType,
					'Error fetching categories',
				),
			);
		}
	}, [setCategories, setFetchingCategoriesError]);

	const fetchProducts = useCallback(async () => {
		try {
			setFetchingProductsError();
			const { data } = await getData<ApiCallResponseType<AdminProducts[]>>(
				'/products',
			);
			setProducts(data?.data);
		} catch (error) {
			setFetchingProductsError(
				constructErrorMessage(
					error as ApiErrorResponseType,
					'Error fetching products',
				),
			);
		}
	}, [setProducts, setFetchingProductsError]);

	return { fetchCategories, fetchProducts, ...details };
};

export default useProduct;
