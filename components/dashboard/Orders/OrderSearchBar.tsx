import React, { useEffect, useState } from "react";
import { Search } from "lucide-react";
import InputField from "@/components/general/InputField";
import { useRouter } from "next/router";
import { useDebouncedCallback } from "use-debounce";

const OrderSearchBar = () => {
  const [searchText, setSearchText] = useState("");

  const { query, push, pathname } = useRouter();
  const { q } = query || {};

  const processSearch = useDebouncedCallback((search = "") => {
    const { q: _, ...otherQueries } = query || {};
    if (search) {
      push({ pathname, query: { ...otherQueries, q: search, page: "1" } });
      return;
    }
    push({ pathname, query: { ...otherQueries, page: "1" } });
  }, 1000);

  useEffect(() => {
    setSearchText(q?.toString() || "");
  }, [q]);
  return (
    <div className="flex flex-col md:flex-row md:items-center md:justify-between mb-6">
      {/* Search Input */}
      <div className="relative flex-grow max-w-md">
        <div className="absolute inset-y-0 right-6 z-10 flex items-center pointer-events-none cursor-pointer">
          <Search className="h-5 w-5 text-gray-400" />
        </div>
        <InputField
          type="text"
          placeholder="Search orders..."
          value={searchText}
          onChange={(e) => {
            const value = (e?.target as HTMLInputElement)?.value;
            setSearchText(value);
            processSearch(value);
          }}
          className="block w-full pl-10 pr-3 py-2 rounded-lg 
                    focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 
                    transition-colors duration-200 ease-in-out"
        />
      </div>
    </div>
  );
};

export default OrderSearchBar;
