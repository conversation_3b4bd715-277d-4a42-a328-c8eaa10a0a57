import React, { useState, useEffect } from 'react';
import Modal from '@/components/Modal';
import Button from '@/components/Button';
import InputField from '@/components/general/InputField';
import { CategoryDetailsType } from '@/store/useProductStore';
import { toast } from 'react-toastify';
import { constructErrorMessage } from '@/utils/functions';
import { X } from 'lucide-react';
import { patchData, postData } from '@/api';

interface AddCategoryModalProps {
	opened: boolean;
	onClose: () => void;
	onCategoryAdded?: () => void;
	category?: CategoryDetailsType;
	isEditing?: boolean;
}

const AddCategoryModal: React.FC<AddCategoryModalProps> = ({
	opened,
	onClose,
	onCategoryAdded,
	category,
	isEditing = false,
}) => {
	const [categoryName, setCategoryName] = useState('');
	const [isSubmitting, setIsSubmitting] = useState(false);
	const [error, setError] = useState<string | null>(null);

	useEffect(() => {
		if (category && isEditing) {
			setCategoryName(category.name);
		}
	}, [category, isEditing]);

	const handleSubmit = async (e: React.FormEvent) => {
		e.preventDefault();
		setIsSubmitting(true);
		setError(null);

		try {
			if (isEditing && category) {
				// Update existing category
				await patchData(`/category/${category?.id}`, { name: categoryName });
				toast.success('Category updated successfully!');
			} else {
				// Create new category
				await postData('/category', { name: categoryName });
				toast.success('Category added successfully!');
			}

			if (onCategoryAdded) {
				onCategoryAdded();
			}
		} catch (err: unknown) {
			const errorMessage = constructErrorMessage(
				err as ApiErrorResponseType,
				isEditing
					? 'Failed to update category. Please try again.'
					: 'Failed to add category. Please try again.',
			);
			setError(errorMessage);
			toast.error(errorMessage);
			console.error('[Category Error]:', err);
		} finally {
			setIsSubmitting(false);
		}
	};

	return (
		<Modal
			opened={opened}
			onClose={onClose}
			position='center'
			contentClassName='bg-white p-6 rounded-lg max-w-md mx-auto'>
			<div className='flex justify-between items-center mb-4'>
				<h2 className='text-xl font-semibold'>
					{isEditing ? 'Edit Category' : 'Add New Category'}
				</h2>
				<button
					title='close modal'
					onClick={onClose}
					className='text-gray-500 hover:text-gray-700'>
					<X className='h-5 w-5' />
				</button>
			</div>

			<form onSubmit={handleSubmit}>
				<div className='mb-4'>
					<InputField
						label='Category Name'
						id='categoryName'
						value={categoryName}
						onChange={(e: React.ChangeEvent<HTMLInputElement>) =>
							setCategoryName(e.target.value)
						}
						placeholder='Enter category name'
						required
					/>
				</div>

				{error && <p className='text-red-500 text-sm mb-4'>{error}</p>}

				<div className='flex justify-end space-x-2'>
					<Button
						type='button'
						variant='white'
						onClick={onClose}
						disabled={isSubmitting}>
						Cancel
					</Button>
					<Button
						type='submit'
						variant='primary'
						disabled={isSubmitting || !categoryName.trim()}>
						{isSubmitting
							? 'Saving...'
							: isEditing
							? 'Update Category'
							: 'Add Category'}
					</Button>
				</div>
			</form>
		</Modal>
	);
};

export default AddCategoryModal;
