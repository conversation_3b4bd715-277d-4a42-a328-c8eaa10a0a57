import React, { useCallback, useEffect, useState } from "react";
import CustomerTableRow from "./CustomerTableRow";
import CustomerTableHeader from "./CustomerTableHeader";
import { useParams } from "next/navigation";
import { useQuery } from "@tanstack/react-query";
import { useRouter } from "next/router";
import { UserDetailsType } from "@/store/useUserStore";
import { getData } from "@/api";
import { ChevronLeft, ChevronRight, Loader2 } from "lucide-react";
import ErrorContainer from "@/components/status/ErrorContainer";
import { constructErrorMessage } from "@/utils/functions";
import Button from "@/components/Button";
const CustomerTable = () => {
  const numberOfColumns = 7;
  const [users, setUsers] = useState<UserDetailsType[] | null>(null);
  const [totalPage, setTotalPage] = useState(0);
  const params = useParams();
  const { status } = params || {};
  const { query, push, pathname } = useRouter();
  const { page: queryPage, q } = query || {};
  const page = parseInt(queryPage?.toString() || "1");
  const fetchAllUsers = useCallback(
    async (page: number) => {
      const urlSearchParams = new URLSearchParams();
      urlSearchParams.set("page", page?.toString());
      setUsers(null);
      if (q) {
        urlSearchParams.set("q", q?.toString());
      }

      if (status) {
        if (status === "new") {
          urlSearchParams.set("new_user", "true");
        }

        if (status !== "new" && status !== "all") {
          urlSearchParams.set("status", status?.toString());
        }
      }

      return await getData<ApiCallResponseType<UserDetailsType[]>>(
        `/users?${urlSearchParams?.toString()}`
      );
    },
    [q, status]
  );
  const { data, refetch, error, isLoading } = useQuery({
    queryKey: ["users-list", status, queryPage, q],
    queryFn: () => fetchAllUsers(page)
  });

  const handlePageChange = useCallback(
    (page: number) => {
      push({ pathname, query: { ...query, page } });
    },
    [query, push, pathname]
  );

  useEffect(() => {
    if (data) {
      setUsers(data?.data?.data);
      setTotalPage(data?.data?.pagination?.totalPage);
    }
  }, [data]);
  return (
    <div className="overflow-x-auto">
      <div className="align-middle inline-block min-w-full">
        <div className="shadowborder-b border-gray-200 sm:rounded-lg">
          <table className="min-w-full divide-y divide-gray-200">
            <CustomerTableHeader />
            <tbody className="bg-white divide-y divide-gray-200">
              {!users && !error && (
                <tr>
                  <td
                    colSpan={numberOfColumns}
                    className="px-4 py-10 text-center text-sm text-gray-500"
                  >
                    <div className="flex justify-center items-center">
                      <Loader2 className="h-6 w-6 animate-spin mr-2" />
                      Loading users...
                    </div>
                  </td>
                </tr>
              )}
              {error && !users && (
                <tr>
                  <td
                    colSpan={numberOfColumns}
                    className="px-4 py-10 text-center text-sm"
                  >
                    <ErrorContainer
                      error={constructErrorMessage(
                        error as ApiErrorResponseType,
                        "Error loading coupons! Please try again"
                      )}
                      retryFunction={refetch}
                    />
                  </td>
                </tr>
              )}
              {users &&
                users.length > 0 &&
                users.map((user) => (
                  <CustomerTableRow key={user?.id} user={user} />
                ))}
              {users && users.length < 1 && (
                <tr>
                  <td
                    colSpan={9}
                    className="px-4 py-10 text-center text-sm text-gray-500"
                  >
                    No customers found.
                  </td>
                </tr>
              )}
            </tbody>
          </table>
        </div>
        {!error && (
          <div className="flex justify-center items-center mt-4 space-x-2">
            <Button
              variant="primary"
              size="small"
              loading={isLoading}
              onClick={() => {
                handlePageChange(page - 1);
              }}
              disabled={page <= 1}
            >
              <ChevronLeft className="h-4 w-4" />
            </Button>
            <span className="text-sm text-gray-700">
              Page {page} of {totalPage}
            </span>
            <Button
              variant="primary"
              size="small"
              loading={isLoading}
              onClick={() => {
                handlePageChange(page + 1);
              }}
              disabled={page >= totalPage}
            >
              <ChevronRight className="h-4 w-4" />
            </Button>
          </div>
        )}
      </div>
    </div>
  );
};

export default CustomerTable;
