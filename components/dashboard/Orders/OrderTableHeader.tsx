// src/components/orders/OrderTableHeader.tsx
import React from "react";
// Assuming path is correct
import { ArrowUpDown } from "lucide-react";
import CheckBox from "@/components/general/CheckBox";

interface OrderTableHeaderProps {
  isAllSelected: boolean;
  onSelectAllOrder: (checked: boolean) => void;
}

const columns = [
  //   { id: "product", label: "Product", sortable: true },
  { id: "id", label: "Order code", sortable: true },
  { id: "date", label: "Date", sortable: true },
  { id: "customer", label: "Customer", sortable: false },
  { id: "total", label: "Total", sortable: true },
  { id: "paymentStatus", label: "Payment Status", sortable: false },
  { id: "items", label: "Items", sortable: true },
  { id: "orderStatus", label: "Order Status", sortable: false }
];

const OrderTableHeader: React.FC<OrderTableHeaderProps> = ({
  isAllSelected,
  onSelectAllOrder
}) => {
  return (
    <thead className="bg-gray-50">
      <tr>
        <th scope="col" className="pl-4 pr-2 py-3 text-left w-12">
          <CheckBox
            checked={isAllSelected}
            onChange={(e) =>
              onSelectAllOrder((e.target as HTMLInputElement).checked)
            }
            aria-label="Select all orders"
          />
        </th>

        {columns.map((col) => (
          <th
            key={col.id}
            scope="col"
            className={`px-3 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider ${
              col.id === "product" ? "w-1/5" : ""
            }`}
          >
            <button className="group inline-flex items-center">
              {col.label}
              {col.sortable && (
                <ArrowUpDown className="ml-1 h-3 w-3 text-gray-400 opacity-0 group-hover:opacity-100 transition-opacity" />
              )}
            </button>
          </th>
        ))}
        <th scope="col" className="relative pl-3 pr-4 py-3 w-12">
          <span className="sr-only">Actions</span>
        </th>
      </tr>
    </thead>
  );
};

export default OrderTableHeader;
