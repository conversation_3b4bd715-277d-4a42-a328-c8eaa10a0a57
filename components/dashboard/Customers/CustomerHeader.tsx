import React from 'react';

const CustomerHeader: React.FC = () => {
  return (
    <div className="flex flex-col md:flex-row justify-between items-start md:items-center mb-6">
      <div>
        <h1 className="text-2xl font-semibold text-gray-900">Customers</h1>
        <p className="text-sm text-gray-500 mt-1">
          Manage your customer base and view customer information
        </p>
      </div>
      <div className="mt-4 md:mt-0 flex space-x-3">
        <button className="px-4 py-2 bg-white border border-gray-300 rounded-md text-sm font-medium text-gray-700 hover:bg-gray-50">
          Export
        </button>
        <button className="px-4 py-2 bg-primary text-white rounded-md text-sm font-medium hover:bg-primary-dark">
          Add Customer
        </button>
      </div>
    </div>
  );
};

export default CustomerHeader;
