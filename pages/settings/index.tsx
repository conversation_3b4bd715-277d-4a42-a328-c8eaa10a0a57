import React, { useState } from "react";
import SectionContainer from "@/components/layouts/SectionContainer";
import Head from "next/head";
import { siteName } from "@/utils/variables";
import AccountSettings from "@/components/settings/AccountSettings";
import SecuritySettings from "@/components/settings/SecuritySettings";

const SettingsPage: React.FC = () => {
  const [activeTab, setActiveTab] = useState("account");

  return (
    <>
      <Head>
        <title>Settings | {siteName}</title>
      </Head>
      <SectionContainer>
        <div className="max-w-4xl mx-auto px-4 py-8">
          <h1 className="text-3xl font-bold mb-6">Settings</h1>

          {/* Tabs */}
          <div className="flex border-b mb-6">
            <button
              className={`py-2 px-4 font-medium ${
                activeTab === "account"
                  ? "border-b-2 border-secondary-500 text-secondary-600"
                  : "text-gray-500 hover:text-gray-700"
              }`}
              onClick={() => setActiveTab("account")}
            >
              Account
            </button>
            <button
              className={`py-2 px-4 font-medium ${
                activeTab === "security"
                  ? "border-b-2 border-secondary-500 text-secondary-600"
                  : "text-gray-500 hover:text-gray-700"
              }`}
              onClick={() => setActiveTab("security")}
            >
              Security
            </button>
          </div>

          {activeTab === "account" && <AccountSettings />}

          {activeTab === "security" && <SecuritySettings />}
        </div>
      </SectionContainer>
    </>
  );
};

export default SettingsPage;
