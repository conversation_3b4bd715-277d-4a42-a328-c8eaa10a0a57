import React from "react";
import SectionContainer from "../layouts/SectionContainer";
import SectionHeader from "../general/SectionHeader";
import ProductCard, { ProductDetailsType } from "./ProductCard";

const SimilarProducts: React.FC<ProductDetailsType> = ({
  similarProducts = []
}) => {
  if (similarProducts?.length < 1) {
    return null;
  }
  return (
    <SectionContainer contentContainerClassName="flex flex-col gap-4 py-20">
      <SectionHeader
        title="Similar Items You Might Like"
        className="max-w-[unset]"
      />
      <div className="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-4 gap-6">
        {similarProducts.map((details) => (
          <ProductCard {...details} key={details.id} />
        ))}
      </div>
    </SectionContainer>
  );
};

export default SimilarProducts;
