import type { Config } from "tailwindcss";

export default {
  darkMode: ["class"],
  content: [
    "./pages/**/*.{js,ts,jsx,tsx,mdx}",
    "./components/**/*.{js,ts,jsx,tsx,mdx}",
    "./app/**/*.{js,ts,jsx,tsx,mdx}"
  ],
  theme: {
    extend: {
      fontFamily: {
        ttchocolates: ["TT Chocolates"]
      },
      colors: {
        primary: {
          "50": "#140510",
          "100": "#290a20",
          "200": "#52143f",
          "300": "#7a1f5f",
          "400": "#a3297e",
          "500": "#cc339e",
          "600": "#d65cb1",
          "700": "#e085c5",
          "800": "#ebadd8",
          "900": "#f5d6ec",
          "950": "#faebf5",
          DEFAULT: "#8F246F",
          foreground: "hsl(var(--primary-foreground))"
        },
        secondary: {
          "50": "#0a0911",
          "100": "#131221",
          "200": "#262442",
          "300": "#393663",
          "400": "#4c4785",
          "500": "#6059a6",
          "600": "#7f7ab8",
          "700": "#9f9cc9",
          "800": "#bfbddb",
          "900": "#dfdeed",
          "950": "#efeef6",
          DEFAULT: "#403C6F",
          foreground: "hsl(var(--secondary-foreground))"
        },
        tetiary: {
          DEFAULT: "#F56630"
        },
        background: "hsl(var(--background))",
        foreground: "hsl(var(--foreground))",
        card: {
          DEFAULT: "hsl(var(--card))",
          foreground: "hsl(var(--card-foreground))"
        },
        popover: {
          DEFAULT: "hsl(var(--popover))",
          foreground: "hsl(var(--popover-foreground))"
        },
        muted: {
          DEFAULT: "hsl(var(--muted))",
          foreground: "hsl(var(--muted-foreground))"
        },
        accent: {
          DEFAULT: "hsl(var(--accent))",
          foreground: "hsl(var(--accent-foreground))"
        },
        destructive: {
          DEFAULT: "hsl(var(--destructive))",
          foreground: "hsl(var(--destructive-foreground))"
        },
        border: "hsl(var(--border))",
        input: "hsl(var(--input))",
        ring: "hsl(var(--ring))",
        chart: {
          "1": "hsl(var(--chart-1))",
          "2": "hsl(var(--chart-2))",
          "3": "hsl(var(--chart-3))",
          "4": "hsl(var(--chart-4))",
          "5": "hsl(var(--chart-5))"
        }
      },
      screens: {
        mdbetweenlg: "1200px"
      },
      borderRadius: {
        lg: "var(--radius)",
        md: "calc(var(--radius) - 2px)",
        sm: "calc(var(--radius) - 4px)"
      }
    }
  },
  plugins: [require("tailwindcss-animate")]
} satisfies Config;
