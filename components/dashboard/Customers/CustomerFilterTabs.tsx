import { changeFirstLetterToCapitalLetter } from "@/utils/functions";
import { useParams } from "next/navigation";
import { useRouter } from "next/router";
import React, { useEffect, useState } from "react";

const tabs = ["all", "active", "inactive", "new"];

const CustomerFilterTabs = () => {
  const [activeTab, setActiveTab] = useState(tabs[0]);
  const { push } = useRouter();
  const params = useParams();
  const { status } = params || {};

  useEffect(() => {
    setActiveTab(status?.toString() || "");
  }, [status]);

  return (
    <div className="mb-6">
      <div className="border-b border-gray-200">
        <nav className="flex -mb-px space-x-8">
          {tabs.map((tab) => (
            <button
              key={tab}
              onClick={() => push(`/account/customers/${tab}`)}
              className={`py-4 px-1 border-b-2 font-medium text-sm ${
                activeTab === tab
                  ? "border-primary text-primary"
                  : "border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300"
              }`}
            >
              {changeFirstLetterToCapitalLetter(tab)}
            </button>
          ))}
        </nav>
      </div>
    </div>
  );
};

export default CustomerFilterTabs;
