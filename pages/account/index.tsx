import React from "react";
import DashboardLayout from "@/components/dashboard/DashboardLayout";
import DashboardOverview from "@/components/dashboard/DashboardOverview";
import isAdmin from "@/hooks/isAdmin";
import Head from "next/head";
import { siteName } from "@/utils/variables";

// Define a custom type that extends React.FC and includes hideFooter and hideNav
interface DashboardPageProps extends React.FC {
  hideFooter?: boolean;
  hideNav?: boolean;
}

const DashboardPage: DashboardPageProps = () => {
  return (
    <>
      <Head>
        <title>Dashboard | Admin | {siteName}</title>
      </Head>
      <DashboardLayout>
        <div className="p-8">
          <h1 className="text-2xl font-medium mb-6">Dashboard</h1>
          <DashboardOverview />
        </div>
      </DashboardLayout>
    </>
  );
};

DashboardPage.hideFooter = true;
DashboardPage.hideNav = true;

export default isAdmin(DashboardPage);
