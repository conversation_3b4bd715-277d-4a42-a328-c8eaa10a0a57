import React, { useCallback, useEffect, useState } from "react";
import ErrorContainer from "@/components/status/ErrorContainer";
import CustomImage from "../general/CustomImage";
import { useQuery } from "@tanstack/react-query";
import { getData } from "@/api";
import { ProductDetailsType } from "../product/ProductCard";
import { constructErrorMessage } from "@/utils/functions";

interface TopProductsTableProps {
  className?: string;
}

const TopProductsTable: React.FC<TopProductsTableProps> = ({
  className = ""
}) => {
  const queryFn = useCallback(async () => {
    return await getData<ApiCallResponseType<ProductDetailsType[]>>(
      "/products?best_seller=true&limit=5"
    );
  }, []);

  const [products, setProducts] = useState<ProductDetailsType[] | null>(null);
  const { refetch, data, error } = useQuery({
    queryKey: ["top-selling-product"],
    queryFn
  });

  useEffect(() => {
    if (data) {
      setProducts(data?.data?.data);
    }
  }, [data]);
  return (
    <div className={`bg-white p-5 rounded-lg shadow-sm ${className}`}>
      <div className="flex items-center justify-between mb-4">
        <h3 className="text-lg font-medium">Top Selling Products</h3>
      </div>
      {error && !products && (
        <ErrorContainer
          error={constructErrorMessage(
            error as ApiErrorResponseType,
            "Error fetching products"
          )}
          retryFunction={refetch}
        />
      )}
      <div className="overflow-x-auto">
        <table className="w-full">
          <thead>
            <tr className="border-b border-gray-200">
              <th className="py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                Product
              </th>
              <th className="py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                Category
              </th>
              <th className="py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                Quantity
              </th>
              <th className="py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                Price
              </th>
              <th className="py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                Rating
              </th>
              <th className="py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                Discount Price
              </th>
            </tr>
          </thead>
          <tbody>
            {!products && (
              <tr>
                <td colSpan={7} className="py-10 text-center text-gray-500">
                  Loading top products...
                </td>
              </tr>
            )}
            {products &&
              products.map((product) => (
                <tr
                  key={product.id}
                  className="border-b border-gray-200 hover:bg-gray-50"
                >
                  <td className="py-4 text-sm font-medium text-gray-900">
                    <div className="flex items-center">
                      <div className="relative h-10 w-10 mr-3 rounded-lg overflow-hidden border border-gray-200 bg-gray-100">
                        {product.images && product.images.length > 0 && (
                          <CustomImage
                            src={product.images[0]}
                            alt={product.name}
                            fill
                            className="w-10 h-10 rounded-md object-cover "
                          />
                        )}
                      </div>
                      <div>
                        <div className="font-medium text-gray-900">
                          {product.name}
                        </div>
                        <div className="text-gray-500 text-xs">
                          {product.id.substring(0, 8)}...
                        </div>
                      </div>
                    </div>
                  </td>
                  <td className="py-4 text-sm text-gray-500">
                    {product.category}
                  </td>
                  <td className="py-4 text-sm text-gray-500">
                    {product.quantity}
                  </td>
                  <td className="py-4 text-sm text-gray-500">
                    {product.price.formatted.withCurrency}
                  </td>
                  <td className="py-4 text-sm text-gray-500">
                    {product.rating}
                  </td>
                  <td className="py-4 text-sm text-gray-500">
                    {product?.discount?.formatted?.withCurrency ||
                      "No discount"}
                  </td>
                </tr>
              ))}
          </tbody>
        </table>
      </div>
    </div>
  );
};

export default TopProductsTable;
