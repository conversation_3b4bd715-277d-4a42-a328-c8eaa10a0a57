import React from "react";
import SectionContainer from "@/components/layouts/SectionContainer";
import { shortSiteName, siteName } from "@/utils/variables";
import Head from "next/head";

const Careers = () => {
  const description = ` At ${shortSiteName}, we are always looking for talented individuals
            to join our team. Explore our open positions and become part of our
            journey.`;
  return (
    <>
      <Head>
        <title>Careers | {siteName}</title>
        <meta name="description" content={description} />
        <meta property="og:title" content="Careers" />
        <meta property="og:description" content={description} />
        <meta name="twitter:description" content={description} />
      </Head>
      <SectionContainer className="py-16">
        <div className="max-w-7xl mx-auto">
          <h1 className="text-4xl font-bold text-center text-[#342B66] mb-8">
            Join Our Team
          </h1>
          <p className="text-lg text-gray-600 text-center mb-12">
            At {shortSiteName}, we are always looking for talented individuals
            to join our team. Explore our open positions and become part of our
            journey.
          </p>

          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
            {/* Example Job Listing */}
            <div className="p-6 border rounded-lg shadow-sm hover:shadow-md transition-shadow">
              <h2 className="text-xl font-semibold text-gray-800 mb-4">
                Marketing Specialist
              </h2>
              <p className="text-gray-600 mb-4">
                Help us grow our brand and reach new customers with innovative
                marketing strategies.
              </p>
              <a
                href="/careers/marketing-specialist"
                className="text-secondary font-medium hover:underline"
              >
                Learn More
              </a>
            </div>

            <div className="p-6 border rounded-lg shadow-sm hover:shadow-md transition-shadow">
              <h2 className="text-xl font-semibold text-gray-800 mb-4">
                Software Engineer
              </h2>
              <p className="text-gray-600 mb-4">
                Join our tech team to build cutting-edge solutions for our
                customers.
              </p>
              <a
                href="/careers/software-engineer"
                className="text-secondary font-medium hover:underline"
              >
                Learn More
              </a>
            </div>

            <div className="p-6 border rounded-lg shadow-sm hover:shadow-md transition-shadow">
              <h2 className="text-xl font-semibold text-gray-800 mb-4">
                Customer Support Specialist
              </h2>
              <p className="text-gray-600 mb-4">
                Provide exceptional support to our customers and ensure their
                satisfaction.
              </p>
              <a
                href="/careers/customer-support"
                className="text-secondary font-medium hover:underline"
              >
                Learn More
              </a>
            </div>
          </div>
        </div>
      </SectionContainer>
    </>
  );
};

export default Careers;
