import CartCard from "@/components/cart/CartCard";
import CartCardLoader from "@/components/cart/CartCardLoader";
import Address from "@/components/checkout/Address";
import PaymentInformation from "@/components/checkout/PaymentInformation";
import SectionContainer from "@/components/layouts/SectionContainer";
import EmptyContainer from "@/components/status/EmptyContainer";
import ErrorContainer from "@/components/status/ErrorContainer";
import protectRoute from "@/hooks/protectRoute";
import useCart from "@/hooks/useCart";
import { siteName } from "@/utils/variables";
import { ArrowLeft } from "lucide-react";
import Head from "next/head";
import { useRouter } from "next/router";
import React from "react";

const Checkout = () => {
  const { back } = useRouter();
  const { carts, getCart, cartFetchingError } = useCart();
  return (
    <>
      <Head>
        <title>Checkout | {siteName}</title>
      </Head>
      <SectionContainer contentContainerClassName="flex items-center gap-4">
        <div className="flex items-center gap-2 py-4">
          <button
            onClick={back}
            title="go back"
            className=" text-slate-500 text-sm inline-flex items-center gap-2"
          >
            <span className="inline-flex border border-slate-200 px-2 py-1">
              <ArrowLeft size={15} />
            </span>
            <span className="inline-block">Go back</span>
          </button>
        </div>
      </SectionContainer>
      <SectionContainer contentContainerClassName="flex items-start gap-10 flex-col md:flex-row py-10">
        <div className="flex flex-col flex-1 gap-6 w-full md:w-[unset]">
          {!carts &&
            !cartFetchingError &&
            new Array(6)
              .fill(0)
              .map((_, index) => <CartCardLoader key={index} />)}

          {carts && !cartFetchingError && (
            <>
              {carts.length < 1 && (
                <EmptyContainer description="No cart at the moment" />
              )}

              {carts?.length > 0 && (
                <div className="flex flex-col w-full">
                  {carts.map((cartItem) => (
                    <CartCard {...cartItem} key={cartItem?.id} isCheckout />
                  ))}
                </div>
              )}
            </>
          )}

          {cartFetchingError && (
            <ErrorContainer error={cartFetchingError} retryFunction={getCart} />
          )}

          <Address />
        </div>
        <PaymentInformation />
      </SectionContainer>
    </>
  );
};

export default protectRoute(Checkout);
