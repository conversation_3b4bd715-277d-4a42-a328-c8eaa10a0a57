import { CartDetailsType } from "@/store/useCartStore";
import React, { useCallback, useEffect, useState } from "react";
import CustomImage from "../general/CustomImage";
import { Minus, Plus, Trash2 } from "lucide-react";
import Spinner from "../general/Spinner";
import { deleteData, putData } from "@/api";
import { toast } from "react-toastify";
import { constructErrorMessage } from "@/utils/functions";
import useCart from "@/hooks/useCart";
import InputField from "../general/InputField";
import { useDebouncedCallback } from "use-debounce";
import useUser from "@/hooks/useUser";

const cartQuantityClassName =
  "disabled:opacity-40 cursor-pointer disabled:cursor-not-allowed";

const CartCard: React.FC<CartDetailsType & { isCheckout?: boolean }> = ({
  price,
  product,
  id,
  isCheckout,
  quantity: cartQuantity = 0
}) => {
  const { userToken } = useUser();
  const {
    removeCart: removeCartFromStore,
    updateCart,
    updateCartQuantityInLocalStorage,
    removeCartStoredInLocalStorage
  } = useCart();
  const [isRemovingCart, setIsRemovingCart] = useState(false);
  const [isUpdatingCart, setIsUpdatingCart] = useState(false);
  const [newQuantity, setNewQuantity] = useState(cartQuantity || 1);
  const quantity = product?.quantity || 0;

  const removeCart = useCallback(async () => {
    if (!id || isRemovingCart) {
      return;
    }
    setIsRemovingCart(true);

    if (!userToken) {
      removeCartStoredInLocalStorage(product?.id || "");
      toast.success(`${product?.name} removed from cart`, {
        autoClose: 500
      });
      return setIsRemovingCart(false);
    }
    try {
      await deleteData(`/cart/${id}`);
      removeCartFromStore(id);
      toast.success(`${product?.name} removed from cart`, {
        autoClose: 500
      });
    } catch (error) {
      toast(
        constructErrorMessage(
          error as ApiErrorResponseType,
          "Error removing cart item"
        )
      );
    } finally {
      setIsRemovingCart(false);
    }
  }, [
    id,
    isRemovingCart,
    removeCartFromStore,
    product,
    userToken,
    removeCartStoredInLocalStorage
  ]);

  const updateCartQuantity = useDebouncedCallback(
    // function
    async (sentQuantity: number) => {
      if (!id || isUpdatingCart || cartQuantity === sentQuantity) {
        return;
      }
      setIsUpdatingCart(true);
      if (!userToken) {
        updateCartQuantityInLocalStorage(product?.id || "", sentQuantity);
        toast("Cart quantity update successfully", {
          autoClose: 500
        });
        return setIsUpdatingCart(false);
      }
      try {
        const { data } = await putData<
          { quantity: number },
          ApiCallResponseType<CartDetailsType>
        >(`/cart/${id}`, { quantity: sentQuantity });

        updateCart(data?.data);
        toast("Cart quantity update successfully", {
          autoClose: 500
        });
      } catch (error) {
        setNewQuantity(cartQuantity);
        toast(
          constructErrorMessage(
            error as ApiErrorResponseType,
            "Unable to update cart quantity"
          )
        );
      } finally {
        setIsUpdatingCart(false);
      }
    },
    // delay in ms
    1000
  );

  useEffect(() => {
    setNewQuantity(cartQuantity);
  }, [cartQuantity]);

  return (
    <div
      className={`flex items-center gap-4 ${isCheckout ? "border-b py-5" : ""}`}
    >
      <div
        className={` ${
          isCheckout ? "size-24" : "size-28"
        } bg-slate-200 rounded-md relative overflow-hidden`}
      >
        <CustomImage
          src={product?.images?.[0] || ""}
          alt={product?.name || "Product image"}
          fill
          className="object-cover object-center h-full w-full"
        />
      </div>
      <div className="flex-1 flex flex-col gap-3">
        <div className="flex items-center justify-between gap-10">
          <h1 className="font-semibold flex-1">{product?.name}</h1>

          <p className="font-bold">{price?.formatted?.withCurrency}</p>
        </div>
        <p className="text-xs opacity-60">Size: 100 ML</p>
        {!isCheckout && (
          <div className="flex items-center justify-between gap-10">
            <div className="p-1 px-2 bg-slate-100 rounded-full inline-flex items-center gap-4 text-xs">
              <button
                title="Subtract 1"
                aria-label="Subtract 1"
                disabled={newQuantity < 2 || isUpdatingCart}
                className={cartQuantityClassName}
                onClick={() => {
                  setNewQuantity((prevState) => {
                    const quantityToSave = prevState - 1;

                    if (quantityToSave > 0) {
                      updateCartQuantity(quantityToSave);
                      return quantityToSave;
                    }

                    return prevState;
                  });
                }}
              >
                <Minus size={13} />
              </button>
              <InputField
                placeholder=" "
                disabled={isUpdatingCart}
                value={newQuantity?.toString()}
                onChange={(e) => {
                  const value = (e?.target as HTMLInputElement)?.value;
                  const valueAsANumber = Number(value || "0");

                  if (isNaN(valueAsANumber)) {
                    return;
                  }

                  if (valueAsANumber > quantity) {
                    toast("You cannot add more than the available quantity");
                    setNewQuantity(quantity);
                    return;
                  }
                  updateCartQuantity(valueAsANumber);
                  setNewQuantity(valueAsANumber);
                }}
                inputClassName="!w-[2rem] bg-transparent border-none !py-1 text-center !px-0 font-medium disabled:opacity-30"
              />
              <button
                disabled={!(newQuantity < quantity) || isUpdatingCart}
                title="Add 1 more"
                arial-label={"Add 1 more"}
                className={cartQuantityClassName}
                onClick={() => {
                  setNewQuantity((prevState) => {
                    const quantityToSave = prevState + 1;

                    if (quantityToSave < quantity) {
                      updateCartQuantity(quantityToSave);
                      return quantityToSave;
                    }
                    updateCartQuantity(quantity);
                    return quantity;
                  });
                }}
              >
                <Plus size={13} />
              </button>
            </div>

            {!isRemovingCart && (
              <button
                title="Remove from cart"
                arial-label="remove from cart"
                onClick={removeCart}
                className="hover:scale-125 transition-all duration-300 opacity-70"
              >
                <Trash2 size={17} />
              </button>
            )}
            {isRemovingCart && <Spinner className="size-4 !border-2" />}
          </div>
        )}
      </div>
    </div>
  );
};

export default CartCard;
