import React, { useEffect } from "react";
import DashboardLayout from "@/components/dashboard/DashboardLayout";
import AddProduct from "@/components/dashboard/AdminProduct/AddProduct";
import isAdmin from "@/hooks/isAdmin";
import Head from "next/head";
import { siteName } from "@/utils/variables";
import useProduct from "@/hooks/useProduct";
import Button from "@/components/Button";
import { useRouter } from "next/router";
import Spinner from "@/components/general/Spinner";

interface AddProductPageProps extends React.FC {
  hideFooter?: boolean;
  hideNav?: boolean;
}

const AddProductPage: AddProductPageProps = () => {
  const { push } = useRouter();
  const { categories, fetchCategories } = useProduct();
  useEffect(() => {
    if (!categories) {
      fetchCategories();
    }
  }, [categories, fetchCategories]);
  return (
    <>
      <Head>
        <title>Add products | Admin | {siteName}</title>
      </Head>
      <DashboardLayout>
        {!categories && (
          <div className="py-20 items-center justify-center flex flex-col">
            <Spinner />
            <p>Fetching categories...</p>
          </div>
        )}
        {categories && categories.length < 1 && (
          <div className="items-center justify-center flex-1 gap-4 w-full flex flex-col py-20">
            <h1 className="font-bold">
              No category found! Please add categories before adding products
            </h1>
            <Button
              onClick={() => {
                push("/account/categories");
              }}
              variant="primary"
            >
              Add category
            </Button>
          </div>
        )}
        {categories && categories.length > 0 && (
          <div className="p-8">
            <AddProduct />
          </div>
        )}
      </DashboardLayout>
    </>
  );
};

AddProductPage.hideFooter = true;
AddProductPage.hideNav = true;

export default isAdmin(AddProductPage);
