import axios from "axios";

export async function getServerSideProps({ req, res }) {
  const protocol = req.headers["x-forwarded-proto"] || "https"; // for proxy servers like Vercel
  const host = req.headers.host; // This gets the domain name (e.g., example.com)

  // Combine them to form the full base URL
  const baseUrl = `${protocol}://${host}`;
  // 🔥 Fetch your products from the DB
  const bestSellerProductsPromise = axios.get(
    `${process?.env?.NEXT_PUBLIC_BASE_URL}/products?best_seller=true`
  );
  const latestProductsPromise = axios.get(
    `${process?.env?.NEXT_PUBLIC_BASE_URL}/products?latest=true`
  );

  const [bestSellerProducts, latestProducts] = await Promise.all([
    bestSellerProductsPromise,
    latestProductsPromise
  ]);

  function removeDuplicateProducts(products, key = "id") {
    const seen = new Set();
    const uniqueProducts = [];

    for (const product of products) {
      const identifier = product[key];
      if (!seen.has(identifier)) {
        seen.add(identifier);
        uniqueProducts.push(product);
      }
    }

    return uniqueProducts;
  }

  const products = removeDuplicateProducts([
    ...(bestSellerProducts?.data?.data || []),
    ...(latestProducts?.data?.data || [])
  ]);

  const sitemap = `<?xml version="1.0" encoding="UTF-8"?>
      <urlset xmlns="http://www.sitemaps.org/schemas/sitemap/0.9">
      <url>
        <loc>${baseUrl}/</loc>
        <lastmod>2025-04-21T17:36:53+00:00</lastmod>
        <priority>1.00</priority>
        </url>
        <url>
        <loc>${baseUrl}/our-story</loc>
        <lastmod>2025-04-21T17:36:53+00:00</lastmod>
        <priority>0.80</priority>
        </url>
        <url>
        <loc>${baseUrl}/careers</loc>
        <lastmod>2025-04-21T17:36:53+00:00</lastmod>
        <priority>0.80</priority>
        </url>
        <url>
        <loc>${baseUrl}/contact</loc>
        <lastmod>2025-04-21T17:36:53+00:00</lastmod>
        <priority>0.80</priority>
        </url>
        <url>
        <loc>${baseUrl}/auth</loc>
        <lastmod>2025-04-21T17:36:53+00:00</lastmod>
        <priority>0.80</priority>
        </url>
        <url>
        <loc>${baseUrl}/auth/forgot-password</loc>
        <lastmod>2025-04-21T17:36:53+00:00</lastmod>
        <priority>0.64</priority>
       </url>
        ${products
          ?.map(
            (product) => `
          <url>
            <loc>${baseUrl}/product/${product?.id}</loc>
            <lastmod>${new Date(
              product.updatedAt || product.createdAt
            ).toISOString()}</lastmod>
            <changefreq>weekly</changefreq>
            <priority>0.80</priority>
          </url>`
          )
          .join("")}
      </urlset>`;

  res.setHeader("Content-Type", "text/xml");
  res.write(sitemap);
  res.end();

  return {
    props: {}
  };
}

export default function Sitemap() {
  // This page will not be rendered
  return <></>;
}
